#!/usr/bin/env python3
"""
创建管理员用户脚本
"""
import sqlite3
import hashlib
from passlib.context import CryptContext

# 密码加密
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def create_admin_user():
    """创建管理员用户"""
    try:
        # 连接数据库
        conn = sqlite3.connect('ar_system.db')
        cursor = conn.cursor()
        
        # 检查是否已有admin用户
        cursor.execute("SELECT id FROM ar_user WHERE username = 'admin'")
        if cursor.fetchone():
            print("❌ admin用户已存在")
            return
        
        # 创建权限
        permissions = [
            ("user:read", "查看用户", "user", "read"),
            ("user:write", "编辑用户", "user", "write"),
            ("user:delete", "删除用户", "user", "delete"),
            ("device:read", "查看设备", "device", "read"),
            ("device:write", "编辑设备", "device", "write"),
            ("device:delete", "删除设备", "device", "delete"),
            ("application:read", "查看应用", "application", "read"),
            ("application:write", "编辑应用", "application", "write"),
            ("application:delete", "删除应用", "application", "delete"),
            ("analytics:read", "查看分析", "analytics", "read"),
            ("ecommerce:read", "查看电商", "ecommerce", "read"),
            ("ecommerce:write", "编辑电商", "ecommerce", "write"),
            ("community:read", "查看社区", "community", "read"),
            ("community:write", "编辑社区", "community", "write"),
        ]
        
        # 插入权限
        for name, desc, resource, action in permissions:
            cursor.execute("""
                INSERT OR IGNORE INTO ar_permission (name, description, resource, action, created_at, updated_at)
                VALUES (?, ?, ?, ?, datetime('now'), datetime('now'))
            """, (name, desc, resource, action))
        
        # 创建管理员角色
        cursor.execute("""
            INSERT OR IGNORE INTO ar_role (name, display_name, description, is_active, created_at, updated_at)
            VALUES ('admin', '系统管理员', '拥有所有权限的系统管理员', 1, datetime('now'), datetime('now'))
        """)
        
        # 创建普通用户角色
        cursor.execute("""
            INSERT OR IGNORE INTO ar_role (name, display_name, description, is_active, created_at, updated_at)
            VALUES ('user', '普通用户', '普通用户角色', 1, datetime('now'), datetime('now'))
        """)
        
        # 获取角色ID
        cursor.execute("SELECT id FROM ar_role WHERE name = 'admin'")
        admin_role_id = cursor.fetchone()[0]
        
        # 给管理员角色分配所有权限
        cursor.execute("SELECT id FROM ar_permission")
        permission_ids = cursor.fetchall()
        
        for (perm_id,) in permission_ids:
            cursor.execute("""
                INSERT OR IGNORE INTO ar_role_permissions (role_id, permission_id)
                VALUES (?, ?)
            """, (admin_role_id, perm_id))
        
        # 创建管理员用户
        hashed_password = get_password_hash("123456")
        cursor.execute("""
            INSERT INTO ar_user (
                username, email, full_name, hashed_password,
                is_active, is_superuser, is_staff, is_verified,
                status, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        """, (
            "admin",
            "<EMAIL>",
            "系统管理员",
            hashed_password,
            1, 1, 1, 1, "active"
        ))
        
        # 获取用户ID
        user_id = cursor.lastrowid
        
        # 给用户分配管理员角色
        cursor.execute("""
            INSERT INTO ar_user_roles (user_id, role_id)
            VALUES (?, ?)
        """, (user_id, admin_role_id))
        
        # 提交事务
        conn.commit()
        
        print("✅ 管理员用户创建成功!")
        print("📧 用户名: admin")
        print("🔑 密码: 123456")
        print("🔐 权限: 超级管理员")
        
    except Exception as e:
        print(f"❌ 创建管理员用户失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    create_admin_user()
