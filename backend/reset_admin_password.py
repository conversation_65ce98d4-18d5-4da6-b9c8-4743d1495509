#!/usr/bin/env python3
"""
重置管理员密码
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import select, update
from app.core.database import AsyncSessionLocal
from app.models.user import User
from app.core.security import get_password_hash

async def reset_admin_password():
    """重置admin用户的密码为123456"""
    try:
        async with AsyncSessionLocal() as session:
            # 查找admin用户
            stmt = select(User).where(User.username == 'admin')
            result = await session.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user:
                print("❌ 管理员用户不存在")
                return
            
            # 生成新密码哈希
            new_password_hash = get_password_hash("123456")
            
            # 更新密码
            user.hashed_password = new_password_hash
            await session.commit()
            
            print("✅ 管理员密码已重置为: 123456")
            print(f"✅ 用户权限: is_superuser={user.is_superuser}")
            
    except Exception as e:
        print(f"❌ 重置密码失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(reset_admin_password())
