# 🚀 AR-System Backend API

AR-System 量子星核后端服务 - 基于 FastAPI 的现代化 API 服务

## 📋 功能特性

### 🔐 认证系统
- JWT 令牌认证
- 用户注册/登录/登出
- 角色权限管理
- 会话管理

### 👥 用户管理
- 用户CRUD操作
- 角色和权限管理
- 用户活动记录
- 个人资料管理

### 📱 设备管理
- 设备注册和监控
- 设备指标收集
- 设备日志管理
- 远程命令控制

### 📊 应用管理
- 应用发布和版本管理
- 应用评价系统
- 应用部署管理
- 应用分析统计

### 🛒 电商系统
- 商品管理
- 订单处理
- 支付集成
- 库存管理

### 💬 社区功能
- 帖子和回复
- 点赞和关注
- 通知系统
- 内容管理

### 📈 数据分析
- 系统指标监控
- 用户行为分析
- 性能监控
- 业务指标统计

## 🛠️ 技术栈

- **框架**: FastAPI 0.104.1
- **数据库**: PostgreSQL + SQLAlchemy 2.0
- **缓存**: Redis
- **认证**: JWT + Passlib
- **异步**: asyncio + asyncpg
- **验证**: Pydantic 2.0
- **文档**: 自动生成 OpenAPI 文档

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <your-repo-url>
cd backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 环境配置

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

主要配置项：
- `DATABASE_URL`: PostgreSQL 数据库连接
- `REDIS_URL`: Redis 连接
- `SECRET_KEY`: JWT 密钥
- `CORS_ORIGINS`: 允许的跨域来源

### 3. 数据库初始化

```bash
# 初始化数据库
python init_db.py

# 重置数据库（可选）
python init_db.py --reset
```

### 4. 启动服务

```bash
# 开发模式启动
python run.py

# 或使用 uvicorn 直接启动
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 5. 访问文档

启动后访问：
- API 文档: http://localhost:8000/docs
- ReDoc 文档: http://localhost:8000/redoc
- 健康检查: http://localhost:8000/health

## 🧪 API 测试

```bash
# 运行 API 测试
python test_api.py

# 指定测试参数
python test_api.py --url http://localhost:8000 --username testuser
```

## 📁 项目结构

```
backend/
├── app/                    # 应用主目录
│   ├── api/               # API 路由
│   │   └── v1/           # API v1 版本
│   │       ├── auth.py   # 认证路由
│   │       ├── users.py  # 用户管理
│   │       ├── devices.py # 设备管理
│   │       ├── applications.py # 应用管理
│   │       ├── analytics.py # 数据分析
│   │       ├── ecommerce.py # 电商管理
│   │       └── community.py # 社区管理
│   ├── core/              # 核心模块
│   │   ├── config.py     # 配置管理
│   │   ├── database.py   # 数据库配置
│   │   └── security.py   # 安全认证
│   ├── models/            # 数据模型
│   │   ├── user.py       # 用户模型
│   │   ├── device.py     # 设备模型
│   │   ├── application.py # 应用模型
│   │   ├── ecommerce.py  # 电商模型
│   │   ├── community.py  # 社区模型
│   │   ├── analytics.py  # 分析模型
│   │   └── system.py     # 系统模型
│   └── schemas/           # Pydantic 模式
│       ├── auth.py       # 认证模式
│       └── device.py     # 设备模式
├── main.py               # 应用入口
├── run.py               # 启动脚本
├── init_db.py           # 数据库初始化
├── test_api.py          # API 测试
├── requirements.txt     # 依赖列表
├── .env.example        # 环境配置示例
└── README.md           # 说明文档
```

## 🔌 API 端点

### 认证相关
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/refresh` - 刷新令牌
- `POST /api/v1/auth/logout` - 用户登出
- `GET /api/v1/auth/me` - 获取当前用户信息

### 用户管理
- `GET /api/v1/users/` - 获取用户列表
- `GET /api/v1/users/{user_id}` - 获取用户详情
- `POST /api/v1/users/` - 创建用户
- `PUT /api/v1/users/{user_id}` - 更新用户
- `DELETE /api/v1/users/{user_id}` - 删除用户

### 设备管理
- `GET /api/v1/devices/` - 获取设备列表
- `GET /api/v1/devices/{device_id}` - 获取设备详情
- `POST /api/v1/devices/` - 注册设备
- `PUT /api/v1/devices/{device_id}` - 更新设备
- `DELETE /api/v1/devices/{device_id}` - 删除设备

### 更多端点
详见 API 文档: http://localhost:8000/docs

## 🐳 Docker 部署

```bash
# 构建镜像
docker build -t ar-system-backend .

# 运行容器
docker run -d \
  --name ar-system-backend \
  -p 8000:8000 \
  -e DATABASE_URL=********************************/db \
  -e REDIS_URL=redis://host:6379 \
  ar-system-backend
```

## 🔧 开发指南

### 添加新的 API 端点

1. 在 `app/models/` 中定义数据模型
2. 在 `app/schemas/` 中定义 Pydantic 模式
3. 在 `app/api/v1/` 中创建路由文件
4. 在 `main.py` 中注册路由

### 数据库迁移

```bash
# 生成迁移文件
alembic revision --autogenerate -m "描述"

# 执行迁移
alembic upgrade head

# 回滚迁移
alembic downgrade -1
```

## 🧪 测试

```bash
# 运行测试
pytest

# 运行测试并生成覆盖率报告
pytest --cov=app --cov-report=html
```

## 📝 日志

日志文件位置：
- 应用日志: `logs/app.log`
- 错误日志: `logs/error.log`
- 访问日志: `logs/access.log`

## 🔒 安全注意事项

1. 修改默认的 `SECRET_KEY`
2. 使用强密码策略
3. 启用 HTTPS
4. 定期更新依赖包
5. 配置防火墙规则

## 📞 支持

如有问题，请联系：
- 技术支持: [您的邮箱]
- 项目地址: [GitHub 链接]
- 文档地址: [文档链接]
