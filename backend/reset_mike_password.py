#!/usr/bin/env python3
"""
重置用户mike的密码为1234
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import select, update
from app.core.database import Async<PERSON>essionLocal
from app.models.user import User
from app.core.security import get_password_hash

async def reset_mike_password():
    """重置mike用户的密码"""
    try:
        async with AsyncSessionLocal() as session:
            # 查找mike用户
            stmt = select(User).where(User.username == 'mike')
            result = await session.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user:
                print("❌ 用户mike不存在")
                return
            
            # 生成新密码哈希
            new_password_hash = get_password_hash("1234")
            
            # 更新密码
            user.hashed_password = new_password_hash
            await session.commit()
            
            print("✅ 用户mike的密码已重置为: 1234")
            print(f"✅ 用户权限: is_superuser={user.is_superuser}, is_staff={user.is_staff}")
            
    except Exception as e:
        print(f"❌ 重置密码失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(reset_mike_password())
