# FastAPI 核心框架
fastapi==0.104.1
uvicorn[standard]==0.24.0

# 数据库相关
sqlalchemy==2.0.23
aiosqlite==0.19.0  # SQLite 异步驱动
asyncpg==0.29.0  # PostgreSQL 异步驱动
psycopg2-binary==2.9.9  # PostgreSQL 同步驱动
alembic==1.13.0  # 数据库迁移

# Redis 缓存
redis==5.0.1
aioredis==2.0.1  # Redis 异步客户端

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# 数据验证和配置
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# HTTP 客户端和文件处理
httpx==0.25.2
aiohttp==3.9.1
aiofiles==23.2.1
Pillow==10.1.0

# 任务队列
celery==5.3.4
kombu==5.3.4

# 工具和配置
python-dotenv==1.0.0
python-dateutil==2.8.2
click==8.1.7
rich==13.7.0

# 开发和测试工具
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
isort==5.12.0
flake8==6.1.0
jinja2==3.1.2
email-validator==2.1.0
