#!/usr/bin/env python3
"""
AR-System Backend 启动脚本
"""
import uvicorn
import os
from app.core.config import settings

if __name__ == "__main__":
    # 设置环境变量
    os.environ.setdefault("NODE_ENV", "development")
    
    # 启动服务器
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True
    )
