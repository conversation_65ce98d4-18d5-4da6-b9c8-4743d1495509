from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from contextlib import asynccontextmanager
import os
import uvicorn
from datetime import datetime, timezone

# 导入路由模块
from app.api.v1 import auth, users, devices, applications, analytics, ecommerce, community, permissions
from app.core.config import settings
from app.core.database import init_db
from app.core.security import verify_token

# 应用生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时初始化数据库
    await init_db()
    print("🚀 AR-System Backend API 启动完成")
    yield
    # 关闭时清理资源
    print("🔄 AR-System Backend API 正在关闭")

app = FastAPI(
    title="AR-System Backend API",
    description="Backend API for AR-System quantum core project - 量子星核后端服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 安全认证
security = HTTPBearer()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户"""
    try:
        payload = verify_token(credentials.credentials)
        return payload
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

# 基础路由
@app.get("/")
async def read_root():
    return {
        "message": "AR-System Backend API",
        "status": "running",
        "version": "1.0.0",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "ar-system-backend",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "version": "1.0.0"
    }

# API路由注册
app.include_router(auth.router, prefix="/api/v1/auth", tags=["认证"])
app.include_router(users.router, prefix="/api/v1/users", tags=["用户管理"])
app.include_router(permissions.router, prefix="/api/v1/permissions", tags=["权限管理"])
app.include_router(devices.router, prefix="/api/v1/devices", tags=["设备管理"])
app.include_router(applications.router, prefix="/api/v1/applications", tags=["应用管理"])
app.include_router(analytics.router, prefix="/api/v1/analytics", tags=["数据分析"])
app.include_router(ecommerce.router, prefix="/api/v1/ecommerce", tags=["电商管理"])
app.include_router(community.router, prefix="/api/v1/community", tags=["社区管理"])

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
