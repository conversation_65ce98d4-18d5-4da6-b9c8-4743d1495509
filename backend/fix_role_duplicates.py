#!/usr/bin/env python3
"""
修复角色重复问题，建立清晰的角色层次结构
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import select, delete
from app.core.database import AsyncSessionLocal
from app.models.user import Role, User

async def fix_role_duplicates():
    """修复角色重复问题"""
    try:
        async with AsyncSessionLocal() as session:
            print("🔍 检查当前角色状态...")
            
            # 查询所有角色
            stmt = select(Role).order_by(Role.id)
            result = await session.execute(stmt)
            all_roles = result.scalars().all()
            
            print(f"当前角色数量: {len(all_roles)}")
            print("=" * 60)
            for role in all_roles:
                print(f"ID: {role.id:2d} | {role.name:15s} | {role.display_name}")
            print("=" * 60)
            
            # 删除重复和不需要的角色
            roles_to_delete = ['system_admin', 'super_admin']  # 删除重复的角色
            
            for role_name in roles_to_delete:
                print(f"\n🗑️ 删除重复角色: {role_name}")
                
                # 检查是否有用户使用这个角色
                role_stmt = select(Role).where(Role.name == role_name)
                role_result = await session.execute(role_stmt)
                role_to_delete = role_result.scalar_one_or_none()
                
                if role_to_delete:
                    # 检查是否有用户关联到这个角色
                    # 注意：这里需要根据实际的用户-角色关联表来检查
                    print(f"   删除角色: {role_to_delete.display_name}")
                    await session.delete(role_to_delete)
                else:
                    print(f"   角色 {role_name} 不存在，跳过")
            
            # 更新admin角色为超级管理员
            admin_stmt = select(Role).where(Role.name == 'admin')
            admin_result = await session.execute(admin_stmt)
            admin_role = admin_result.scalar_one_or_none()
            
            if admin_role:
                print(f"\n📝 更新admin角色定义...")
                admin_role.display_name = "超级管理员"
                admin_role.description = "拥有系统所有权限的最高级别管理员，系统中只能有一个"
                admin_role.is_system = True
                print(f"   ✅ admin角色已更新为: {admin_role.display_name}")
            
            # 提交更改
            await session.commit()
            
            # 验证最终结果
            print(f"\n📊 角色清理完成，最终结果:")
            print("=" * 60)
            
            final_stmt = select(Role).order_by(Role.id)
            final_result = await session.execute(final_stmt)
            final_roles = final_result.scalars().all()
            
            # 定义清晰的角色层次
            role_hierarchy = {
                'admin': {'level': 10, 'category': '系统级'},
                'manager': {'level': 6, 'category': '管理级'},
                'developer': {'level': 5, 'category': '专业级'},
                'operator': {'level': 3, 'category': '操作级'},
                'user': {'level': 1, 'category': '基础级'}
            }
            
            print(f"角色总数: {len(final_roles)}")
            print(f"{'ID':>2} | {'角色名':15s} | {'显示名':12s} | {'级别':4s} | {'分类':8s} | 描述")
            print("-" * 80)
            
            for role in final_roles:
                hierarchy = role_hierarchy.get(role.name, {'level': 0, 'category': '未知'})
                print(f"{role.id:2d} | {role.name:15s} | {role.display_name:12s} | {hierarchy['level']:4d} | {hierarchy['category']:8s} | {role.description}")
            
            print("\n🎉 角色层次结构优化完成！")
            
            # 显示推荐的角色使用指南
            print(f"\n📋 角色使用指南:")
            print("=" * 60)
            print("🔴 超级管理员 (admin)     - 系统最高权限，只能有一个")
            print("🟠 部门经理 (manager)     - 部门级管理权限")
            print("🟡 开发者 (developer)     - 开发工具和API权限")
            print("🟢 操作员 (operator)      - 基本操作权限")
            print("🔵 普通用户 (user)        - 基础功能权限")
            
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(fix_role_duplicates())
