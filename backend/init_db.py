#!/usr/bin/env python3
"""
AR-System 数据库初始化脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.database import init_db, DatabaseMigration
from app.core.config import settings


async def main():
    """主函数"""
    print("🚀 AR-System 数据库初始化开始...")
    print(f"📊 数据库URL: {settings.DATABASE_URL}")
    
    try:
        # 初始化数据库
        await init_db()
        print("✅ 数据库初始化完成")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        sys.exit(1)


async def reset_db():
    """重置数据库"""
    print("⚠️  警告: 即将重置数据库，所有数据将被删除！")
    confirm = input("请输入 'yes' 确认重置: ")
    
    if confirm.lower() != 'yes':
        print("❌ 操作已取消")
        return
    
    try:
        print("🔄 正在重置数据库...")
        await DatabaseMigration.reset()
        print("✅ 数据库重置完成")
        
    except Exception as e:
        print(f"❌ 数据库重置失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="AR-System 数据库管理工具")
    parser.add_argument(
        "--reset", 
        action="store_true", 
        help="重置数据库（删除所有数据）"
    )
    
    args = parser.parse_args()
    
    if args.reset:
        asyncio.run(reset_db())
    else:
        asyncio.run(main())
