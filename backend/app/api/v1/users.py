"""
AR-System 用户管理API路由
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query, Body
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload
from typing import Optional, List

from app.core.database import get_async_session
from app.core.security import verify_token, get_password_hash
from app.models.user import User, Role, UserActivity
from app.schemas.auth import (
    UserResponse, UserDetailResponse, UserCreateRequest, 
    UserUpdateRequest, UserListResponse, ActivityResponse
)

router = APIRouter()
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_session)
) -> User:
    """获取当前用户依赖"""
    payload = verify_token(credentials.credentials)
    user_id = payload.get("user_id")
    
    stmt = select(User).where(User.id == user_id)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return user


async def require_admin(current_user: User = Depends(get_current_user)):
    """需要管理员权限的依赖"""
    if not current_user.is_superuser and not current_user.has_role("admin"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user


@router.get("/", response_model=UserListResponse, summary="获取用户列表")
async def get_users(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    is_superuser: Optional[bool] = Query(None, description="是否超级用户"),
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_async_session)
):
    """
    获取用户列表
    
    需要管理员权限
    """
    # 构建查询条件
    conditions = []
    
    if search:
        search_pattern = f"%{search}%"
        conditions.append(
            or_(
                User.username.ilike(search_pattern),
                User.email.ilike(search_pattern),
                User.full_name.ilike(search_pattern)
            )
        )
    
    if is_active is not None:
        conditions.append(User.is_active == is_active)
    
    if is_superuser is not None:
        conditions.append(User.is_superuser == is_superuser)
    
    # 查询总数
    count_stmt = select(func.count(User.id))
    if conditions:
        count_stmt = count_stmt.where(and_(*conditions))
    
    count_result = await db.execute(count_stmt)
    total = count_result.scalar()
    
    # 查询用户列表（包含角色和权限信息）
    stmt = select(User).options(
        selectinload(User.roles).selectinload(Role.permissions)
    )
    if conditions:
        stmt = stmt.where(and_(*conditions))

    stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    stmt = stmt.order_by(User.created_at.desc())
    
    result = await db.execute(stmt)
    users = result.scalars().all()
    
    return UserListResponse(
        users=[UserResponse.model_validate(user) for user in users],
        total=total,
        page=page,
        page_size=page_size,
        total_pages=(total + page_size - 1) // page_size
    )


@router.get("/{user_id}", response_model=UserDetailResponse, summary="获取用户详情")
async def get_user(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """
    获取用户详情
    
    - 用户只能查看自己的详情
    - 管理员可以查看所有用户详情
    """
    # 权限检查
    if user_id != current_user.id and not current_user.is_superuser and not current_user.has_role("admin"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限查看该用户信息"
        )
    
    stmt = select(User).options(
        selectinload(User.roles).selectinload(Role.permissions)
    ).where(User.id == user_id)
    
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 构建响应数据
    user_data = UserDetailResponse.from_orm(user)
    user_data.permissions = user.get_permissions()
    
    return user_data


@router.post("/", response_model=UserResponse, summary="创建用户")
async def create_user(
    request: UserCreateRequest,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_async_session)
):
    """
    创建新用户
    
    需要管理员权限
    """
    # 检查用户名是否已存在
    stmt = select(User).where(User.username == request.username)
    result = await db.execute(stmt)
    if result.scalar_one_or_none():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )
    
    # 检查邮箱是否已存在
    stmt = select(User).where(User.email == request.email)
    result = await db.execute(stmt)
    if result.scalar_one_or_none():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已存在"
        )
    
    # 创建用户
    user = User(
        username=request.username,
        email=request.email,
        full_name=request.full_name,
        hashed_password=get_password_hash(request.password),
        is_active=request.is_active,
        is_superuser=request.is_superuser
    )
    
    # 分配角色
    if request.role_ids:
        stmt = select(Role).where(Role.id.in_(request.role_ids))
        result = await db.execute(stmt)
        roles = result.scalars().all()
        user.roles = roles
    
    db.add(user)
    await db.commit()
    await db.refresh(user)
    
    # 记录活动
    activity = UserActivity(
        user_id=current_user.id,
        action="create_user",
        resource="user",
        resource_id=str(user.id),
        details=f"创建用户: {user.username}"
    )
    db.add(activity)
    await db.commit()
    
    return UserResponse.from_orm(user)


@router.put("/{user_id}", response_model=UserResponse, summary="更新用户")
async def update_user(
    user_id: int,
    request: UserUpdateRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """
    更新用户信息
    
    - 用户可以更新自己的基本信息
    - 管理员可以更新所有用户信息
    """
    # 查找用户
    stmt = select(User).where(User.id == user_id)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 权限检查
    is_admin = current_user.is_superuser or current_user.has_role("admin")
    is_self = user_id == current_user.id
    
    if not is_admin and not is_self:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限修改该用户信息"
        )
    
    # 更新字段
    update_data = request.dict(exclude_unset=True)
    
    # 非管理员不能修改某些字段
    if not is_admin:
        restricted_fields = ["is_active", "role_ids"]
        for field in restricted_fields:
            update_data.pop(field, None)
    
    # 检查邮箱唯一性
    if "email" in update_data:
        stmt = select(User).where(User.email == update_data["email"], User.id != user_id)
        result = await db.execute(stmt)
        if result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在"
            )
    
    # 更新角色
    if "role_ids" in update_data and is_admin:
        role_ids = update_data.pop("role_ids")
        if role_ids:
            stmt = select(Role).where(Role.id.in_(role_ids))
            result = await db.execute(stmt)
            roles = result.scalars().all()
            user.roles = roles
        else:
            user.roles = []
    
    # 更新其他字段
    for field, value in update_data.items():
        setattr(user, field, value)
    
    await db.commit()
    await db.refresh(user)
    
    # 记录活动
    activity = UserActivity(
        user_id=current_user.id,
        action="update_user",
        resource="user",
        resource_id=str(user.id),
        details=f"更新用户: {user.username}"
    )
    db.add(activity)
    await db.commit()
    
    return UserResponse.from_orm(user)


@router.delete("/{user_id}", summary="删除用户")
async def delete_user(
    user_id: int,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_async_session)
):
    """
    删除用户
    
    需要管理员权限
    """
    # 查找用户
    stmt = select(User).where(User.id == user_id)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 不能删除自己
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除自己"
        )
    
    # 软删除（设置为非激活状态）
    user.is_active = False
    await db.commit()
    
    # 记录活动
    activity = UserActivity(
        user_id=current_user.id,
        action="delete_user",
        resource="user",
        resource_id=str(user.id),
        details=f"删除用户: {user.username}"
    )
    db.add(activity)
    await db.commit()
    
    return {"message": "用户删除成功"}


@router.get("/{user_id}/activities", response_model=List[ActivityResponse], summary="获取用户活动记录")
async def get_user_activities(
    user_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """
    获取用户活动记录
    
    - 用户只能查看自己的活动记录
    - 管理员可以查看所有用户的活动记录
    """
    # 权限检查
    if user_id != current_user.id and not current_user.is_superuser and not current_user.has_role("admin"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限查看该用户活动记录"
        )
    
    stmt = select(UserActivity).where(UserActivity.user_id == user_id)
    stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    stmt = stmt.order_by(UserActivity.created_at.desc())
    
    result = await db.execute(stmt)
    activities = result.scalars().all()
    
    return [ActivityResponse.from_orm(activity) for activity in activities]

@router.post("/{user_id}/reset-password")
async def reset_user_password(
    user_id: int,
    new_password: str = Body(..., embed=True),
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_async_session)
):
    """重置用户密码"""
    # 查找用户
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()

    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")

    # 不能重置自己的密码（应该通过修改密码接口）
    if user.id == current_user.id:
        raise HTTPException(status_code=400, detail="不能重置自己的密码，请使用修改密码功能")

    # 更新密码
    user.hashed_password = get_password_hash(new_password)
    await db.commit()

    # 记录活动
    activity = UserActivity(
        user_id=current_user.id,
        action="reset_password",
        resource="user",
        resource_id=str(user.id),
        details=f"重置用户密码: {user.username}"
    )
    db.add(activity)
    await db.commit()

    return {"message": "密码重置成功"}
