"""
权限管理API路由 - 简化版本
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload
from typing import Optional, Dict, Any

from app.core.database import get_async_session
from app.models.user import User, Role, Permission, UserActivity, user_roles

router = APIRouter()

# 导入get_current_user函数
from app.api.v1.users import get_current_user

async def require_admin(current_user: User = Depends(get_current_user)):
    """需要管理员权限的依赖"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user


# ==================== 权限管理 ====================

@router.get("/permissions")
async def get_permissions(
    page: int = Query(1, ge=1),
    page_size: int = Query(50, ge=1, le=100),
    search: Optional[str] = Query(None),
    module: Optional[str] = Query(None),
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_async_session)
):
    """获取权限列表"""
    stmt = select(Permission)

    if search:
        search_pattern = f"%{search}%"
        stmt = stmt.where(
            Permission.name.ilike(search_pattern) |
            Permission.display_name.ilike(search_pattern)
        )

    if module:
        stmt = stmt.where(Permission.module == module)

    stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    stmt = stmt.order_by(Permission.name)

    result = await db.execute(stmt)
    permissions = result.scalars().all()

    return [
        {
            "id": perm.id,
            "name": perm.name,
            "display_name": perm.display_name,
            "description": perm.description,
            "resource": perm.resource,
            "action": perm.action,
            "module": perm.module,
            "created_at": perm.created_at.isoformat() if perm.created_at else None,
            "updated_at": perm.updated_at.isoformat() if perm.updated_at else None
        }
        for perm in permissions
    ]


@router.post("/permissions")
async def create_permission(
    permission_data: Dict[str, Any],
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_async_session)
):
    """创建权限"""
    # 检查权限名称是否已存在
    stmt = select(Permission).where(Permission.name == permission_data.get("name"))
    result = await db.execute(stmt)
    if result.scalar_one_or_none():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="权限名称已存在"
        )

    permission = Permission(
        name=permission_data.get("name"),
        display_name=permission_data.get("display_name"),
        description=permission_data.get("description"),
        resource=permission_data.get("resource"),
        action=permission_data.get("action"),
        module=permission_data.get("module")
    )
    db.add(permission)
    await db.commit()
    await db.refresh(permission)

    # 记录活动
    activity = UserActivity(
        user_id=current_user.id,
        action="create_permission",
        resource="permission",
        resource_id=str(permission.id),
        details=f"创建权限: {permission.name}"
    )
    db.add(activity)
    await db.commit()

    return {
        "id": permission.id,
        "name": permission.name,
        "display_name": permission.display_name,
        "description": permission.description,
        "resource": permission.resource,
        "action": permission.action,
        "module": permission.module,
        "created_at": permission.created_at.isoformat() if permission.created_at else None
    }


@router.delete("/permissions/{permission_id}")
async def delete_permission(
    permission_id: int,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_async_session)
):
    """删除权限"""
    stmt = select(Permission).where(Permission.id == permission_id)
    result = await db.execute(stmt)
    permission = result.scalar_one_or_none()

    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="权限不存在"
        )

    await db.delete(permission)
    await db.commit()

    # 记录活动
    activity = UserActivity(
        user_id=current_user.id,
        action="delete_permission",
        resource="permission",
        resource_id=str(permission.id),
        details=f"删除权限: {permission.name}"
    )
    db.add(activity)
    await db.commit()

    return {"message": "权限删除成功"}


# ==================== 角色管理 ====================

@router.get("/roles")
async def get_roles(
    page: int = Query(1, ge=1),
    page_size: int = Query(50, ge=1, le=100),
    search: Optional[str] = Query(None),
    include_system: bool = Query(True),
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_async_session)
):
    """获取角色列表"""
    stmt = select(Role).options(selectinload(Role.permissions))

    if search:
        search_pattern = f"%{search}%"
        stmt = stmt.where(
            Role.name.ilike(search_pattern) |
            Role.display_name.ilike(search_pattern)
        )

    if not include_system:
        stmt = stmt.where(Role.is_system == False)

    stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    stmt = stmt.order_by(Role.is_system.desc(), Role.name)

    result = await db.execute(stmt)
    roles = result.scalars().all()

    return [
        {
            "id": role.id,
            "name": role.name,
            "display_name": role.display_name,
            "description": role.description,
            "is_system": role.is_system,
            "permissions": [
                {
                    "id": perm.id,
                    "name": perm.name,
                    "display_name": perm.display_name
                }
                for perm in role.permissions
            ],
            "user_count": 0,  # TODO: 计算用户数量
            "created_at": role.created_at.isoformat() if role.created_at else None
        }
        for role in roles
    ]


@router.post("/roles")
async def create_role(
    role_data: Dict[str, Any],
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_async_session)
):
    """创建角色"""
    # 检查角色名称是否已存在
    stmt = select(Role).where(Role.name == role_data.get("name"))
    result = await db.execute(stmt)
    if result.scalar_one_or_none():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="角色名称已存在"
        )

    role = Role(
        name=role_data.get("name"),
        display_name=role_data.get("display_name"),
        description=role_data.get("description"),
        is_system=role_data.get("is_system", False)
    )

    # 分配权限
    permission_ids = role_data.get("permission_ids", [])
    if permission_ids:
        stmt = select(Permission).where(Permission.id.in_(permission_ids))
        result = await db.execute(stmt)
        permissions = result.scalars().all()
        role.permissions = permissions

    db.add(role)
    await db.commit()
    await db.refresh(role)

    # 记录活动
    activity = UserActivity(
        user_id=current_user.id,
        action="create_role",
        resource="role",
        resource_id=str(role.id),
        details=f"创建角色: {role.name}"
    )
    db.add(activity)
    await db.commit()

    return {
        "id": role.id,
        "name": role.name,
        "display_name": role.display_name,
        "description": role.description,
        "is_system": role.is_system,
        "created_at": role.created_at.isoformat() if role.created_at else None
    }


@router.delete("/roles/{role_id}")
async def delete_role(
    role_id: int,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_async_session)
):
    """删除角色"""
    stmt = select(Role).where(Role.id == role_id)
    result = await db.execute(stmt)
    role = result.scalar_one_or_none()

    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在"
        )

    # 系统角色不能删除
    if role.is_system:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="系统角色不能删除"
        )

    await db.delete(role)
    await db.commit()

    # 记录活动
    activity = UserActivity(
        user_id=current_user.id,
        action="delete_role",
        resource="role",
        resource_id=str(role.id),
        details=f"删除角色: {role.name}"
    )
    db.add(activity)
    await db.commit()

    return {"message": "角色删除成功"}


# ==================== 用户权限管理 ====================

@router.get("/users/{user_id}/permissions")
async def get_user_permissions(
    user_id: int,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_async_session)
):
    """获取用户权限详情"""
    stmt = select(User).options(
        selectinload(User.roles).selectinload(Role.permissions)
    ).where(User.id == user_id)

    result = await db.execute(stmt)
    user = result.scalar_one_or_none()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 收集所有权限
    all_permissions = set()
    for role in user.roles:
        for permission in role.permissions:
            all_permissions.add(permission)

    return {
        "user_id": user.id,
        "username": user.username,
        "full_name": user.full_name,
        "is_superuser": user.is_superuser,
        "roles": [
            {
                "id": role.id,
                "name": role.name,
                "display_name": role.display_name,
                "is_system": role.is_system
            }
            for role in user.roles
        ],
        "permissions": [
            {
                "id": perm.id,
                "name": perm.name,
                "display_name": perm.display_name,
                "resource": perm.resource,
                "action": perm.action,
                "module": perm.module
            }
            for perm in all_permissions
        ]
    }


@router.post("/users/{user_id}/roles")
async def assign_user_roles(
    user_id: int,
    role_data: Dict[str, Any],
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_async_session)
):
    """分配用户角色"""
    # 查找用户
    stmt = select(User).where(User.id == user_id)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 获取角色
    role_ids = role_data.get("role_ids", [])
    action = role_data.get("action", "replace")  # add, remove, replace

    if action == "replace":
        # 替换所有角色
        if role_ids:
            stmt = select(Role).where(Role.id.in_(role_ids))
            result = await db.execute(stmt)
            roles = result.scalars().all()

            # 先清空现有角色，然后添加新角色
            await db.execute(
                user_roles.delete().where(user_roles.c.user_id == user.id)
            )

            # 添加新角色关联
            for role in roles:
                await db.execute(
                    user_roles.insert().values(user_id=user.id, role_id=role.id)
                )
        else:
            # 清空所有角色
            await db.execute(
                user_roles.delete().where(user_roles.c.user_id == user.id)
            )
    elif action == "add":
        # 添加角色
        if role_ids:
            # 获取用户现有角色
            existing_stmt = select(user_roles.c.role_id).where(user_roles.c.user_id == user.id)
            existing_result = await db.execute(existing_stmt)
            existing_role_ids = {row[0] for row in existing_result.fetchall()}

            # 添加新角色
            for role_id in role_ids:
                if role_id not in existing_role_ids:
                    await db.execute(
                        user_roles.insert().values(user_id=user.id, role_id=role_id)
                    )
    elif action == "remove":
        # 移除角色
        if role_ids:
            await db.execute(
                user_roles.delete().where(
                    (user_roles.c.user_id == user.id) &
                    (user_roles.c.role_id.in_(role_ids))
                )
            )

    await db.commit()

    # 记录活动
    activity = UserActivity(
        user_id=current_user.id,
        action="assign_user_roles",
        resource="user",
        resource_id=str(user.id),
        details=f"修改用户 {user.username} 的角色分配"
    )
    db.add(activity)
    await db.commit()

    return {"message": "用户角色分配成功"}


@router.put("/users/{user_id}/superuser")
async def update_user_superuser_status(
    user_id: int,
    status_data: Dict[str, Any],
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_async_session)
):
    """更新用户超级管理员状态"""
    # 查找用户
    stmt = select(User).where(User.id == user_id)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 不能修改自己的超级管理员状态
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能修改自己的超级管理员状态"
        )

    is_superuser = status_data.get("is_superuser", False)

    # 🔒 超级管理员唯一性检查
    if is_superuser:
        # 检查系统中是否已经有超级管理员
        existing_superuser_stmt = select(User).where(
            (User.is_superuser == True) & (User.id != user_id)
        )
        existing_result = await db.execute(existing_superuser_stmt)
        existing_superuser = existing_result.scalar_one_or_none()

        if existing_superuser:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"系统只能有一个超级管理员。当前超级管理员是: {existing_superuser.username}"
            )

    # 🔒 防止移除最后一个超级管理员
    if not is_superuser and user.is_superuser:
        # 检查这是否是最后一个超级管理员
        superuser_count_stmt = select(User).where(User.is_superuser == True)
        superuser_result = await db.execute(superuser_count_stmt)
        superuser_count = len(superuser_result.scalars().all())

        if superuser_count <= 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能移除最后一个超级管理员，系统必须至少有一个超级管理员"
            )

    user.is_superuser = is_superuser

    await db.commit()

    # 记录活动
    activity = UserActivity(
        user_id=current_user.id,
        action="update_superuser_status",
        resource="user",
        resource_id=str(user.id),
        details=f"{'设置' if is_superuser else '取消'} {user.username} 的超级管理员权限"
    )
    db.add(activity)
    await db.commit()

    return {"message": "用户权限更新成功"}


