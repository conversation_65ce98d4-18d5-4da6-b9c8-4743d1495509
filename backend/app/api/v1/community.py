"""
AR-System 社区管理API路由
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload
from typing import Optional, List

from app.core.database import get_async_session
from app.core.security import verify_token
from app.models.user import User
from app.models.community import Post, Category, Tag, Reply, PostLike, Notification, CommunityReport

router = APIRouter()
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_session)
) -> User:
    """获取当前用户依赖"""
    payload = verify_token(credentials.credentials)
    user_id = payload.get("user_id")
    
    stmt = select(User).where(User.id == user_id)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return user


@router.get("/posts", summary="获取帖子列表")
async def get_posts(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    category_id: Optional[int] = Query(None, description="分类ID"),
    post_type: Optional[str] = Query(None, description="帖子类型"),
    is_pinned: Optional[bool] = Query(None, description="是否置顶"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """获取帖子列表"""
    # 构建查询条件
    conditions = [Post.is_active == True]
    
    if search:
        search_pattern = f"%{search}%"
        conditions.append(
            or_(
                Post.title.ilike(search_pattern),
                Post.content.ilike(search_pattern)
            )
        )
    
    if category_id:
        conditions.append(Post.category_id == category_id)
    
    if post_type:
        conditions.append(Post.post_type == post_type)
    
    if is_pinned is not None:
        conditions.append(Post.is_pinned == is_pinned)
    
    # 查询总数
    count_stmt = select(func.count(Post.id)).where(and_(*conditions))
    count_result = await db.execute(count_stmt)
    total = count_result.scalar()
    
    # 查询帖子列表
    stmt = select(Post).options(
        selectinload(Post.author),
        selectinload(Post.category),
        selectinload(Post.tags)
    )
    stmt = stmt.where(and_(*conditions))
    stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    
    # 排序：置顶帖子优先，然后按最后活动时间排序
    stmt = stmt.order_by(
        Post.is_pinned.desc(),
        Post.last_activity_at.desc()
    )
    
    result = await db.execute(stmt)
    posts = result.scalars().all()
    
    return {
        "posts": posts,
        "total": total,
        "page": page,
        "page_size": page_size,
        "total_pages": (total + page_size - 1) // page_size
    }


@router.get("/posts/{post_id}", summary="获取帖子详情")
async def get_post(
    post_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """获取帖子详情"""
    stmt = select(Post).options(
        selectinload(Post.author),
        selectinload(Post.category),
        selectinload(Post.tags),
        selectinload(Post.replies).selectinload(Reply.author)
    ).where(Post.id == post_id, Post.is_active == True)
    
    result = await db.execute(stmt)
    post = result.scalar_one_or_none()
    
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="帖子不存在"
        )
    
    # 增加浏览次数
    post.increment_view_count()
    await db.commit()
    
    return post


@router.post("/posts", summary="创建帖子")
async def create_post(
    # request: PostCreateRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """创建新帖子"""
    # TODO: 实现帖子创建逻辑
    return {"message": "帖子创建功能待实现"}


@router.put("/posts/{post_id}", summary="更新帖子")
async def update_post(
    post_id: int,
    # request: PostUpdateRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """更新帖子"""
    # TODO: 实现帖子更新逻辑
    return {"message": "帖子更新功能待实现"}


@router.delete("/posts/{post_id}", summary="删除帖子")
async def delete_post(
    post_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """删除帖子"""
    # TODO: 实现帖子删除逻辑
    return {"message": "帖子删除功能待实现"}


@router.post("/posts/{post_id}/like", summary="点赞帖子")
async def like_post(
    post_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """点赞或取消点赞帖子"""
    # 检查帖子是否存在
    stmt = select(Post).where(Post.id == post_id, Post.is_active == True)
    result = await db.execute(stmt)
    post = result.scalar_one_or_none()
    
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="帖子不存在"
        )
    
    # 检查是否已经点赞
    like_stmt = select(PostLike).where(
        PostLike.post_id == post_id,
        PostLike.user_id == current_user.id
    )
    like_result = await db.execute(like_stmt)
    existing_like = like_result.scalar_one_or_none()
    
    if existing_like:
        # 取消点赞
        await db.delete(existing_like)
        post.like_count = max(0, post.like_count - 1)
        action = "unliked"
    else:
        # 添加点赞
        like = PostLike(post_id=post_id, user_id=current_user.id)
        db.add(like)
        post.like_count += 1
        action = "liked"
    
    await db.commit()
    
    return {
        "action": action,
        "like_count": post.like_count
    }


@router.get("/posts/{post_id}/replies", summary="获取帖子回复")
async def get_post_replies(
    post_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """获取帖子回复列表"""
    # 检查帖子是否存在
    post_stmt = select(Post).where(Post.id == post_id, Post.is_active == True)
    post_result = await db.execute(post_stmt)
    post = post_result.scalar_one_or_none()
    
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="帖子不存在"
        )
    
    # 查询回复
    stmt = select(Reply).options(selectinload(Reply.author))
    stmt = stmt.where(Reply.post_id == post_id, Reply.is_active == True)
    stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    stmt = stmt.order_by(Reply.created_at.asc())
    
    result = await db.execute(stmt)
    replies = result.scalars().all()
    
    return replies


@router.post("/posts/{post_id}/replies", summary="回复帖子")
async def create_reply(
    post_id: int,
    # request: ReplyCreateRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """回复帖子"""
    # TODO: 实现回复创建逻辑
    return {"message": "回复功能待实现"}


@router.get("/categories", summary="获取分类列表")
async def get_categories(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """获取社区分类列表"""
    stmt = select(Category).where(Category.is_active == True)
    stmt = stmt.order_by(Category.sort_order, Category.name)
    
    result = await db.execute(stmt)
    categories = result.scalars().all()
    
    return categories


@router.get("/tags", summary="获取标签列表")
async def get_tags(
    search: Optional[str] = Query(None, description="搜索关键词"),
    limit: int = Query(20, ge=1, le=100, description="返回数量"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """获取标签列表"""
    stmt = select(Tag)
    
    if search:
        search_pattern = f"%{search}%"
        stmt = stmt.where(Tag.name.ilike(search_pattern))
    
    stmt = stmt.order_by(Tag.usage_count.desc(), Tag.name)
    stmt = stmt.limit(limit)
    
    result = await db.execute(stmt)
    tags = result.scalars().all()
    
    return tags


@router.get("/notifications", summary="获取通知列表")
async def get_notifications(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    is_read: Optional[bool] = Query(None, description="是否已读"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """获取用户通知列表"""
    conditions = [Notification.user_id == current_user.id]
    
    if is_read is not None:
        conditions.append(Notification.is_read == is_read)
    
    # 查询总数
    count_stmt = select(func.count(Notification.id)).where(and_(*conditions))
    count_result = await db.execute(count_stmt)
    total = count_result.scalar()
    
    # 查询通知列表
    stmt = select(Notification).where(and_(*conditions))
    stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    stmt = stmt.order_by(Notification.created_at.desc())
    
    result = await db.execute(stmt)
    notifications = result.scalars().all()
    
    return {
        "notifications": notifications,
        "total": total,
        "page": page,
        "page_size": page_size,
        "total_pages": (total + page_size - 1) // page_size
    }


@router.put("/notifications/{notification_id}/read", summary="标记通知为已读")
async def mark_notification_read(
    notification_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """标记通知为已读"""
    stmt = select(Notification).where(
        Notification.id == notification_id,
        Notification.user_id == current_user.id
    )
    result = await db.execute(stmt)
    notification = result.scalar_one_or_none()
    
    if not notification:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="通知不存在"
        )
    
    notification.mark_as_read()
    await db.commit()
    
    return {"message": "通知已标记为已读"}


@router.put("/notifications/read-all", summary="标记所有通知为已读")
async def mark_all_notifications_read(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """标记所有通知为已读"""
    from sqlalchemy import update
    from datetime import datetime, timezone
    
    stmt = update(Notification).where(
        Notification.user_id == current_user.id,
        Notification.is_read == False
    ).values(
        is_read=True,
        read_at=datetime.now(timezone.utc)
    )
    
    await db.execute(stmt)
    await db.commit()
    
    return {"message": "所有通知已标记为已读"}
