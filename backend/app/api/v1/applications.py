"""
AR-System 应用管理API路由
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload
from typing import Optional, List

from app.core.database import get_async_session
from app.core.security import verify_token
from app.models.user import User
from app.models.application import Application, ApplicationVersion, ApplicationReview

router = APIRouter()
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_session)
) -> User:
    """获取当前用户依赖"""
    payload = verify_token(credentials.credentials)
    user_id = payload.get("user_id")
    
    stmt = select(User).where(User.id == user_id)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return user


@router.get("/", summary="获取应用列表")
async def get_applications(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    category: Optional[str] = Query(None, description="应用分类"),
    app_status: Optional[str] = Query(None, description="应用状态"),
    is_featured: Optional[bool] = Query(None, description="是否推荐"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """获取应用列表"""
    # 构建查询条件
    conditions = []
    
    # 非管理员只能看到已发布的应用
    is_admin = current_user.is_superuser or current_user.has_role("admin")
    if not is_admin:
        conditions.append(Application.app_status == "published")
    elif app_status:
        conditions.append(Application.app_status == app_status)
    
    if search:
        search_pattern = f"%{search}%"
        conditions.append(
            or_(
                Application.name.ilike(search_pattern),
                Application.display_name.ilike(search_pattern),
                Application.description.ilike(search_pattern)
            )
        )
    
    if category:
        conditions.append(Application.category == category)
    
    if is_featured is not None:
        conditions.append(Application.is_featured == is_featured)
    
    # 查询总数
    count_stmt = select(func.count(Application.id))
    if conditions:
        count_stmt = count_stmt.where(and_(*conditions))
    
    count_result = await db.execute(count_stmt)
    total = count_result.scalar()
    
    # 查询应用列表
    stmt = select(Application).options(selectinload(Application.developer))
    if conditions:
        stmt = stmt.where(and_(*conditions))
    
    stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    stmt = stmt.order_by(Application.published_at.desc().nullslast(), Application.created_at.desc())
    
    result = await db.execute(stmt)
    applications = result.scalars().all()
    
    return {
        "applications": applications,
        "total": total,
        "page": page,
        "page_size": page_size,
        "total_pages": (total + page_size - 1) // page_size
    }


@router.get("/{app_id}", summary="获取应用详情")
async def get_application(
    app_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """获取应用详情"""
    stmt = select(Application).options(
        selectinload(Application.developer),
        selectinload(Application.versions),
        selectinload(Application.reviews)
    ).where(Application.id == app_id)
    
    result = await db.execute(stmt)
    application = result.scalar_one_or_none()
    
    if not application:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="应用不存在"
        )
    
    # 权限检查
    is_admin = current_user.is_superuser or current_user.has_role("admin")
    is_developer = application.developer_id == current_user.id
    
    if not is_admin and not is_developer and application.app_status != "published":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限查看该应用"
        )
    
    return application


@router.post("/", summary="创建应用")
async def create_application(
    # request: ApplicationCreateRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """创建新应用"""
    # TODO: 实现应用创建逻辑
    return {"message": "应用创建功能待实现"}


@router.put("/{app_id}", summary="更新应用")
async def update_application(
    app_id: int,
    # request: ApplicationUpdateRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """更新应用信息"""
    # TODO: 实现应用更新逻辑
    return {"message": "应用更新功能待实现"}


@router.delete("/{app_id}", summary="删除应用")
async def delete_application(
    app_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """删除应用"""
    # TODO: 实现应用删除逻辑
    return {"message": "应用删除功能待实现"}


@router.get("/{app_id}/versions", summary="获取应用版本列表")
async def get_application_versions(
    app_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """获取应用版本列表"""
    # 先检查应用权限
    application = await get_application(app_id, current_user, db)
    
    stmt = select(ApplicationVersion).where(ApplicationVersion.application_id == app_id)
    stmt = stmt.order_by(ApplicationVersion.released_at.desc().nullslast())
    
    result = await db.execute(stmt)
    versions = result.scalars().all()
    
    return versions


@router.get("/{app_id}/reviews", summary="获取应用评价")
async def get_application_reviews(
    app_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """获取应用评价列表"""
    # 先检查应用权限
    application = await get_application(app_id, current_user, db)
    
    stmt = select(ApplicationReview).options(selectinload(ApplicationReview.user))
    stmt = stmt.where(ApplicationReview.application_id == app_id)
    stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    stmt = stmt.order_by(ApplicationReview.created_at.desc())
    
    result = await db.execute(stmt)
    reviews = result.scalars().all()
    
    return reviews


@router.post("/{app_id}/reviews", summary="创建应用评价")
async def create_application_review(
    app_id: int,
    # request: ApplicationReviewCreateRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """创建应用评价"""
    # TODO: 实现应用评价创建逻辑
    return {"message": "应用评价功能待实现"}


@router.get("/categories", summary="获取应用分类")
async def get_application_categories():
    """获取应用分类列表"""
    from app.models.application import ApplicationCategory
    
    categories = [
        {"value": category.value, "label": category.value}
        for category in ApplicationCategory
    ]
    
    return categories


@router.get("/stats", summary="获取应用统计")
async def get_application_stats(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """获取应用统计信息"""
    # 需要管理员权限
    if not current_user.is_superuser and not current_user.has_role("admin"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    
    # 统计各种状态的应用数量
    stats = {}
    
    # 总应用数
    total_stmt = select(func.count(Application.id))
    total_result = await db.execute(total_stmt)
    stats["total_applications"] = total_result.scalar()
    
    # 已发布应用数
    published_stmt = select(func.count(Application.id)).where(Application.app_status == "published")
    published_result = await db.execute(published_stmt)
    stats["published_applications"] = published_result.scalar()
    
    # 草稿应用数
    draft_stmt = select(func.count(Application.id)).where(Application.app_status == "draft")
    draft_result = await db.execute(draft_stmt)
    stats["draft_applications"] = draft_result.scalar()
    
    # 推荐应用数
    featured_stmt = select(func.count(Application.id)).where(Application.is_featured == True)
    featured_result = await db.execute(featured_stmt)
    stats["featured_applications"] = featured_result.scalar()
    
    return stats
