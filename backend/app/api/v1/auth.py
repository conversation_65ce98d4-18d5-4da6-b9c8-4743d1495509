"""
AR-System 认证API路由
"""
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from datetime import datetime, timezone

from app.core.database import get_async_session
from app.core.security import (
    verify_password, get_password_hash, 
    TokenManager, verify_token
)
from app.models.user import User, UserSession
from app.schemas.auth import (
    LoginRequest, LoginResponse, RegisterRequest, 
    RefreshTokenRequest, ChangePasswordRequest,
    UserResponse
)

router = APIRouter()
security = HTTPBearer()


@router.post("/login", response_model=LoginResponse, summary="用户登录")
async def login(
    request: LoginRequest,
    db: AsyncSession = Depends(get_async_session)
):
    """
    用户登录接口
    
    - **username**: 用户名或邮箱
    - **password**: 密码
    """
    # 查找用户
    stmt = select(User).where(
        (User.username == request.username) | (User.email == request.username)
    ).where(User.is_active == True)
    
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user or not verify_password(request.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    
    # 生成令牌
    try:
        permissions = user.get_permissions()
    except Exception as e:
        print(f"获取用户权限失败，使用空权限列表: {e}")
        permissions = []

    user_data = {
        "user_id": user.id,
        "username": user.username,
        "email": user.email,
        "is_superuser": user.is_superuser,
        "permissions": permissions
    }
    
    tokens = TokenManager.generate_tokens(user_data)
    
    # 创建会话记录
    session = UserSession(
        user_id=user.id,
        session_token=tokens["access_token"],
        refresh_token=tokens["refresh_token"],
        ip_address=request.ip_address,
        user_agent=request.user_agent,
        expires_at=datetime.now(timezone.utc).replace(hour=23, minute=59, second=59)
    )
    db.add(session)
    
    # 更新用户登录信息
    user.last_login_at = datetime.now(timezone.utc)
    user.last_login_ip = request.ip_address
    user.login_count += 1
    
    await db.commit()
    
    # 创建用户响应数据（不包含角色信息，避免异步加载问题）
    user_response = UserResponse.model_validate(user)

    return LoginResponse(
        access_token=tokens["access_token"],
        refresh_token=tokens["refresh_token"],
        token_type=tokens["token_type"],
        expires_in=1800,  # 30分钟
        user=user_response
    )


@router.post("/register", response_model=UserResponse, summary="用户注册")
async def register(
    request: RegisterRequest,
    db: AsyncSession = Depends(get_async_session)
):
    """
    用户注册接口
    
    - **username**: 用户名
    - **email**: 邮箱
    - **password**: 密码
    - **full_name**: 全名（可选）
    """
    # 检查用户名是否已存在
    stmt = select(User).where(User.username == request.username)
    result = await db.execute(stmt)
    if result.scalar_one_or_none():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )
    
    # 检查邮箱是否已存在
    stmt = select(User).where(User.email == request.email)
    result = await db.execute(stmt)
    if result.scalar_one_or_none():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已存在"
        )
    
    # 创建新用户
    user = User(
        username=request.username,
        email=request.email,
        full_name=request.full_name,
        hashed_password=get_password_hash(request.password),
        is_active=True,
        is_verified=False
    )
    
    db.add(user)
    await db.commit()
    await db.refresh(user)
    
    return UserResponse.model_validate(user)


@router.post("/refresh", response_model=LoginResponse, summary="刷新令牌")
async def refresh_token(
    request: RefreshTokenRequest,
    db: AsyncSession = Depends(get_async_session)
):
    """
    刷新访问令牌
    
    - **refresh_token**: 刷新令牌
    """
    try:
        # 验证刷新令牌
        payload = verify_token(request.refresh_token)
        
        if payload.get("type") != "refresh":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌"
            )
        
        # 查找用户
        user_id = payload.get("user_id")
        stmt = select(User).where(User.id == user_id).where(User.is_active == True)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在或已被禁用"
            )
        
        # 生成新的访问令牌
        user_data = {
            "user_id": user.id,
            "username": user.username,
            "email": user.email,
            "is_superuser": user.is_superuser,
            "permissions": user.get_permissions()
        }
        
        new_access_token = TokenManager.refresh_access_token(request.refresh_token)
        
        return LoginResponse(
            access_token=new_access_token,
            refresh_token=request.refresh_token,
            token_type="bearer",
            expires_in=1800,
            user=UserResponse.model_validate(user)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="刷新令牌无效或已过期"
        )


@router.post("/logout", summary="用户登出")
async def logout(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_session)
):
    """
    用户登出接口
    """
    try:
        # 验证令牌
        payload = verify_token(credentials.credentials)
        user_id = payload.get("user_id")
        
        # 删除会话记录
        stmt = select(UserSession).where(
            UserSession.user_id == user_id,
            UserSession.session_token == credentials.credentials
        )
        result = await db.execute(stmt)
        session = result.scalar_one_or_none()
        
        if session:
            session.is_active = False
            await db.commit()
        
        return {"message": "登出成功"}
        
    except Exception:
        # 即使令牌无效也返回成功，避免泄露信息
        return {"message": "登出成功"}


@router.post("/change-password", summary="修改密码")
async def change_password(
    request: ChangePasswordRequest,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_session)
):
    """
    修改密码接口
    
    - **old_password**: 旧密码
    - **new_password**: 新密码
    """
    # 验证令牌
    payload = verify_token(credentials.credentials)
    user_id = payload.get("user_id")
    
    # 查找用户
    stmt = select(User).where(User.id == user_id)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 验证旧密码
    if not verify_password(request.old_password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="旧密码错误"
        )
    
    # 更新密码
    user.hashed_password = get_password_hash(request.new_password)
    await db.commit()
    
    return {"message": "密码修改成功"}


@router.get("/me", summary="获取当前用户信息")
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_session)
):
    """
    获取当前登录用户信息（包含角色和权限）
    """
    from sqlalchemy.orm import selectinload
    from app.models.user import Role

    # 验证令牌
    payload = verify_token(credentials.credentials)
    user_id = payload.get("user_id")

    # 查找用户并加载角色信息
    stmt = select(User).options(
        selectinload(User.roles).selectinload(Role.permissions)
    ).where(User.id == user_id)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 手动构建响应，包含角色信息
    user_dict = {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "full_name": user.full_name,
        "avatar_url": user.avatar_url,
        "is_active": user.is_active,
        "is_verified": user.is_verified,
        "is_superuser": user.is_superuser,
        "phone": user.phone,
        "bio": user.bio,
        "location": user.location,
        "website": user.website,
        "language": user.language,
        "timezone": user.timezone,
        "theme": user.theme,
        "two_factor_enabled": user.two_factor_enabled,
        "last_login_at": user.last_login_at,
        "login_count": user.login_count,
        "created_at": user.created_at,
        "updated_at": user.updated_at,
        # 添加角色信息
        "roles": [
            {
                "id": role.id,
                "name": role.name,
                "display_name": role.display_name,
                "description": role.description,
                "is_active": role.is_active
            }
            for role in user.roles if role.is_active
        ],
        # 添加权限信息
        "permissions": [perm.name for role in user.roles if role.is_active for perm in role.permissions]
    }

    return user_dict


@router.get("/verify-token", summary="验证令牌")
async def verify_token_endpoint(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    验证访问令牌是否有效
    """
    try:
        payload = verify_token(credentials.credentials)
        return {
            "valid": True,
            "user_id": payload.get("user_id"),
            "username": payload.get("username"),
            "expires_at": payload.get("exp")
        }
    except Exception as e:
        return {
            "valid": False,
            "error": str(e)
        }
