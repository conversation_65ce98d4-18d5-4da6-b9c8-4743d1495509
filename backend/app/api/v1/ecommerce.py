"""
AR-System 电商管理API路由
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload
from typing import Optional, List

from app.core.database import get_async_session
from app.core.security import verify_token
from app.models.user import User
from app.models.ecommerce import Product, ProductCategory, Order, OrderItem

router = APIRouter()
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_session)
) -> User:
    """获取当前用户依赖"""
    payload = verify_token(credentials.credentials)
    user_id = payload.get("user_id")
    
    stmt = select(User).where(User.id == user_id)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return user


@router.get("/products", summary="获取商品列表")
async def get_products(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    category_id: Optional[int] = Query(None, description="分类ID"),
    min_price: Optional[float] = Query(None, description="最低价格"),
    max_price: Optional[float] = Query(None, description="最高价格"),
    is_featured: Optional[bool] = Query(None, description="是否推荐"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """获取商品列表"""
    # 构建查询条件
    conditions = [Product.is_active == True]
    
    if search:
        search_pattern = f"%{search}%"
        conditions.append(
            or_(
                Product.name.ilike(search_pattern),
                Product.description.ilike(search_pattern),
                Product.sku.ilike(search_pattern)
            )
        )
    
    if category_id:
        conditions.append(Product.category_id == category_id)
    
    if min_price is not None:
        conditions.append(Product.price >= min_price)
    
    if max_price is not None:
        conditions.append(Product.price <= max_price)
    
    if is_featured is not None:
        conditions.append(Product.is_featured == is_featured)
    
    # 查询总数
    count_stmt = select(func.count(Product.id)).where(and_(*conditions))
    count_result = await db.execute(count_stmt)
    total = count_result.scalar()
    
    # 查询商品列表
    stmt = select(Product).options(selectinload(Product.category))
    stmt = stmt.where(and_(*conditions))
    stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    stmt = stmt.order_by(Product.created_at.desc())
    
    result = await db.execute(stmt)
    products = result.scalars().all()
    
    return {
        "products": products,
        "total": total,
        "page": page,
        "page_size": page_size,
        "total_pages": (total + page_size - 1) // page_size
    }


@router.get("/products/{product_id}", summary="获取商品详情")
async def get_product(
    product_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """获取商品详情"""
    stmt = select(Product).options(
        selectinload(Product.category),
        selectinload(Product.variants),
        selectinload(Product.reviews)
    ).where(Product.id == product_id, Product.is_active == True)
    
    result = await db.execute(stmt)
    product = result.scalar_one_or_none()
    
    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="商品不存在"
        )
    
    # 增加浏览次数
    product.view_count += 1
    await db.commit()
    
    return product


@router.get("/categories", summary="获取商品分类")
async def get_categories(
    parent_id: Optional[int] = Query(None, description="父分类ID"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """获取商品分类列表"""
    conditions = [ProductCategory.is_active == True]
    
    if parent_id is not None:
        conditions.append(ProductCategory.parent_id == parent_id)
    else:
        conditions.append(ProductCategory.parent_id.is_(None))
    
    stmt = select(ProductCategory).where(and_(*conditions))
    stmt = stmt.order_by(ProductCategory.sort_order, ProductCategory.name)
    
    result = await db.execute(stmt)
    categories = result.scalars().all()
    
    return categories


@router.get("/orders", summary="获取订单列表")
async def get_orders(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    order_status: Optional[str] = Query(None, description="订单状态"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """获取订单列表"""
    # 构建查询条件
    conditions = []
    
    # 权限过滤
    is_admin = current_user.is_superuser or current_user.has_role("admin")
    if not is_admin:
        conditions.append(Order.user_id == current_user.id)
    
    if order_status:
        conditions.append(Order.order_status == order_status)
    
    # 查询总数
    count_stmt = select(func.count(Order.id))
    if conditions:
        count_stmt = count_stmt.where(and_(*conditions))
    
    count_result = await db.execute(count_stmt)
    total = count_result.scalar()
    
    # 查询订单列表
    stmt = select(Order).options(selectinload(Order.items))
    if conditions:
        stmt = stmt.where(and_(*conditions))
    
    stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    stmt = stmt.order_by(Order.created_at.desc())
    
    result = await db.execute(stmt)
    orders = result.scalars().all()
    
    return {
        "orders": orders,
        "total": total,
        "page": page,
        "page_size": page_size,
        "total_pages": (total + page_size - 1) // page_size
    }


@router.get("/orders/{order_id}", summary="获取订单详情")
async def get_order(
    order_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """获取订单详情"""
    stmt = select(Order).options(
        selectinload(Order.items).selectinload(OrderItem.product),
        selectinload(Order.payments),
        selectinload(Order.shipments)
    ).where(Order.id == order_id)
    
    result = await db.execute(stmt)
    order = result.scalar_one_or_none()
    
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="订单不存在"
        )
    
    # 权限检查
    is_admin = current_user.is_superuser or current_user.has_role("admin")
    if not is_admin and order.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限查看该订单"
        )
    
    return order


@router.post("/orders", summary="创建订单")
async def create_order(
    # request: OrderCreateRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """创建新订单"""
    # TODO: 实现订单创建逻辑
    return {"message": "订单创建功能待实现"}


@router.put("/orders/{order_id}", summary="更新订单")
async def update_order(
    order_id: int,
    # request: OrderUpdateRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """更新订单状态"""
    # TODO: 实现订单更新逻辑
    return {"message": "订单更新功能待实现"}


@router.post("/cart/add", summary="添加到购物车")
async def add_to_cart(
    # request: CartAddRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """添加商品到购物车"""
    # TODO: 实现购物车功能
    return {"message": "购物车功能待实现"}


@router.get("/cart", summary="获取购物车")
async def get_cart(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """获取用户购物车"""
    # TODO: 实现购物车功能
    return {"message": "购物车功能待实现"}


@router.get("/stats", summary="获取电商统计")
async def get_ecommerce_stats(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """获取电商统计信息"""
    # 需要管理员权限
    if not current_user.is_superuser and not current_user.has_role("admin"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    
    stats = {}
    
    # 商品统计
    total_products_stmt = select(func.count(Product.id)).where(Product.is_active == True)
    total_products_result = await db.execute(total_products_stmt)
    stats["total_products"] = total_products_result.scalar()
    
    # 订单统计
    total_orders_stmt = select(func.count(Order.id))
    total_orders_result = await db.execute(total_orders_stmt)
    stats["total_orders"] = total_orders_result.scalar()
    
    # 销售额统计
    total_sales_stmt = select(func.sum(Order.total_amount)).where(Order.order_status == "completed")
    total_sales_result = await db.execute(total_sales_stmt)
    stats["total_sales"] = total_sales_result.scalar() or 0
    
    # 今日订单
    from datetime import datetime, timezone
    today = datetime.now(timezone.utc).date()
    today_orders_stmt = select(func.count(Order.id)).where(
        func.date(Order.created_at) == today
    )
    today_orders_result = await db.execute(today_orders_stmt)
    stats["today_orders"] = today_orders_result.scalar()
    
    return stats
