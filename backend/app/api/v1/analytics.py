"""
AR-System 数据分析API路由
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, text
from typing import Optional, List, Dict, Any
from datetime import datetime, timezone, timedelta

from app.core.database import get_async_session
from app.core.security import verify_token
from app.models.user import User
from app.models.analytics import SystemMetric, UserBehavior, PerformanceMetric, BusinessMetric, AnalyticsReport

router = APIRouter()
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_session)
) -> User:
    """获取当前用户依赖"""
    payload = verify_token(credentials.credentials)
    user_id = payload.get("user_id")
    
    stmt = select(User).where(User.id == user_id)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return user


async def require_admin(current_user: User = Depends(get_current_user)):
    """需要管理员权限的依赖"""
    if not current_user.is_superuser and not current_user.has_role("admin"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user


@router.get("/overview", summary="获取系统概览")
async def get_system_overview(
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_async_session)
):
    """获取系统概览数据"""
    overview = {}
    
    # 用户统计
    from app.models.user import User as UserModel
    total_users_stmt = select(func.count(UserModel.id))
    total_users_result = await db.execute(total_users_stmt)
    overview["total_users"] = total_users_result.scalar()
    
    active_users_stmt = select(func.count(UserModel.id)).where(UserModel.is_active == True)
    active_users_result = await db.execute(active_users_stmt)
    overview["active_users"] = active_users_result.scalar()
    
    # 设备统计
    from app.models.device import Device
    total_devices_stmt = select(func.count(Device.id))
    total_devices_result = await db.execute(total_devices_stmt)
    overview["total_devices"] = total_devices_result.scalar()
    
    online_devices_stmt = select(func.count(Device.id)).where(Device.device_status == "online")
    online_devices_result = await db.execute(online_devices_stmt)
    overview["online_devices"] = online_devices_result.scalar()
    
    # 应用统计
    from app.models.application import Application
    total_apps_stmt = select(func.count(Application.id))
    total_apps_result = await db.execute(total_apps_stmt)
    overview["total_applications"] = total_apps_result.scalar()
    
    published_apps_stmt = select(func.count(Application.id)).where(Application.app_status == "published")
    published_apps_result = await db.execute(published_apps_stmt)
    overview["published_applications"] = published_apps_result.scalar()
    
    # 订单统计（如果有电商模块）
    try:
        from app.models.ecommerce import Order
        total_orders_stmt = select(func.count(Order.id))
        total_orders_result = await db.execute(total_orders_stmt)
        overview["total_orders"] = total_orders_result.scalar()
        
        # 今日订单
        today = datetime.now(timezone.utc).date()
        today_orders_stmt = select(func.count(Order.id)).where(
            func.date(Order.created_at) == today
        )
        today_orders_result = await db.execute(today_orders_stmt)
        overview["today_orders"] = today_orders_result.scalar()
    except ImportError:
        overview["total_orders"] = 0
        overview["today_orders"] = 0
    
    return overview


@router.get("/metrics", summary="获取系统指标")
async def get_system_metrics(
    metric_name: Optional[str] = Query(None, description="指标名称"),
    metric_type: Optional[str] = Query(None, description="指标类型"),
    hours: int = Query(24, ge=1, le=168, description="时间范围（小时）"),
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_async_session)
):
    """获取系统指标数据"""
    # 构建查询条件
    conditions = []
    
    # 时间范围
    from_time = datetime.now(timezone.utc) - timedelta(hours=hours)
    conditions.append(SystemMetric.timestamp >= from_time)
    
    if metric_name:
        conditions.append(SystemMetric.metric_name == metric_name)
    
    if metric_type:
        conditions.append(SystemMetric.metric_type == metric_type)
    
    stmt = select(SystemMetric).where(and_(*conditions))
    stmt = stmt.order_by(SystemMetric.timestamp.desc())
    
    result = await db.execute(stmt)
    metrics = result.scalars().all()
    
    return [
        {
            "id": metric.id,
            "metric_name": metric.metric_name,
            "metric_type": metric.metric_type,
            "metric_value": metric.metric_value,
            "metric_unit": metric.metric_unit,
            "dimensions": metric.dimensions,
            "tags": metric.tags,
            "timestamp": metric.timestamp
        }
        for metric in metrics
    ]


@router.get("/user-behavior", summary="获取用户行为分析")
async def get_user_behavior(
    event_type: Optional[str] = Query(None, description="事件类型"),
    event_category: Optional[str] = Query(None, description="事件分类"),
    hours: int = Query(24, ge=1, le=168, description="时间范围（小时）"),
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_async_session)
):
    """获取用户行为分析数据"""
    # 构建查询条件
    conditions = []
    
    # 时间范围
    from_time = datetime.now(timezone.utc) - timedelta(hours=hours)
    conditions.append(UserBehavior.timestamp >= from_time)
    
    if event_type:
        conditions.append(UserBehavior.event_type == event_type)
    
    if event_category:
        conditions.append(UserBehavior.event_category == event_category)
    
    stmt = select(UserBehavior).where(and_(*conditions))
    stmt = stmt.order_by(UserBehavior.timestamp.desc())
    stmt = stmt.limit(1000)  # 限制返回数量
    
    result = await db.execute(stmt)
    behaviors = result.scalars().all()
    
    return [
        {
            "id": behavior.id,
            "user_id": behavior.user_id,
            "session_id": behavior.session_id,
            "event_type": behavior.event_type,
            "event_category": behavior.event_category,
            "event_action": behavior.event_action,
            "event_label": behavior.event_label,
            "page_url": behavior.page_url,
            "page_title": behavior.page_title,
            "device_type": behavior.device_type,
            "browser": behavior.browser,
            "os": behavior.os,
            "country": behavior.country,
            "city": behavior.city,
            "custom_data": behavior.custom_data,
            "timestamp": behavior.timestamp
        }
        for behavior in behaviors
    ]


@router.get("/performance", summary="获取性能指标")
async def get_performance_metrics(
    service_name: Optional[str] = Query(None, description="服务名称"),
    endpoint: Optional[str] = Query(None, description="接口端点"),
    hours: int = Query(24, ge=1, le=168, description="时间范围（小时）"),
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_async_session)
):
    """获取性能指标数据"""
    # 构建查询条件
    conditions = []
    
    # 时间范围
    from_time = datetime.now(timezone.utc) - timedelta(hours=hours)
    conditions.append(PerformanceMetric.timestamp >= from_time)
    
    if service_name:
        conditions.append(PerformanceMetric.service_name == service_name)
    
    if endpoint:
        conditions.append(PerformanceMetric.endpoint == endpoint)
    
    stmt = select(PerformanceMetric).where(and_(*conditions))
    stmt = stmt.order_by(PerformanceMetric.timestamp.desc())
    
    result = await db.execute(stmt)
    metrics = result.scalars().all()
    
    return [
        {
            "id": metric.id,
            "service_name": metric.service_name,
            "endpoint": metric.endpoint,
            "method": metric.method,
            "response_time": metric.response_time,
            "cpu_usage": metric.cpu_usage,
            "memory_usage": metric.memory_usage,
            "disk_usage": metric.disk_usage,
            "network_io": metric.network_io,
            "status_code": metric.status_code,
            "error_count": metric.error_count,
            "success_count": metric.success_count,
            "timestamp": metric.timestamp
        }
        for metric in metrics
    ]


@router.get("/business-metrics", summary="获取业务指标")
async def get_business_metrics(
    metric_name: Optional[str] = Query(None, description="指标名称"),
    metric_category: Optional[str] = Query(None, description="指标分类"),
    period_type: str = Query("daily", description="周期类型"),
    days: int = Query(30, ge=1, le=365, description="时间范围（天）"),
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_async_session)
):
    """获取业务指标数据"""
    # 构建查询条件
    conditions = []
    
    # 时间范围
    from_date = datetime.now(timezone.utc).date() - timedelta(days=days)
    conditions.append(BusinessMetric.date_dimension >= from_date)
    conditions.append(BusinessMetric.period_type == period_type)
    
    if metric_name:
        conditions.append(BusinessMetric.metric_name == metric_name)
    
    if metric_category:
        conditions.append(BusinessMetric.metric_category == metric_category)
    
    stmt = select(BusinessMetric).where(and_(*conditions))
    stmt = stmt.order_by(BusinessMetric.date_dimension.desc())
    
    result = await db.execute(stmt)
    metrics = result.scalars().all()
    
    return [
        {
            "id": metric.id,
            "metric_name": metric.metric_name,
            "metric_category": metric.metric_category,
            "metric_value": metric.metric_value,
            "business_unit": metric.business_unit,
            "product_line": metric.product_line,
            "region": metric.region,
            "date_dimension": metric.date_dimension,
            "period_type": metric.period_type,
            "metadata": metric.metadata,
            "timestamp": metric.timestamp
        }
        for metric in metrics
    ]


@router.get("/dashboard-data", summary="获取仪表盘数据")
async def get_dashboard_data(
    dashboard_type: str = Query("overview", description="仪表盘类型"),
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_async_session)
):
    """获取仪表盘数据"""
    if dashboard_type == "overview":
        return await get_system_overview(current_user, db)
    elif dashboard_type == "user":
        return await get_user_analytics(current_user, db)
    elif dashboard_type == "device":
        return await get_device_analytics(current_user, db)
    elif dashboard_type == "application":
        return await get_application_analytics(current_user, db)
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的仪表盘类型"
        )


async def get_user_analytics(current_user: User, db: AsyncSession) -> Dict[str, Any]:
    """获取用户分析数据"""
    # TODO: 实现用户分析逻辑
    return {"message": "用户分析数据"}


async def get_device_analytics(current_user: User, db: AsyncSession) -> Dict[str, Any]:
    """获取设备分析数据"""
    # TODO: 实现设备分析逻辑
    return {"message": "设备分析数据"}


async def get_application_analytics(current_user: User, db: AsyncSession) -> Dict[str, Any]:
    """获取应用分析数据"""
    # TODO: 实现应用分析逻辑
    return {"message": "应用分析数据"}


@router.post("/track", summary="记录用户行为")
async def track_user_behavior(
    event_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """记录用户行为数据"""
    behavior = UserBehavior(
        user_id=current_user.id,
        session_id=event_data.get("session_id"),
        event_type=event_data.get("event_type"),
        event_category=event_data.get("event_category"),
        event_action=event_data.get("event_action"),
        event_label=event_data.get("event_label"),
        page_url=event_data.get("page_url"),
        page_title=event_data.get("page_title"),
        referrer=event_data.get("referrer"),
        device_type=event_data.get("device_type"),
        browser=event_data.get("browser"),
        os=event_data.get("os"),
        ip_address=event_data.get("ip_address"),
        country=event_data.get("country"),
        city=event_data.get("city"),
        custom_data=event_data.get("custom_data")
    )
    
    db.add(behavior)
    await db.commit()
    
    return {"message": "行为数据记录成功"}
