"""
AR-System 设备管理API路由
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload
from typing import Optional, List
from datetime import datetime, timezone, timedelta

from app.core.database import get_async_session
from app.core.security import verify_token
from app.models.user import User
from app.models.device import Device, DeviceMetric, DeviceLog, DeviceCommand
# from app.schemas.device import (
#     DeviceResponse, DeviceCreateRequest, DeviceUpdateRequest,
#     DeviceListResponse, DeviceMetricResponse, DeviceLogResponse,
#     DeviceCommandRequest, DeviceCommandResponse
# )

router = APIRouter()
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_session)
) -> User:
    """获取当前用户依赖"""
    payload = verify_token(credentials.credentials)
    user_id = payload.get("user_id")
    
    stmt = select(User).where(User.id == user_id)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return user


@router.get("/", summary="获取设备列表")
async def get_devices(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    device_type: Optional[str] = Query(None, description="设备类型"),
    device_status: Optional[str] = Query(None, description="设备状态"),
    owner_id: Optional[int] = Query(None, description="所有者ID"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """
    获取设备列表
    
    - 普通用户只能查看自己的设备
    - 管理员可以查看所有设备
    """
    # 构建查询条件
    conditions = []
    
    # 权限过滤
    is_admin = current_user.is_superuser or current_user.has_role("admin")
    if not is_admin:
        conditions.append(Device.owner_id == current_user.id)
    elif owner_id:
        conditions.append(Device.owner_id == owner_id)
    
    if search:
        search_pattern = f"%{search}%"
        conditions.append(
            or_(
                Device.name.ilike(search_pattern),
                Device.device_id.ilike(search_pattern),
                Device.model.ilike(search_pattern),
                Device.manufacturer.ilike(search_pattern)
            )
        )
    
    if device_type:
        conditions.append(Device.device_type == device_type)
    
    if device_status:
        conditions.append(Device.device_status == device_status)
    
    # 查询总数
    count_stmt = select(func.count(Device.id))
    if conditions:
        count_stmt = count_stmt.where(and_(*conditions))
    
    count_result = await db.execute(count_stmt)
    total = count_result.scalar()
    
    # 查询设备列表
    stmt = select(Device).options(selectinload(Device.owner))
    if conditions:
        stmt = stmt.where(and_(*conditions))
    
    stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    stmt = stmt.order_by(Device.last_seen_at.desc().nullslast(), Device.created_at.desc())
    
    result = await db.execute(stmt)
    devices = result.scalars().all()
    
    return {
        "devices": [
            {
                "id": device.id,
                "device_id": device.device_id,
                "name": device.name,
                "model": device.model,
                "manufacturer": device.manufacturer,
                "device_type": device.device_type,
                "device_status": device.device_status,
                "battery_level": device.battery_level,
                "last_seen_at": device.last_seen_at,
                "owner_id": device.owner_id,
                "is_active": device.is_active,
                "created_at": device.created_at,
                "updated_at": device.updated_at
            }
            for device in devices
        ],
        "total": total,
        "page": page,
        "page_size": page_size,
        "total_pages": (total + page_size - 1) // page_size
    }


@router.get("/{device_id}", summary="获取设备详情")
async def get_device(
    device_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """获取设备详情"""
    stmt = select(Device).options(selectinload(Device.owner)).where(Device.id == device_id)
    result = await db.execute(stmt)
    device = result.scalar_one_or_none()

    if not device:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="设备不存在"
        )

    # 权限检查
    is_admin = current_user.is_superuser or current_user.has_role("admin")
    if not is_admin and device.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限查看该设备"
        )

    return {
        "id": device.id,
        "device_id": device.device_id,
        "name": device.name,
        "model": device.model,
        "manufacturer": device.manufacturer,
        "serial_number": device.serial_number,
        "device_type": device.device_type,
        "hardware_version": device.hardware_version,
        "firmware_version": device.firmware_version,
        "os_version": device.os_version,
        "device_status": device.device_status,
        "battery_level": device.battery_level,
        "temperature": device.temperature,
        "ip_address": device.ip_address,
        "mac_address": device.mac_address,
        "wifi_ssid": device.wifi_ssid,
        "signal_strength": device.signal_strength,
        "latitude": device.latitude,
        "longitude": device.longitude,
        "altitude": device.altitude,
        "location_name": device.location_name,
        "cpu_usage": device.cpu_usage,
        "memory_usage": device.memory_usage,
        "storage_usage": device.storage_usage,
        "last_seen_at": device.last_seen_at,
        "first_connected_at": device.first_connected_at,
        "configuration": device.configuration,
        "capabilities": device.capabilities,
        "owner_id": device.owner_id,
        "is_active": device.is_active,
        "created_at": device.created_at,
        "updated_at": device.updated_at
    }


@router.post("/", summary="创建设备")
async def create_device(
    device_data: dict,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """创建新设备"""
    # 检查设备ID是否已存在
    device_id = device_data.get("device_id")
    if not device_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="设备ID不能为空"
        )

    stmt = select(Device).where(Device.device_id == device_id)
    result = await db.execute(stmt)
    if result.scalar_one_or_none():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="设备ID已存在"
        )

    # 创建设备
    device = Device(
        device_id=device_data.get("device_id"),
        name=device_data.get("name", ""),
        model=device_data.get("model", ""),
        manufacturer=device_data.get("manufacturer", ""),
        device_type=device_data.get("device_type", "unknown"),
        owner_id=current_user.id,
        first_connected_at=datetime.now(timezone.utc)
    )

    db.add(device)
    await db.commit()
    await db.refresh(device)

    return {"message": "设备创建成功", "device_id": device.id}


@router.put("/{device_id}", summary="更新设备")
async def update_device(
    device_id: int,
    update_data: dict,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """更新设备信息"""
    stmt = select(Device).where(Device.id == device_id)
    result = await db.execute(stmt)
    device = result.scalar_one_or_none()

    if not device:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="设备不存在"
        )

    # 权限检查
    is_admin = current_user.is_superuser or current_user.has_role("admin")
    if not is_admin and device.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限修改该设备"
        )

    # 更新设备信息
    allowed_fields = [
        "name", "device_status", "battery_level", "temperature",
        "ip_address", "mac_address", "wifi_ssid", "signal_strength",
        "latitude", "longitude", "altitude", "location_name",
        "cpu_usage", "memory_usage", "storage_usage", "configuration"
    ]

    for field, value in update_data.items():
        if field in allowed_fields and hasattr(device, field):
            setattr(device, field, value)

    await db.commit()

    return {"message": "设备更新成功"}


@router.delete("/{device_id}", summary="删除设备")
async def delete_device(
    device_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """删除设备"""
    stmt = select(Device).where(Device.id == device_id)
    result = await db.execute(stmt)
    device = result.scalar_one_or_none()
    
    if not device:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="设备不存在"
        )
    
    # 权限检查
    is_admin = current_user.is_superuser or current_user.has_role("admin")
    if not is_admin and device.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限删除该设备"
        )
    
    await db.delete(device)
    await db.commit()
    
    return {"message": "设备删除成功"}


@router.get("/{device_id}/metrics", summary="获取设备指标")
async def get_device_metrics(
    device_id: int,
    hours: int = Query(24, ge=1, le=168, description="时间范围（小时）"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """获取设备指标数据"""
    # 检查设备权限
    device_info = await get_device(device_id, current_user, db)

    # 查询指标数据
    from_time = datetime.now(timezone.utc) - timedelta(hours=hours)
    stmt = select(DeviceMetric).where(
        DeviceMetric.device_id == device_id,
        DeviceMetric.timestamp >= from_time
    ).order_by(DeviceMetric.timestamp.desc())

    result = await db.execute(stmt)
    metrics = result.scalars().all()

    return [
        {
            "id": metric.id,
            "device_id": metric.device_id,
            "metric_name": metric.metric_name,
            "metric_value": metric.metric_value,
            "metric_unit": metric.metric_unit,
            "timestamp": metric.timestamp
        }
        for metric in metrics
    ]


@router.get("/{device_id}/logs", summary="获取设备日志")
async def get_device_logs(
    device_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=200, description="每页数量"),
    log_level: Optional[str] = Query(None, description="日志级别"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """获取设备日志"""
    # 检查设备权限
    device_info = await get_device(device_id, current_user, db)

    # 构建查询条件
    conditions = [DeviceLog.device_id == device_id]
    if log_level:
        conditions.append(DeviceLog.log_level == log_level)

    stmt = select(DeviceLog).where(and_(*conditions))
    stmt = stmt.offset((page - 1) * page_size).limit(page_size)
    stmt = stmt.order_by(DeviceLog.timestamp.desc())

    result = await db.execute(stmt)
    logs = result.scalars().all()

    return [
        {
            "id": log.id,
            "device_id": log.device_id,
            "log_level": log.log_level,
            "message": log.message,
            "source": log.source,
            "error_code": log.error_code,
            "timestamp": log.timestamp
        }
        for log in logs
    ]


@router.post("/{device_id}/commands", summary="发送设备命令")
async def send_device_command(
    device_id: int,
    command_data: dict,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """发送设备命令"""
    # 检查设备权限
    device_info = await get_device(device_id, current_user, db)

    command_type = command_data.get("command_type")
    if not command_type:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="命令类型不能为空"
        )

    # 创建命令记录
    command = DeviceCommand(
        device_id=device_id,
        command_type=command_type,
        command_data=command_data.get("command_data"),
        sender_id=current_user.id
    )

    db.add(command)
    await db.commit()
    await db.refresh(command)

    # TODO: 实际发送命令到设备
    # 这里可以集成消息队列或WebSocket来实现实时命令发送

    return {
        "message": "命令发送成功",
        "command_id": command.id,
        "status": command.status
    }
