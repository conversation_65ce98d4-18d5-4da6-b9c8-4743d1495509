"""
AR-System 社区管理数据模型
"""
from sqlalchemy import Column, Integer, String, Boolean, Text, ForeignKey, DateTime, JSON, Table
from sqlalchemy.orm import relationship
from datetime import datetime, timezone

from app.models.base import BaseModel, StatusMixin, get_table_name


# 帖子标签关联表
post_tags = Table(
    get_table_name('post_tags'),
    BaseModel.metadata,
    Column('post_id', Integer, ForeignKey(f'{get_table_name("post")}.id'), primary_key=True),
    Column('tag_id', Integer, ForeignKey(f'{get_table_name("tag")}.id'), primary_key=True)
)


class Category(BaseModel, StatusMixin):
    """社区分类模型"""
    
    __tablename__ = get_table_name('category')
    
    name = Column(String(255), nullable=False, comment="分类名称")
    slug = Column(String(255), unique=True, nullable=False, index=True, comment="分类标识")
    description = Column(Text, nullable=True, comment="分类描述")
    
    # 层级结构
    parent_id = Column(Integer, ForeignKey(f'{get_table_name("category")}.id'), nullable=True, comment="父分类ID")
    level = Column(Integer, default=0, comment="分类层级")
    sort_order = Column(Integer, default=0, comment="排序顺序")
    
    # 显示信息
    icon_url = Column(String(500), nullable=True, comment="图标URL")
    color = Column(String(7), nullable=True, comment="颜色代码")
    
    # 统计信息
    post_count = Column(Integer, default=0, comment="帖子数量")
    
    # 关联关系
    parent = relationship("Category", remote_side="Category.id")
    children = relationship("Category")
    posts = relationship("Post", back_populates="category")
    
    def __repr__(self):
        return f"<Category(id={self.id}, name='{self.name}', slug='{self.slug}')>"


class Tag(BaseModel):
    """标签模型"""
    
    __tablename__ = get_table_name('tag')
    
    name = Column(String(100), unique=True, nullable=False, index=True, comment="标签名称")
    slug = Column(String(100), unique=True, nullable=False, index=True, comment="标签标识")
    description = Column(Text, nullable=True, comment="标签描述")
    color = Column(String(7), nullable=True, comment="颜色代码")
    
    # 统计信息
    usage_count = Column(Integer, default=0, comment="使用次数")
    
    # 关联关系
    posts = relationship("Post", secondary=post_tags, back_populates="tags")
    
    def __repr__(self):
        return f"<Tag(id={self.id}, name='{self.name}', usage_count={self.usage_count})>"


class Post(BaseModel, StatusMixin):
    """帖子模型"""
    
    __tablename__ = get_table_name('post')
    
    # 基本信息
    title = Column(String(255), nullable=False, comment="帖子标题")
    slug = Column(String(255), unique=True, nullable=False, index=True, comment="帖子标识")
    content = Column(Text, nullable=False, comment="帖子内容")
    excerpt = Column(Text, nullable=True, comment="摘要")
    
    # 分类和作者
    category_id = Column(Integer, ForeignKey(f'{get_table_name("category")}.id'), nullable=False, comment="分类ID")
    author_id = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=False, comment="作者用户ID")
    
    # 帖子类型
    post_type = Column(String(50), default="discussion", comment="帖子类型")  # discussion, question, announcement, etc.
    
    # 状态信息
    is_pinned = Column(Boolean, default=False, comment="是否置顶")
    is_locked = Column(Boolean, default=False, comment="是否锁定")
    is_featured = Column(Boolean, default=False, comment="是否推荐")
    
    # 统计信息
    view_count = Column(Integer, default=0, comment="浏览次数")
    like_count = Column(Integer, default=0, comment="点赞数")
    reply_count = Column(Integer, default=0, comment="回复数")
    
    # 媒体文件
    attachments = Column(JSON, nullable=True, comment="附件列表JSON")
    images = Column(JSON, nullable=True, comment="图片列表JSON")
    
    # 时间信息
    published_at = Column(DateTime(timezone=True), nullable=True, comment="发布时间")
    last_activity_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), comment="最后活动时间")
    
    # SEO信息
    meta_title = Column(String(255), nullable=True, comment="SEO标题")
    meta_description = Column(Text, nullable=True, comment="SEO描述")
    
    # 关联关系
    category = relationship("Category", back_populates="posts")
    author = relationship("User")
    tags = relationship("Tag", secondary=post_tags, back_populates="posts")
    replies = relationship("Reply", back_populates="post")
    likes = relationship("PostLike", back_populates="post")
    
    def __repr__(self):
        return f"<Post(id={self.id}, title='{self.title}', author_id={self.author_id})>"
    
    def increment_view_count(self):
        """增加浏览次数"""
        self.view_count += 1
    
    def update_last_activity(self):
        """更新最后活动时间"""
        self.last_activity_at = datetime.now(timezone.utc)


class Reply(BaseModel, StatusMixin):
    """回复模型"""
    
    __tablename__ = get_table_name('reply')
    
    post_id = Column(Integer, ForeignKey(f'{get_table_name("post")}.id'), nullable=False, comment="帖子ID")
    author_id = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=False, comment="作者用户ID")
    parent_id = Column(Integer, ForeignKey(f'{get_table_name("reply")}.id'), nullable=True, comment="父回复ID")
    
    # 回复内容
    content = Column(Text, nullable=False, comment="回复内容")
    
    # 统计信息
    like_count = Column(Integer, default=0, comment="点赞数")
    
    # 媒体文件
    attachments = Column(JSON, nullable=True, comment="附件列表JSON")
    images = Column(JSON, nullable=True, comment="图片列表JSON")
    
    # 关联关系
    post = relationship("Post", back_populates="replies")
    author = relationship("User")
    parent = relationship("Reply", remote_side="Reply.id")
    children = relationship("Reply")
    likes = relationship("ReplyLike", back_populates="reply")
    
    def __repr__(self):
        return f"<Reply(id={self.id}, post_id={self.post_id}, author_id={self.author_id})>"


class PostLike(BaseModel):
    """帖子点赞模型"""
    
    __tablename__ = get_table_name('post_like')
    
    post_id = Column(Integer, ForeignKey(f'{get_table_name("post")}.id'), nullable=False, comment="帖子ID")
    user_id = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=False, comment="用户ID")
    
    # 关联关系
    post = relationship("Post", back_populates="likes")
    user = relationship("User")
    
    def __repr__(self):
        return f"<PostLike(id={self.id}, post_id={self.post_id}, user_id={self.user_id})>"


class ReplyLike(BaseModel):
    """回复点赞模型"""
    
    __tablename__ = get_table_name('reply_like')
    
    reply_id = Column(Integer, ForeignKey(f'{get_table_name("reply")}.id'), nullable=False, comment="回复ID")
    user_id = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=False, comment="用户ID")
    
    # 关联关系
    reply = relationship("Reply", back_populates="likes")
    user = relationship("User")
    
    def __repr__(self):
        return f"<ReplyLike(id={self.id}, reply_id={self.reply_id}, user_id={self.user_id})>"


class Follow(BaseModel):
    """关注关系模型"""
    
    __tablename__ = get_table_name('follow')
    
    follower_id = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=False, comment="关注者用户ID")
    following_id = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=False, comment="被关注者用户ID")
    
    # 关注类型
    follow_type = Column(String(20), default="user", comment="关注类型")  # user, category, tag
    
    # 关联关系
    follower = relationship("User", foreign_keys=[follower_id])
    following = relationship("User", foreign_keys=[following_id])
    
    def __repr__(self):
        return f"<Follow(id={self.id}, follower_id={self.follower_id}, following_id={self.following_id})>"


class Notification(BaseModel):
    """通知模型"""
    
    __tablename__ = get_table_name('notification')
    
    user_id = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=False, comment="用户ID")
    
    # 通知内容
    title = Column(String(255), nullable=False, comment="通知标题")
    content = Column(Text, nullable=True, comment="通知内容")
    notification_type = Column(String(50), nullable=False, comment="通知类型")
    
    # 关联信息
    related_id = Column(Integer, nullable=True, comment="关联对象ID")
    related_type = Column(String(50), nullable=True, comment="关联对象类型")
    
    # 状态信息
    is_read = Column(Boolean, default=False, comment="是否已读")
    read_at = Column(DateTime(timezone=True), nullable=True, comment="阅读时间")
    
    # 额外数据
    data = Column(JSON, nullable=True, comment="额外数据JSON")
    
    # 关联关系
    user = relationship("User")
    
    def __repr__(self):
        return f"<Notification(id={self.id}, user_id={self.user_id}, type='{self.notification_type}', is_read={self.is_read})>"
    
    def mark_as_read(self):
        """标记为已读"""
        self.is_read = True
        self.read_at = datetime.now(timezone.utc)


class CommunityReport(BaseModel):
    """社区举报模型"""

    __tablename__ = get_table_name('community_report')
    
    reporter_id = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=False, comment="举报者用户ID")
    
    # 举报对象
    reported_type = Column(String(50), nullable=False, comment="举报对象类型")  # post, reply, user
    reported_id = Column(Integer, nullable=False, comment="举报对象ID")
    
    # 举报内容
    reason = Column(String(100), nullable=False, comment="举报原因")
    description = Column(Text, nullable=True, comment="详细描述")
    
    # 处理状态
    status = Column(String(20), default="pending", comment="处理状态")  # pending, reviewing, resolved, rejected
    
    # 处理信息
    handled_by = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=True, comment="处理者用户ID")
    handled_at = Column(DateTime(timezone=True), nullable=True, comment="处理时间")
    handle_result = Column(Text, nullable=True, comment="处理结果")
    
    # 关联关系
    reporter = relationship("User", foreign_keys=[reporter_id])
    handler = relationship("User", foreign_keys=[handled_by])
    
    def __repr__(self):
        return f"<CommunityReport(id={self.id}, reporter_id={self.reporter_id}, type='{self.reported_type}', status='{self.status}')>"


class Event(BaseModel, StatusMixin):
    """活动模型"""
    
    __tablename__ = get_table_name('event')
    
    # 活动基本信息
    title = Column(String(255), nullable=False, comment="活动标题")
    description = Column(Text, nullable=True, comment="活动描述")
    
    # 活动时间
    start_time = Column(DateTime(timezone=True), nullable=False, comment="开始时间")
    end_time = Column(DateTime(timezone=True), nullable=False, comment="结束时间")
    
    # 活动地点
    location = Column(String(255), nullable=True, comment="活动地点")
    online_url = Column(String(500), nullable=True, comment="在线活动链接")
    
    # 活动类型
    event_type = Column(String(50), nullable=False, comment="活动类型")
    
    # 参与信息
    max_participants = Column(Integer, nullable=True, comment="最大参与人数")
    current_participants = Column(Integer, default=0, comment="当前参与人数")
    
    # 创建者信息
    organizer_id = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=False, comment="组织者用户ID")
    
    # 媒体文件
    banner_url = Column(String(500), nullable=True, comment="横幅图片URL")
    images = Column(JSON, nullable=True, comment="活动图片JSON")
    
    # 关联关系
    organizer = relationship("User")
    
    def __repr__(self):
        return f"<Event(id={self.id}, title='{self.title}', organizer_id={self.organizer_id})>"
