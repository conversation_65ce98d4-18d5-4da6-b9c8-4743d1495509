"""
AR-System 系统管理数据模型
"""
from sqlalchemy import Column, Integer, String, Boolean, Text, DateTime, JSON, Float
from datetime import datetime, timezone

from app.models.base import BaseModel, StatusMixin, get_table_name


class SystemConfig(BaseModel):
    """系统配置模型"""
    
    __tablename__ = get_table_name('system_config')
    
    # 配置基本信息
    key = Column(String(100), unique=True, nullable=False, index=True, comment="配置键")
    value = Column(Text, nullable=True, comment="配置值")
    description = Column(Text, nullable=True, comment="配置描述")
    
    # 配置类型
    config_type = Column(String(50), nullable=False, comment="配置类型")  # string, integer, boolean, json
    category = Column(String(50), nullable=False, comment="配置分类")
    
    # 是否敏感配置
    is_sensitive = Column(Boolean, default=False, comment="是否敏感配置")
    is_readonly = Column(Boolean, default=False, comment="是否只读配置")
    
    def __repr__(self):
        return f"<SystemConfig(id={self.id}, key='{self.key}', type='{self.config_type}')>"
    
    def get_typed_value(self):
        """获取类型化的值"""
        if self.config_type == "boolean":
            return self.value.lower() in ("true", "1", "yes")
        elif self.config_type == "integer":
            return int(self.value) if self.value else 0
        elif self.config_type == "json":
            import json
            return json.loads(self.value) if self.value else {}
        else:
            return self.value


class SystemLog(BaseModel):
    """系统日志模型"""
    
    __tablename__ = get_table_name('system_log')
    
    # 日志基本信息
    level = Column(String(20), nullable=False, comment="日志级别")  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    message = Column(Text, nullable=False, comment="日志消息")
    logger_name = Column(String(100), nullable=True, comment="记录器名称")
    
    # 模块信息
    module = Column(String(100), nullable=True, comment="模块名称")
    function = Column(String(100), nullable=True, comment="函数名称")
    line_number = Column(Integer, nullable=True, comment="行号")
    
    # 异常信息
    exception_type = Column(String(100), nullable=True, comment="异常类型")
    exception_message = Column(Text, nullable=True, comment="异常消息")
    stack_trace = Column(Text, nullable=True, comment="堆栈跟踪")
    
    # 上下文信息
    user_id = Column(Integer, nullable=True, comment="用户ID")
    session_id = Column(String(100), nullable=True, comment="会话ID")
    request_id = Column(String(100), nullable=True, comment="请求ID")
    ip_address = Column(String(45), nullable=True, comment="IP地址")
    
    # 额外数据
    extra_data = Column(JSON, nullable=True, comment="额外数据JSON")
    
    # 时间信息
    timestamp = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), comment="时间戳")
    
    def __repr__(self):
        return f"<SystemLog(id={self.id}, level='{self.level}', message='{self.message[:50]}...')>"


class AuditLog(BaseModel):
    """审计日志模型"""
    
    __tablename__ = get_table_name('audit_log')
    
    # 操作信息
    action = Column(String(100), nullable=False, comment="操作动作")
    resource_type = Column(String(50), nullable=False, comment="资源类型")
    resource_id = Column(String(100), nullable=True, comment="资源ID")
    
    # 操作者信息
    user_id = Column(Integer, nullable=True, comment="操作用户ID")
    username = Column(String(100), nullable=True, comment="用户名")
    
    # 操作详情
    description = Column(Text, nullable=True, comment="操作描述")
    old_values = Column(JSON, nullable=True, comment="旧值JSON")
    new_values = Column(JSON, nullable=True, comment="新值JSON")
    
    # 请求信息
    ip_address = Column(String(45), nullable=True, comment="IP地址")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    request_method = Column(String(10), nullable=True, comment="请求方法")
    request_url = Column(String(500), nullable=True, comment="请求URL")
    
    # 结果信息
    status = Column(String(20), nullable=False, comment="操作状态")  # success, failure, error
    error_message = Column(Text, nullable=True, comment="错误消息")
    
    # 时间信息
    timestamp = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), comment="时间戳")
    
    def __repr__(self):
        return f"<AuditLog(id={self.id}, action='{self.action}', resource_type='{self.resource_type}', user_id={self.user_id})>"


class BackupRecord(BaseModel):
    """备份记录模型"""
    
    __tablename__ = get_table_name('backup_record')
    
    # 备份基本信息
    backup_name = Column(String(255), nullable=False, comment="备份名称")
    backup_type = Column(String(50), nullable=False, comment="备份类型")  # full, incremental, differential
    
    # 备份内容
    tables = Column(JSON, nullable=True, comment="备份表列表JSON")
    file_path = Column(String(500), nullable=True, comment="备份文件路径")
    file_size = Column(Integer, nullable=True, comment="文件大小（字节）")
    
    # 备份状态
    status = Column(String(20), nullable=False, comment="备份状态")  # running, completed, failed
    progress = Column(Integer, default=0, comment="备份进度百分比")
    
    # 时间信息
    started_at = Column(DateTime(timezone=True), nullable=True, comment="开始时间")
    completed_at = Column(DateTime(timezone=True), nullable=True, comment="完成时间")
    
    # 结果信息
    record_count = Column(Integer, nullable=True, comment="记录数量")
    error_message = Column(Text, nullable=True, comment="错误消息")
    
    # 操作者信息
    created_by = Column(Integer, nullable=True, comment="创建者用户ID")
    
    def __repr__(self):
        return f"<BackupRecord(id={self.id}, name='{self.backup_name}', type='{self.backup_type}', status='{self.status}')>"


class ScheduledTask(BaseModel, StatusMixin):
    """定时任务模型"""
    
    __tablename__ = get_table_name('scheduled_task')
    
    # 任务基本信息
    name = Column(String(255), nullable=False, comment="任务名称")
    description = Column(Text, nullable=True, comment="任务描述")
    task_type = Column(String(50), nullable=False, comment="任务类型")
    
    # 任务配置
    command = Column(Text, nullable=False, comment="执行命令")
    parameters = Column(JSON, nullable=True, comment="任务参数JSON")
    
    # 调度配置
    cron_expression = Column(String(100), nullable=False, comment="Cron表达式")
    timezone = Column(String(50), default="Asia/Shanghai", comment="时区")
    
    # 执行状态
    last_run_at = Column(DateTime(timezone=True), nullable=True, comment="最后执行时间")
    next_run_at = Column(DateTime(timezone=True), nullable=True, comment="下次执行时间")
    run_count = Column(Integer, default=0, comment="执行次数")
    
    # 结果信息
    last_status = Column(String(20), nullable=True, comment="最后执行状态")
    last_output = Column(Text, nullable=True, comment="最后执行输出")
    last_error = Column(Text, nullable=True, comment="最后执行错误")
    
    # 创建者信息
    created_by = Column(Integer, nullable=True, comment="创建者用户ID")
    
    def __repr__(self):
        return f"<ScheduledTask(id={self.id}, name='{self.name}', type='{self.task_type}', is_active={self.is_active})>"


class TaskExecution(BaseModel):
    """任务执行记录模型"""
    
    __tablename__ = get_table_name('task_execution')
    
    task_id = Column(Integer, nullable=False, comment="任务ID")
    
    # 执行信息
    execution_id = Column(String(100), nullable=False, index=True, comment="执行ID")
    status = Column(String(20), nullable=False, comment="执行状态")  # running, completed, failed, cancelled
    
    # 时间信息
    started_at = Column(DateTime(timezone=True), nullable=False, comment="开始时间")
    completed_at = Column(DateTime(timezone=True), nullable=True, comment="完成时间")
    duration = Column(Integer, nullable=True, comment="执行时长（秒）")
    
    # 结果信息
    output = Column(Text, nullable=True, comment="执行输出")
    error_message = Column(Text, nullable=True, comment="错误消息")
    exit_code = Column(Integer, nullable=True, comment="退出代码")
    
    # 资源使用
    cpu_usage = Column(Float, nullable=True, comment="CPU使用率")
    memory_usage = Column(Float, nullable=True, comment="内存使用量（MB）")
    
    def __repr__(self):
        return f"<TaskExecution(id={self.id}, task_id={self.task_id}, execution_id='{self.execution_id}', status='{self.status}')>"


class ApiKey(BaseModel, StatusMixin):
    """API密钥模型"""
    
    __tablename__ = get_table_name('api_key')
    
    # 密钥信息
    name = Column(String(255), nullable=False, comment="密钥名称")
    key_hash = Column(String(255), unique=True, nullable=False, index=True, comment="密钥哈希")
    prefix = Column(String(20), nullable=False, comment="密钥前缀")
    
    # 权限信息
    permissions = Column(JSON, nullable=True, comment="权限列表JSON")
    allowed_ips = Column(JSON, nullable=True, comment="允许的IP列表JSON")
    
    # 使用限制
    rate_limit = Column(Integer, nullable=True, comment="速率限制（每分钟请求数）")
    usage_count = Column(Integer, default=0, comment="使用次数")
    
    # 有效期
    expires_at = Column(DateTime(timezone=True), nullable=True, comment="过期时间")
    
    # 最后使用
    last_used_at = Column(DateTime(timezone=True), nullable=True, comment="最后使用时间")
    last_used_ip = Column(String(45), nullable=True, comment="最后使用IP")
    
    # 创建者信息
    created_by = Column(Integer, nullable=False, comment="创建者用户ID")
    
    def __repr__(self):
        return f"<ApiKey(id={self.id}, name='{self.name}', prefix='{self.prefix}', is_active={self.is_active})>"
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.expires_at:
            return datetime.now(timezone.utc) > self.expires_at
        return False
