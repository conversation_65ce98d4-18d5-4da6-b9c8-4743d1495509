"""
AR-System 用户管理数据模型
"""
from sqlalchemy import Column, Integer, String, Boolean, Text, ForeignKey, Table, DateTime
from sqlalchemy.orm import relationship
from datetime import datetime, timezone

from app.models.base import BaseModel, StatusMixin, get_table_name


# 用户角色关联表
user_roles = Table(
    get_table_name('user_roles'),
    BaseModel.metadata,
    Column('user_id', Integer, ForeignKey(f'{get_table_name("user")}.id'), primary_key=True),
    Column('role_id', Integer, ForeignKey(f'{get_table_name("role")}.id'), primary_key=True)
)

# 角色权限关联表
role_permissions = Table(
    get_table_name('role_permissions'),
    BaseModel.metadata,
    Column('role_id', Integer, ForeignKey(f'{get_table_name("role")}.id'), primary_key=True),
    Column('permission_id', Integer, ForeignKey(f'{get_table_name("permission")}.id'), primary_key=True)
)


class User(BaseModel, StatusMixin):
    """用户模型"""
    
    __tablename__ = get_table_name('user')
    
    # 基本信息
    username = Column(String(50), unique=True, nullable=False, index=True, comment="用户名")
    email = Column(String(255), unique=True, nullable=False, index=True, comment="邮箱")
    full_name = Column(String(255), nullable=True, comment="全名")
    avatar_url = Column(String(500), nullable=True, comment="头像URL")
    
    # 认证信息
    hashed_password = Column(String(255), nullable=False, comment="密码哈希")
    is_superuser = Column(Boolean, default=False, comment="是否超级用户")
    is_staff = Column(Boolean, default=False, comment="是否管理员")
    is_verified = Column(Boolean, default=False, comment="是否已验证")
    
    # 个人信息
    phone = Column(String(20), nullable=True, comment="手机号")
    bio = Column(Text, nullable=True, comment="个人简介")
    location = Column(String(255), nullable=True, comment="位置")
    website = Column(String(500), nullable=True, comment="个人网站")
    
    # 偏好设置
    language = Column(String(10), default="zh-CN", comment="语言偏好")
    timezone = Column(String(50), default="Asia/Shanghai", comment="时区")
    theme = Column(String(20), default="light", comment="主题偏好")
    
    # 安全设置
    two_factor_enabled = Column(Boolean, default=False, comment="是否启用双因子认证")
    last_login_at = Column(DateTime(timezone=True), nullable=True, comment="最后登录时间")
    last_login_ip = Column(String(45), nullable=True, comment="最后登录IP")
    login_count = Column(Integer, default=0, comment="登录次数")
    
    # 关联关系
    roles = relationship("Role", secondary=user_roles, back_populates="users")
    activities = relationship("UserActivity", back_populates="user")
    devices = relationship("Device", back_populates="owner")
    orders = relationship("Order", back_populates="user")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"
    
    def has_permission(self, permission_name: str) -> bool:
        """检查用户是否有指定权限"""
        if self.is_superuser:
            return True
        
        for role in self.roles:
            if role.is_active:
                for permission in role.permissions:
                    if permission.name == permission_name:
                        return True
        return False
    
    def has_role(self, role_name: str) -> bool:
        """检查用户是否有指定角色"""
        return any(role.name == role_name and role.is_active for role in self.roles)
    
    def get_permissions(self) -> list:
        """获取用户所有权限"""
        permissions = set()
        try:
            # 检查是否有角色，避免greenlet错误
            if hasattr(self, 'roles'):
                for role in self.roles:
                    if hasattr(role, 'is_active') and role.is_active:
                        if hasattr(role, 'permissions'):
                            for permission in role.permissions:
                                permissions.add(permission.name)
        except Exception as e:
            # 如果查询角色失败，返回空权限列表
            print(f"获取用户权限失败: {e}")
            return []
        return list(permissions)


class Role(BaseModel, StatusMixin):
    """角色模型"""
    
    __tablename__ = get_table_name('role')
    
    name = Column(String(50), unique=True, nullable=False, index=True, comment="角色名称")
    display_name = Column(String(255), nullable=False, comment="显示名称")
    description = Column(Text, nullable=True, comment="角色描述")
    is_system = Column(Boolean, default=False, comment="是否系统角色")
    
    # 关联关系
    users = relationship("User", secondary=user_roles, back_populates="roles")
    permissions = relationship("Permission", secondary=role_permissions, back_populates="roles")
    
    def __repr__(self):
        return f"<Role(id={self.id}, name='{self.name}', display_name='{self.display_name}')>"


class Permission(BaseModel):
    """权限模型"""
    
    __tablename__ = get_table_name('permission')
    
    name = Column(String(100), unique=True, nullable=False, index=True, comment="权限名称")
    display_name = Column(String(255), nullable=False, comment="显示名称")
    description = Column(String(255), nullable=True, comment="权限描述")
    resource = Column(String(50), nullable=False, comment="资源类型")
    action = Column(String(50), nullable=False, comment="操作类型")
    module = Column(String(50), nullable=True, comment="所属模块")
    
    # 关联关系
    roles = relationship("Role", secondary=role_permissions, back_populates="permissions")
    
    def __repr__(self):
        return f"<Permission(id={self.id}, name='{self.name}', resource='{self.resource}', action='{self.action}')>"


class UserActivity(BaseModel):
    """用户活动记录模型"""
    
    __tablename__ = get_table_name('user_activity')
    
    user_id = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=False, comment="用户ID")
    action = Column(String(100), nullable=False, comment="操作类型")
    resource = Column(String(100), nullable=True, comment="操作资源")
    resource_id = Column(String(100), nullable=True, comment="资源ID")
    details = Column(Text, nullable=True, comment="操作详情")
    
    # 请求信息
    ip_address = Column(String(45), nullable=True, comment="IP地址")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    
    # 关联关系
    user = relationship("User", back_populates="activities")
    
    def __repr__(self):
        return f"<UserActivity(id={self.id}, user_id={self.user_id}, action='{self.action}')>"


class UserSession(BaseModel):
    """用户会话模型"""
    
    __tablename__ = get_table_name('user_session')
    
    user_id = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=False, comment="用户ID")
    session_token = Column(String(255), unique=True, nullable=False, index=True, comment="会话令牌")
    refresh_token = Column(String(255), unique=True, nullable=True, comment="刷新令牌")
    
    # 会话信息
    ip_address = Column(String(45), nullable=True, comment="IP地址")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    device_info = Column(Text, nullable=True, comment="设备信息")
    
    # 时间信息
    expires_at = Column(DateTime(timezone=True), nullable=False, comment="过期时间")
    last_activity_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), comment="最后活动时间")
    
    is_active = Column(Boolean, default=True, comment="是否活跃")
    
    # 关联关系
    user = relationship("User")
    
    def __repr__(self):
        return f"<UserSession(id={self.id}, user_id={self.user_id}, is_active={self.is_active})>"
    
    def is_expired(self) -> bool:
        """检查会话是否过期"""
        return datetime.now(timezone.utc) > self.expires_at


class UserProfile(BaseModel):
    """用户详细资料模型"""
    
    __tablename__ = get_table_name('user_profile')
    
    user_id = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), unique=True, nullable=False, comment="用户ID")
    
    # 个人信息
    first_name = Column(String(100), nullable=True, comment="名")
    last_name = Column(String(100), nullable=True, comment="姓")
    gender = Column(String(10), nullable=True, comment="性别")
    birth_date = Column(DateTime, nullable=True, comment="出生日期")
    
    # 联系信息
    address = Column(Text, nullable=True, comment="地址")
    city = Column(String(100), nullable=True, comment="城市")
    country = Column(String(100), nullable=True, comment="国家")
    postal_code = Column(String(20), nullable=True, comment="邮政编码")
    
    # 职业信息
    company = Column(String(255), nullable=True, comment="公司")
    job_title = Column(String(255), nullable=True, comment="职位")
    industry = Column(String(100), nullable=True, comment="行业")
    
    # 社交信息
    github_url = Column(String(500), nullable=True, comment="GitHub链接")
    linkedin_url = Column(String(500), nullable=True, comment="LinkedIn链接")
    twitter_url = Column(String(500), nullable=True, comment="Twitter链接")
    
    # 关联关系
    user = relationship("User")
    
    def __repr__(self):
        return f"<UserProfile(id={self.id}, user_id={self.user_id})>"
