"""
AR-System 设备管理数据模型
"""
from sqlalchemy import Column, Integer, String, Boolean, Text, ForeignKey, DateTime, Float, JSON
from sqlalchemy.orm import relationship
from datetime import datetime, timezone

from app.models.base import BaseModel, StatusMixin, get_table_name, DeviceStatusEnum


class Device(BaseModel, StatusMixin):
    """设备模型"""
    
    __tablename__ = get_table_name('device')
    
    # 基本信息
    device_id = Column(String(100), unique=True, nullable=False, index=True, comment="设备唯一标识")
    name = Column(String(255), nullable=False, comment="设备名称")
    model = Column(String(100), nullable=False, comment="设备型号")
    manufacturer = Column(String(100), nullable=False, comment="制造商")
    serial_number = Column(String(100), unique=True, nullable=True, comment="序列号")
    
    # 设备类型和版本
    device_type = Column(String(50), nullable=False, comment="设备类型")  # ar_glasses, ar_headset, mobile, etc.
    hardware_version = Column(String(50), nullable=True, comment="硬件版本")
    firmware_version = Column(String(50), nullable=True, comment="固件版本")
    os_version = Column(String(50), nullable=True, comment="操作系统版本")
    
    # 状态信息
    device_status = Column(String(20), default=DeviceStatusEnum.OFFLINE, comment="设备状态")
    battery_level = Column(Integer, nullable=True, comment="电池电量百分比")
    temperature = Column(Float, nullable=True, comment="设备温度")
    
    # 网络信息
    ip_address = Column(String(45), nullable=True, comment="IP地址")
    mac_address = Column(String(17), nullable=True, comment="MAC地址")
    wifi_ssid = Column(String(100), nullable=True, comment="WiFi SSID")
    signal_strength = Column(Integer, nullable=True, comment="信号强度")
    
    # 位置信息
    latitude = Column(Float, nullable=True, comment="纬度")
    longitude = Column(Float, nullable=True, comment="经度")
    altitude = Column(Float, nullable=True, comment="海拔")
    location_name = Column(String(255), nullable=True, comment="位置名称")
    
    # 性能指标
    cpu_usage = Column(Float, nullable=True, comment="CPU使用率")
    memory_usage = Column(Float, nullable=True, comment="内存使用率")
    storage_usage = Column(Float, nullable=True, comment="存储使用率")
    
    # 时间信息
    last_seen_at = Column(DateTime(timezone=True), nullable=True, comment="最后在线时间")
    first_connected_at = Column(DateTime(timezone=True), nullable=True, comment="首次连接时间")
    
    # 配置信息
    configuration = Column(JSON, nullable=True, comment="设备配置JSON")
    capabilities = Column(JSON, nullable=True, comment="设备能力JSON")
    
    # 所有者信息
    owner_id = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=True, comment="所有者用户ID")
    
    # 关联关系
    owner = relationship("User", back_populates="devices")
    metrics = relationship("DeviceMetric", back_populates="device")
    logs = relationship("DeviceLog", back_populates="device")
    applications = relationship("DeviceApplication", back_populates="device")
    
    def __repr__(self):
        return f"<Device(id={self.id}, device_id='{self.device_id}', name='{self.name}', status='{self.device_status}')>"
    
    def is_online(self) -> bool:
        """检查设备是否在线"""
        return self.device_status == DeviceStatusEnum.ONLINE
    
    def update_last_seen(self):
        """更新最后在线时间"""
        self.last_seen_at = datetime.now(timezone.utc)
    
    def get_uptime_hours(self) -> float:
        """获取设备运行时间（小时）"""
        if self.last_seen_at and self.first_connected_at:
            delta = self.last_seen_at - self.first_connected_at
            return delta.total_seconds() / 3600
        return 0.0


class DeviceMetric(BaseModel):
    """设备指标模型"""
    
    __tablename__ = get_table_name('device_metric')
    
    device_id = Column(Integer, ForeignKey(f'{get_table_name("device")}.id'), nullable=False, comment="设备ID")
    metric_name = Column(String(100), nullable=False, comment="指标名称")
    metric_value = Column(Float, nullable=False, comment="指标值")
    metric_unit = Column(String(20), nullable=True, comment="指标单位")
    
    # 时间戳
    timestamp = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), comment="时间戳")
    
    # 关联关系
    device = relationship("Device", back_populates="metrics")
    
    def __repr__(self):
        return f"<DeviceMetric(id={self.id}, device_id={self.device_id}, metric_name='{self.metric_name}', value={self.metric_value})>"


class DeviceLog(BaseModel):
    """设备日志模型"""
    
    __tablename__ = get_table_name('device_log')
    
    device_id = Column(Integer, ForeignKey(f'{get_table_name("device")}.id'), nullable=False, comment="设备ID")
    log_level = Column(String(20), nullable=False, comment="日志级别")  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    message = Column(Text, nullable=False, comment="日志消息")
    source = Column(String(100), nullable=True, comment="日志来源")
    
    # 额外信息
    error_code = Column(String(50), nullable=True, comment="错误代码")
    stack_trace = Column(Text, nullable=True, comment="堆栈跟踪")
    context = Column(JSON, nullable=True, comment="上下文信息JSON")
    
    # 时间戳
    timestamp = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), comment="时间戳")
    
    # 关联关系
    device = relationship("Device", back_populates="logs")
    
    def __repr__(self):
        return f"<DeviceLog(id={self.id}, device_id={self.device_id}, level='{self.log_level}', message='{self.message[:50]}...')>"


class DeviceGroup(BaseModel, StatusMixin):
    """设备组模型"""
    
    __tablename__ = get_table_name('device_group')
    
    name = Column(String(255), nullable=False, comment="组名称")
    description = Column(Text, nullable=True, comment="组描述")
    group_type = Column(String(50), nullable=False, comment="组类型")  # location, model, department, etc.
    
    # 配置信息
    configuration = Column(JSON, nullable=True, comment="组配置JSON")
    
    # 关联关系
    devices = relationship("Device", secondary="ar_device_group_members", back_populates="groups")
    
    def __repr__(self):
        return f"<DeviceGroup(id={self.id}, name='{self.name}', type='{self.group_type}')>"


# 设备组成员关联表
from sqlalchemy import Table
device_group_members = Table(
    get_table_name('device_group_members'),
    BaseModel.metadata,
    Column('device_id', Integer, ForeignKey(f'{get_table_name("device")}.id'), primary_key=True),
    Column('group_id', Integer, ForeignKey(f'{get_table_name("device_group")}.id'), primary_key=True)
)

# 更新Device模型以包含groups关系
Device.groups = relationship("DeviceGroup", secondary=device_group_members, back_populates="devices")


class DeviceApplication(BaseModel, StatusMixin):
    """设备应用关联模型"""
    
    __tablename__ = get_table_name('device_application')
    
    device_id = Column(Integer, ForeignKey(f'{get_table_name("device")}.id'), nullable=False, comment="设备ID")
    application_id = Column(Integer, ForeignKey(f'{get_table_name("application")}.id'), nullable=False, comment="应用ID")
    
    # 安装信息
    installed_version = Column(String(50), nullable=True, comment="已安装版本")
    installation_date = Column(DateTime(timezone=True), nullable=True, comment="安装日期")
    last_used_at = Column(DateTime(timezone=True), nullable=True, comment="最后使用时间")
    
    # 使用统计
    usage_count = Column(Integer, default=0, comment="使用次数")
    total_usage_time = Column(Integer, default=0, comment="总使用时间（秒）")
    
    # 配置信息
    app_configuration = Column(JSON, nullable=True, comment="应用配置JSON")
    
    # 关联关系
    device = relationship("Device", back_populates="applications")
    application = relationship("Application", back_populates="devices")
    
    def __repr__(self):
        return f"<DeviceApplication(id={self.id}, device_id={self.device_id}, application_id={self.application_id})>"


class DeviceCommand(BaseModel):
    """设备命令模型"""
    
    __tablename__ = get_table_name('device_command')
    
    device_id = Column(Integer, ForeignKey(f'{get_table_name("device")}.id'), nullable=False, comment="设备ID")
    command_type = Column(String(50), nullable=False, comment="命令类型")
    command_data = Column(JSON, nullable=True, comment="命令数据JSON")
    
    # 执行状态
    status = Column(String(20), default="pending", comment="执行状态")  # pending, executing, completed, failed
    result = Column(Text, nullable=True, comment="执行结果")
    error_message = Column(Text, nullable=True, comment="错误消息")
    
    # 时间信息
    sent_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), comment="发送时间")
    executed_at = Column(DateTime(timezone=True), nullable=True, comment="执行时间")
    completed_at = Column(DateTime(timezone=True), nullable=True, comment="完成时间")
    
    # 发送者信息
    sender_id = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=True, comment="发送者用户ID")
    
    # 关联关系
    device = relationship("Device")
    sender = relationship("User")
    
    def __repr__(self):
        return f"<DeviceCommand(id={self.id}, device_id={self.device_id}, type='{self.command_type}', status='{self.status}')>"
