"""
AR-System 数据分析模型
"""
from sqlalchemy import Column, Integer, String, Boolean, Text, ForeignKey, DateTime, Float, JSON
from sqlalchemy.orm import relationship
from datetime import datetime, timezone

from app.models.base import BaseModel, get_table_name


class SystemMetric(BaseModel):
    """系统指标模型"""
    
    __tablename__ = get_table_name('system_metric')
    
    # 指标基本信息
    metric_name = Column(String(100), nullable=False, index=True, comment="指标名称")
    metric_type = Column(String(50), nullable=False, comment="指标类型")
    metric_value = Column(Float, nullable=False, comment="指标值")
    metric_unit = Column(String(20), nullable=True, comment="指标单位")
    
    # 维度信息
    dimensions = Column(JSON, nullable=True, comment="维度信息JSON")
    tags = Column(JSON, nullable=True, comment="标签信息JSON")
    
    # 时间信息
    timestamp = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), comment="时间戳")
    
    def __repr__(self):
        return f"<SystemMetric(id={self.id}, name='{self.metric_name}', value={self.metric_value}, timestamp={self.timestamp})>"


class UserBehavior(BaseModel):
    """用户行为分析模型"""
    
    __tablename__ = get_table_name('user_behavior')
    
    user_id = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=True, comment="用户ID")
    session_id = Column(String(100), nullable=True, comment="会话ID")
    
    # 行为信息
    event_type = Column(String(50), nullable=False, comment="事件类型")
    event_category = Column(String(50), nullable=True, comment="事件分类")
    event_action = Column(String(100), nullable=False, comment="事件动作")
    event_label = Column(String(255), nullable=True, comment="事件标签")
    
    # 页面信息
    page_url = Column(String(500), nullable=True, comment="页面URL")
    page_title = Column(String(255), nullable=True, comment="页面标题")
    referrer = Column(String(500), nullable=True, comment="来源页面")
    
    # 设备信息
    device_type = Column(String(50), nullable=True, comment="设备类型")
    browser = Column(String(100), nullable=True, comment="浏览器")
    os = Column(String(100), nullable=True, comment="操作系统")
    
    # 位置信息
    ip_address = Column(String(45), nullable=True, comment="IP地址")
    country = Column(String(100), nullable=True, comment="国家")
    city = Column(String(100), nullable=True, comment="城市")
    
    # 额外数据
    custom_data = Column(JSON, nullable=True, comment="自定义数据JSON")
    
    # 时间信息
    timestamp = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), comment="时间戳")
    
    # 关联关系
    user = relationship("User")
    
    def __repr__(self):
        return f"<UserBehavior(id={self.id}, user_id={self.user_id}, event_type='{self.event_type}', action='{self.event_action}')>"


class PerformanceMetric(BaseModel):
    """性能指标模型"""
    
    __tablename__ = get_table_name('performance_metric')
    
    # 服务信息
    service_name = Column(String(100), nullable=False, comment="服务名称")
    endpoint = Column(String(255), nullable=True, comment="接口端点")
    method = Column(String(10), nullable=True, comment="HTTP方法")
    
    # 性能指标
    response_time = Column(Float, nullable=True, comment="响应时间（毫秒）")
    cpu_usage = Column(Float, nullable=True, comment="CPU使用率")
    memory_usage = Column(Float, nullable=True, comment="内存使用量（MB）")
    disk_usage = Column(Float, nullable=True, comment="磁盘使用率")
    network_io = Column(Float, nullable=True, comment="网络IO（KB/s）")
    
    # 状态信息
    status_code = Column(Integer, nullable=True, comment="状态码")
    error_count = Column(Integer, default=0, comment="错误次数")
    success_count = Column(Integer, default=0, comment="成功次数")
    
    # 时间信息
    timestamp = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), comment="时间戳")
    
    def __repr__(self):
        return f"<PerformanceMetric(id={self.id}, service='{self.service_name}', response_time={self.response_time})>"


class BusinessMetric(BaseModel):
    """业务指标模型"""
    
    __tablename__ = get_table_name('business_metric')
    
    # 指标信息
    metric_name = Column(String(100), nullable=False, comment="指标名称")
    metric_category = Column(String(50), nullable=False, comment="指标分类")
    metric_value = Column(Float, nullable=False, comment="指标值")
    
    # 业务维度
    business_unit = Column(String(100), nullable=True, comment="业务单元")
    product_line = Column(String(100), nullable=True, comment="产品线")
    region = Column(String(100), nullable=True, comment="地区")
    
    # 时间维度
    date_dimension = Column(DateTime, nullable=False, comment="日期维度")
    period_type = Column(String(20), nullable=False, comment="周期类型")  # daily, weekly, monthly, yearly
    
    # 额外信息
    metric_metadata = Column(JSON, nullable=True, comment="元数据JSON")
    
    # 时间信息
    timestamp = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), comment="时间戳")
    
    def __repr__(self):
        return f"<BusinessMetric(id={self.id}, name='{self.metric_name}', value={self.metric_value}, date={self.date_dimension})>"


class AnalyticsReport(BaseModel):
    """分析报告模型"""

    __tablename__ = get_table_name('analytics_report')
    
    # 报告基本信息
    name = Column(String(255), nullable=False, comment="报告名称")
    description = Column(Text, nullable=True, comment="报告描述")
    report_type = Column(String(50), nullable=False, comment="报告类型")
    
    # 报告配置
    config = Column(JSON, nullable=True, comment="报告配置JSON")
    filters = Column(JSON, nullable=True, comment="过滤条件JSON")
    
    # 生成信息
    generated_by = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=True, comment="生成者用户ID")
    generated_at = Column(DateTime(timezone=True), nullable=True, comment="生成时间")
    
    # 状态信息
    status = Column(String(20), default="draft", comment="报告状态")
    is_scheduled = Column(Boolean, default=False, comment="是否定时报告")
    schedule_config = Column(JSON, nullable=True, comment="定时配置JSON")
    
    # 文件信息
    file_url = Column(String(500), nullable=True, comment="报告文件URL")
    file_size = Column(Integer, nullable=True, comment="文件大小（字节）")
    
    # 关联关系
    generator = relationship("User")
    
    def __repr__(self):
        return f"<AnalyticsReport(id={self.id}, name='{self.name}', type='{self.report_type}', status='{self.status}')>"


class Dashboard(BaseModel):
    """仪表盘模型"""
    
    __tablename__ = get_table_name('dashboard')
    
    # 仪表盘基本信息
    name = Column(String(255), nullable=False, comment="仪表盘名称")
    description = Column(Text, nullable=True, comment="仪表盘描述")
    
    # 配置信息
    layout_config = Column(JSON, nullable=True, comment="布局配置JSON")
    widgets_config = Column(JSON, nullable=True, comment="组件配置JSON")
    
    # 权限信息
    is_public = Column(Boolean, default=False, comment="是否公开")
    owner_id = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=False, comment="所有者用户ID")
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    # 关联关系
    owner = relationship("User")
    
    def __repr__(self):
        return f"<Dashboard(id={self.id}, name='{self.name}', owner_id={self.owner_id})>"


class Alert(BaseModel):
    """告警模型"""
    
    __tablename__ = get_table_name('alert')
    
    # 告警基本信息
    name = Column(String(255), nullable=False, comment="告警名称")
    description = Column(Text, nullable=True, comment="告警描述")
    alert_type = Column(String(50), nullable=False, comment="告警类型")
    
    # 告警条件
    condition_config = Column(JSON, nullable=False, comment="告警条件配置JSON")
    threshold_value = Column(Float, nullable=True, comment="阈值")
    
    # 告警级别
    severity = Column(String(20), nullable=False, comment="严重级别")  # low, medium, high, critical
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_triggered = Column(Boolean, default=False, comment="是否已触发")
    last_triggered_at = Column(DateTime(timezone=True), nullable=True, comment="最后触发时间")
    
    # 通知配置
    notification_config = Column(JSON, nullable=True, comment="通知配置JSON")
    
    # 创建者信息
    created_by = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=True, comment="创建者用户ID")
    
    # 关联关系
    creator = relationship("User")
    
    def __repr__(self):
        return f"<Alert(id={self.id}, name='{self.name}', type='{self.alert_type}', severity='{self.severity}')>"
