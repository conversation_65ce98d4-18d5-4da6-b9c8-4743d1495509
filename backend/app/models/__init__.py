# AR-System Models Package

# 导入所有模型以确保它们被注册到SQLAlchemy
from .user import User, Role, Permission
from .device import Device, DeviceMetric, DeviceLog, DeviceCommand, DeviceApplication
from .application import Application, ApplicationVersion, ApplicationReview
from .analytics import SystemMetric, UserBehavior, PerformanceMetric, BusinessMetric, AnalyticsReport
from .ecommerce import Product, ProductCategory, ProductVariant, Order, OrderItem, Payment
from .community import Post, Category, Tag, Reply, PostLike, Notification, CommunityReport
from .system import SystemConfig, AuditLog

__all__ = [
    # User models
    "User", "Role", "Permission",

    # Device models
    "Device", "DeviceMetric", "DeviceLog", "DeviceCommand", "DeviceApplication",

    # Application models
    "Application", "ApplicationVersion", "ApplicationReview",

    # Analytics models
    "SystemMetric", "UserBehavior", "PerformanceMetric", "BusinessMetric", "AnalyticsReport",

    # Ecommerce models
    "Product", "ProductCategory", "ProductVariant", "Order", "OrderItem", "Payment",

    # Community models
    "Post", "Category", "Tag", "Reply", "PostLike", "Notification", "CommunityReport",

    # System models
    "SystemConfig", "AuditLog",
]
