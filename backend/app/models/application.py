"""
AR-System 应用管理数据模型
"""
from sqlalchemy import Column, Integer, String, Boolean, Text, ForeignKey, DateTime, Float, JSON, Enum
from sqlalchemy.orm import relationship
from datetime import datetime, timezone
import enum

from app.models.base import BaseModel, StatusMixin, get_table_name, ApplicationStatusEnum


class ApplicationCategory(enum.Enum):
    """应用分类枚举"""
    PRODUCTIVITY = "productivity"
    ENTERTAINMENT = "entertainment"
    EDUCATION = "education"
    SOCIAL = "social"
    UTILITY = "utility"
    GAME = "game"
    BUSINESS = "business"
    HEALTH = "health"
    DEVELOPER = "developer"
    SYSTEM = "system"


class Application(BaseModel, StatusMixin):
    """应用模型"""
    
    __tablename__ = get_table_name('application')
    
    # 基本信息
    app_id = Column(String(100), unique=True, nullable=False, index=True, comment="应用唯一标识")
    name = Column(String(255), nullable=False, comment="应用名称")
    display_name = Column(String(255), nullable=True, comment="显示名称")
    description = Column(Text, nullable=True, comment="应用描述")
    
    # 版本信息
    version = Column(String(50), nullable=False, comment="当前版本")
    build_number = Column(Integer, nullable=True, comment="构建号")
    min_os_version = Column(String(50), nullable=True, comment="最低操作系统版本")
    
    # 分类和标签
    category = Column(Enum(ApplicationCategory), nullable=False, comment="应用分类")
    tags = Column(String(500), nullable=True, comment="标签（逗号分隔）")
    
    # 开发者信息
    developer_id = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=False, comment="开发者用户ID")
    developer_name = Column(String(255), nullable=True, comment="开发者名称")
    developer_email = Column(String(255), nullable=True, comment="开发者邮箱")
    
    # 应用状态
    app_status = Column(String(20), default=ApplicationStatusEnum.DRAFT, comment="应用状态")
    is_featured = Column(Boolean, default=False, comment="是否推荐应用")
    is_system_app = Column(Boolean, default=False, comment="是否系统应用")
    
    # 文件信息
    package_url = Column(String(500), nullable=True, comment="安装包URL")
    package_size = Column(Integer, nullable=True, comment="安装包大小（字节）")
    icon_url = Column(String(500), nullable=True, comment="图标URL")
    screenshots = Column(JSON, nullable=True, comment="截图URL列表JSON")
    
    # 权限和要求
    permissions = Column(JSON, nullable=True, comment="所需权限JSON")
    hardware_requirements = Column(JSON, nullable=True, comment="硬件要求JSON")
    
    # 统计信息
    download_count = Column(Integer, default=0, comment="下载次数")
    install_count = Column(Integer, default=0, comment="安装次数")
    rating_average = Column(Float, default=0.0, comment="平均评分")
    rating_count = Column(Integer, default=0, comment="评分次数")
    
    # 发布信息
    published_at = Column(DateTime(timezone=True), nullable=True, comment="发布时间")
    last_updated_at = Column(DateTime(timezone=True), nullable=True, comment="最后更新时间")
    
    # 配置信息
    configuration = Column(JSON, nullable=True, comment="应用配置JSON")
    app_metadata = Column(JSON, nullable=True, comment="元数据JSON")
    
    # 关联关系
    developer = relationship("User")
    versions = relationship("ApplicationVersion", back_populates="application")
    reviews = relationship("ApplicationReview", back_populates="application")
    devices = relationship("DeviceApplication", back_populates="application")
    
    def __repr__(self):
        return f"<Application(id={self.id}, app_id='{self.app_id}', name='{self.name}', version='{self.version}')>"
    
    def is_published(self) -> bool:
        """检查应用是否已发布"""
        return self.app_status == ApplicationStatusEnum.PUBLISHED
    
    def update_rating(self, new_rating: float):
        """更新应用评分"""
        total_score = self.rating_average * self.rating_count + new_rating
        self.rating_count += 1
        self.rating_average = total_score / self.rating_count


class ApplicationVersion(BaseModel):
    """应用版本模型"""
    
    __tablename__ = get_table_name('application_version')
    
    application_id = Column(Integer, ForeignKey(f'{get_table_name("application")}.id'), nullable=False, comment="应用ID")
    version = Column(String(50), nullable=False, comment="版本号")
    build_number = Column(Integer, nullable=True, comment="构建号")
    
    # 版本信息
    release_notes = Column(Text, nullable=True, comment="版本说明")
    is_stable = Column(Boolean, default=True, comment="是否稳定版本")
    is_beta = Column(Boolean, default=False, comment="是否测试版本")
    
    # 文件信息
    package_url = Column(String(500), nullable=True, comment="安装包URL")
    package_size = Column(Integer, nullable=True, comment="安装包大小（字节）")
    package_hash = Column(String(128), nullable=True, comment="安装包哈希值")
    
    # 兼容性
    min_os_version = Column(String(50), nullable=True, comment="最低操作系统版本")
    supported_devices = Column(JSON, nullable=True, comment="支持的设备类型JSON")
    
    # 发布信息
    released_at = Column(DateTime(timezone=True), nullable=True, comment="发布时间")
    download_count = Column(Integer, default=0, comment="下载次数")
    
    # 关联关系
    application = relationship("Application", back_populates="versions")
    
    def __repr__(self):
        return f"<ApplicationVersion(id={self.id}, application_id={self.application_id}, version='{self.version}')>"


class ApplicationReview(BaseModel):
    """应用评价模型"""
    
    __tablename__ = get_table_name('application_review')
    
    application_id = Column(Integer, ForeignKey(f'{get_table_name("application")}.id'), nullable=False, comment="应用ID")
    user_id = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=False, comment="用户ID")
    
    # 评价内容
    rating = Column(Integer, nullable=False, comment="评分（1-5）")
    title = Column(String(255), nullable=True, comment="评价标题")
    content = Column(Text, nullable=True, comment="评价内容")
    
    # 版本信息
    app_version = Column(String(50), nullable=True, comment="评价的应用版本")
    device_model = Column(String(100), nullable=True, comment="使用的设备型号")
    
    # 状态信息
    is_verified = Column(Boolean, default=False, comment="是否已验证")
    is_helpful = Column(Boolean, default=False, comment="是否有帮助")
    helpful_count = Column(Integer, default=0, comment="有帮助的投票数")
    
    # 关联关系
    application = relationship("Application", back_populates="reviews")
    user = relationship("User")
    
    def __repr__(self):
        return f"<ApplicationReview(id={self.id}, application_id={self.application_id}, user_id={self.user_id}, rating={self.rating})>"


class ApplicationPermission(BaseModel):
    """应用权限模型"""
    
    __tablename__ = get_table_name('application_permission')
    
    name = Column(String(100), unique=True, nullable=False, index=True, comment="权限名称")
    display_name = Column(String(255), nullable=False, comment="显示名称")
    description = Column(Text, nullable=True, comment="权限描述")
    
    # 权限级别
    level = Column(String(20), nullable=False, comment="权限级别")  # normal, dangerous, signature, system
    group_name = Column(String(100), nullable=True, comment="权限组名称")
    
    # 是否敏感权限
    is_sensitive = Column(Boolean, default=False, comment="是否敏感权限")
    requires_approval = Column(Boolean, default=False, comment="是否需要审批")
    
    def __repr__(self):
        return f"<ApplicationPermission(id={self.id}, name='{self.name}', level='{self.level}')>"


class ApplicationDeployment(BaseModel):
    """应用部署模型"""
    
    __tablename__ = get_table_name('application_deployment')
    
    application_id = Column(Integer, ForeignKey(f'{get_table_name("application")}.id'), nullable=False, comment="应用ID")
    version_id = Column(Integer, ForeignKey(f'{get_table_name("application_version")}.id'), nullable=False, comment="版本ID")
    
    # 部署信息
    deployment_type = Column(String(50), nullable=False, comment="部署类型")  # manual, automatic, scheduled
    target_devices = Column(JSON, nullable=True, comment="目标设备JSON")
    deployment_config = Column(JSON, nullable=True, comment="部署配置JSON")
    
    # 状态信息
    status = Column(String(20), default="pending", comment="部署状态")  # pending, in_progress, completed, failed
    progress = Column(Integer, default=0, comment="部署进度百分比")
    
    # 结果信息
    success_count = Column(Integer, default=0, comment="成功部署数量")
    failure_count = Column(Integer, default=0, comment="失败部署数量")
    error_message = Column(Text, nullable=True, comment="错误消息")
    
    # 时间信息
    scheduled_at = Column(DateTime(timezone=True), nullable=True, comment="计划部署时间")
    started_at = Column(DateTime(timezone=True), nullable=True, comment="开始部署时间")
    completed_at = Column(DateTime(timezone=True), nullable=True, comment="完成部署时间")
    
    # 操作者信息
    deployed_by = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=True, comment="部署者用户ID")
    
    # 关联关系
    application = relationship("Application")
    version = relationship("ApplicationVersion")
    deployer = relationship("User")
    
    def __repr__(self):
        return f"<ApplicationDeployment(id={self.id}, application_id={self.application_id}, status='{self.status}')>"


class ApplicationAnalytics(BaseModel):
    """应用分析数据模型"""
    
    __tablename__ = get_table_name('application_analytics')
    
    application_id = Column(Integer, ForeignKey(f'{get_table_name("application")}.id'), nullable=False, comment="应用ID")
    device_id = Column(Integer, ForeignKey(f'{get_table_name("device")}.id'), nullable=True, comment="设备ID")
    user_id = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=True, comment="用户ID")
    
    # 事件信息
    event_type = Column(String(50), nullable=False, comment="事件类型")  # launch, close, crash, error, etc.
    event_data = Column(JSON, nullable=True, comment="事件数据JSON")
    
    # 会话信息
    session_id = Column(String(100), nullable=True, comment="会话ID")
    session_duration = Column(Integer, nullable=True, comment="会话时长（秒）")
    
    # 性能信息
    cpu_usage = Column(Float, nullable=True, comment="CPU使用率")
    memory_usage = Column(Float, nullable=True, comment="内存使用量（MB）")
    battery_usage = Column(Float, nullable=True, comment="电池使用量")
    
    # 时间戳
    timestamp = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), comment="时间戳")
    
    # 关联关系
    application = relationship("Application")
    device = relationship("Device")
    user = relationship("User")
    
    def __repr__(self):
        return f"<ApplicationAnalytics(id={self.id}, application_id={self.application_id}, event_type='{self.event_type}')>"
