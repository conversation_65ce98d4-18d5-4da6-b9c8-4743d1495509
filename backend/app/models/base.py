"""
AR-System 基础数据模型
"""
from datetime import datetime, timezone
from sqlalchemy import Column, Integer, DateTime, Boolean, String, Text
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import declarative_base

Base = declarative_base()


class TimestampMixin:
    """时间戳混入类"""
    
    created_at = Column(
        DateTime(timezone=True), 
        default=lambda: datetime.now(timezone.utc),
        nullable=False,
        comment="创建时间"
    )
    
    updated_at = Column(
        DateTime(timezone=True), 
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        nullable=False,
        comment="更新时间"
    )


class SoftDeleteMixin:
    """软删除混入类"""
    
    is_deleted = Column(
        Boolean, 
        default=False, 
        nullable=False,
        comment="是否已删除"
    )
    
    deleted_at = Column(
        DateTime(timezone=True), 
        nullable=True,
        comment="删除时间"
    )


class BaseModel(Base, TimestampMixin):
    """基础模型类"""
    
    __abstract__ = True
    
    id = Column(
        Integer, 
        primary_key=True, 
        index=True, 
        autoincrement=True,
        comment="主键ID"
    )
    
    @declared_attr
    def __tablename__(cls):
        """自动生成表名"""
        return cls.__name__.lower()
    
    def to_dict(self):
        """转换为字典"""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }
    
    def update_from_dict(self, data: dict):
        """从字典更新属性"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)


class BaseModelWithSoftDelete(BaseModel, SoftDeleteMixin):
    """带软删除的基础模型类"""
    
    __abstract__ = True
    
    def soft_delete(self):
        """软删除"""
        self.is_deleted = True
        self.deleted_at = datetime.now(timezone.utc)
    
    def restore(self):
        """恢复删除"""
        self.is_deleted = False
        self.deleted_at = None


class StatusMixin:
    """状态混入类"""
    
    is_active = Column(
        Boolean, 
        default=True, 
        nullable=False,
        comment="是否激活"
    )
    
    status = Column(
        String(50), 
        default="active",
        nullable=False,
        comment="状态"
    )


class MetadataMixin:
    """元数据混入类"""
    
    description = Column(
        Text,
        nullable=True,
        comment="描述"
    )
    
    tags = Column(
        String(500),
        nullable=True,
        comment="标签（逗号分隔）"
    )
    
    metadata_json = Column(
        Text,
        nullable=True,
        comment="JSON格式的元数据"
    )


class AuditMixin:
    """审计混入类"""
    
    created_by = Column(
        Integer,
        nullable=True,
        comment="创建者ID"
    )
    
    updated_by = Column(
        Integer,
        nullable=True,
        comment="更新者ID"
    )
    
    version = Column(
        Integer,
        default=1,
        nullable=False,
        comment="版本号"
    )


class FullBaseModel(BaseModel, StatusMixin, MetadataMixin, AuditMixin):
    """完整的基础模型类"""
    
    __abstract__ = True
    
    name = Column(
        String(255),
        nullable=False,
        comment="名称"
    )
    
    display_name = Column(
        String(255),
        nullable=True,
        comment="显示名称"
    )


# 常用的枚举类型
class StatusEnum:
    """状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    SUSPENDED = "suspended"
    DELETED = "deleted"


class DeviceStatusEnum:
    """设备状态枚举"""
    ONLINE = "online"
    OFFLINE = "offline"
    MAINTENANCE = "maintenance"
    ERROR = "error"


class OrderStatusEnum:
    """订单状态枚举"""
    PENDING = "pending"
    PAID = "paid"
    PROCESSING = "processing"
    SHIPPED = "shipped"
    DELIVERED = "delivered"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"


class ApplicationStatusEnum:
    """应用状态枚举"""
    DRAFT = "draft"
    PUBLISHED = "published"
    DEPRECATED = "deprecated"
    REMOVED = "removed"


class UserStatusEnum:
    """用户状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING_VERIFICATION = "pending_verification"


# 数据库表前缀
TABLE_PREFIX = "ar_"


def get_table_name(name: str) -> str:
    """获取带前缀的表名"""
    return f"{TABLE_PREFIX}{name}"


# 常用的数据库约束
class DatabaseConstraints:
    """数据库约束常量"""
    
    # 字符串长度
    SHORT_STRING = 50
    MEDIUM_STRING = 255
    LONG_STRING = 500
    TEXT_LENGTH = 2000
    
    # 数值范围
    SMALL_INT_MAX = 32767
    INT_MAX = 2147483647
    
    # 精度
    DECIMAL_PRECISION = 10
    DECIMAL_SCALE = 2
    
    # 索引名称长度限制
    INDEX_NAME_MAX_LENGTH = 63
