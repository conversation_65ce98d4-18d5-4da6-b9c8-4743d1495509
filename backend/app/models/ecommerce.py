"""
AR-System 电商管理数据模型
"""
from sqlalchemy import Column, Integer, String, Boolean, Text, ForeignKey, DateTime, Float, JSON, Table
from sqlalchemy.types import Numeric
from sqlalchemy.orm import relationship
from datetime import datetime, timezone
import enum

from app.models.base import BaseModel, StatusMixin, get_table_name, OrderStatusEnum


class ProductCategory(BaseModel, StatusMixin):
    """商品分类模型"""
    
    __tablename__ = get_table_name('product_category')
    
    name = Column(String(255), nullable=False, comment="分类名称")
    slug = Column(String(255), unique=True, nullable=False, index=True, comment="分类标识")
    description = Column(Text, nullable=True, comment="分类描述")
    
    # 层级结构
    parent_id = Column(Integer, ForeignKey(f'{get_table_name("product_category")}.id'), nullable=True, comment="父分类ID")
    level = Column(Integer, default=0, comment="分类层级")
    sort_order = Column(Integer, default=0, comment="排序顺序")
    
    # 显示信息
    icon_url = Column(String(500), nullable=True, comment="图标URL")
    banner_url = Column(String(500), nullable=True, comment="横幅URL")
    
    # 关联关系
    parent = relationship("ProductCategory", remote_side="ProductCategory.id")
    children = relationship("ProductCategory")
    products = relationship("Product", back_populates="category")
    
    def __repr__(self):
        return f"<ProductCategory(id={self.id}, name='{self.name}', slug='{self.slug}')>"


class Product(BaseModel, StatusMixin):
    """商品模型"""
    
    __tablename__ = get_table_name('product')
    
    # 基本信息
    sku = Column(String(100), unique=True, nullable=False, index=True, comment="商品SKU")
    name = Column(String(255), nullable=False, comment="商品名称")
    slug = Column(String(255), unique=True, nullable=False, index=True, comment="商品标识")
    description = Column(Text, nullable=True, comment="商品描述")
    short_description = Column(Text, nullable=True, comment="简短描述")
    
    # 分类信息
    category_id = Column(Integer, ForeignKey(f'{get_table_name("product_category")}.id'), nullable=False, comment="分类ID")
    brand = Column(String(100), nullable=True, comment="品牌")
    model = Column(String(100), nullable=True, comment="型号")
    
    # 价格信息
    price = Column(Numeric(10, 2), nullable=False, comment="价格")
    original_price = Column(Numeric(10, 2), nullable=True, comment="原价")
    cost_price = Column(Numeric(10, 2), nullable=True, comment="成本价")
    
    # 库存信息
    stock_quantity = Column(Integer, default=0, comment="库存数量")
    min_stock_level = Column(Integer, default=0, comment="最低库存水平")
    max_stock_level = Column(Integer, nullable=True, comment="最高库存水平")
    
    # 物理属性
    weight = Column(Float, nullable=True, comment="重量（克）")
    dimensions = Column(JSON, nullable=True, comment="尺寸JSON（长宽高）")
    
    # 媒体文件
    images = Column(JSON, nullable=True, comment="图片URL列表JSON")
    videos = Column(JSON, nullable=True, comment="视频URL列表JSON")
    documents = Column(JSON, nullable=True, comment="文档URL列表JSON")
    
    # 规格参数
    specifications = Column(JSON, nullable=True, comment="规格参数JSON")
    features = Column(JSON, nullable=True, comment="特性列表JSON")
    
    # 销售信息
    is_featured = Column(Boolean, default=False, comment="是否推荐商品")
    is_digital = Column(Boolean, default=False, comment="是否数字商品")
    requires_shipping = Column(Boolean, default=True, comment="是否需要配送")
    
    # 统计信息
    view_count = Column(Integer, default=0, comment="浏览次数")
    sales_count = Column(Integer, default=0, comment="销售数量")
    rating_average = Column(Float, default=0.0, comment="平均评分")
    rating_count = Column(Integer, default=0, comment="评分次数")
    
    # SEO信息
    meta_title = Column(String(255), nullable=True, comment="SEO标题")
    meta_description = Column(Text, nullable=True, comment="SEO描述")
    meta_keywords = Column(String(500), nullable=True, comment="SEO关键词")
    
    # 关联关系
    category = relationship("ProductCategory", back_populates="products")
    variants = relationship("ProductVariant", back_populates="product")
    reviews = relationship("ProductReview", back_populates="product")
    order_items = relationship("OrderItem", back_populates="product")
    
    def __repr__(self):
        return f"<Product(id={self.id}, sku='{self.sku}', name='{self.name}', price={self.price})>"
    
    def is_in_stock(self) -> bool:
        """检查是否有库存"""
        return self.stock_quantity > 0
    
    def is_low_stock(self) -> bool:
        """检查是否库存不足"""
        return self.stock_quantity <= self.min_stock_level


class ProductVariant(BaseModel, StatusMixin):
    """商品变体模型"""
    
    __tablename__ = get_table_name('product_variant')
    
    product_id = Column(Integer, ForeignKey(f'{get_table_name("product")}.id'), nullable=False, comment="商品ID")
    sku = Column(String(100), unique=True, nullable=False, index=True, comment="变体SKU")
    name = Column(String(255), nullable=False, comment="变体名称")
    
    # 变体属性
    attributes = Column(JSON, nullable=True, comment="变体属性JSON")  # 如：{"color": "red", "size": "L"}
    
    # 价格和库存
    price = Column(Numeric(10, 2), nullable=True, comment="变体价格")
    stock_quantity = Column(Integer, default=0, comment="变体库存")
    
    # 媒体文件
    images = Column(JSON, nullable=True, comment="变体图片JSON")
    
    # 关联关系
    product = relationship("Product", back_populates="variants")
    
    def __repr__(self):
        return f"<ProductVariant(id={self.id}, sku='{self.sku}', name='{self.name}')>"


class ProductReview(BaseModel):
    """商品评价模型"""
    
    __tablename__ = get_table_name('product_review')
    
    product_id = Column(Integer, ForeignKey(f'{get_table_name("product")}.id'), nullable=False, comment="商品ID")
    user_id = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=False, comment="用户ID")
    order_id = Column(Integer, ForeignKey(f'{get_table_name("order")}.id'), nullable=True, comment="订单ID")
    
    # 评价内容
    rating = Column(Integer, nullable=False, comment="评分（1-5）")
    title = Column(String(255), nullable=True, comment="评价标题")
    content = Column(Text, nullable=True, comment="评价内容")
    
    # 媒体文件
    images = Column(JSON, nullable=True, comment="评价图片JSON")
    videos = Column(JSON, nullable=True, comment="评价视频JSON")
    
    # 状态信息
    is_verified = Column(Boolean, default=False, comment="是否已验证")
    is_anonymous = Column(Boolean, default=False, comment="是否匿名评价")
    helpful_count = Column(Integer, default=0, comment="有帮助的投票数")
    
    # 关联关系
    product = relationship("Product", back_populates="reviews")
    user = relationship("User")
    # order = relationship("Order")  # 在下面定义
    
    def __repr__(self):
        return f"<ProductReview(id={self.id}, product_id={self.product_id}, user_id={self.user_id}, rating={self.rating})>"


class Order(BaseModel, StatusMixin):
    """订单模型"""
    
    __tablename__ = get_table_name('order')
    
    # 订单基本信息
    order_number = Column(String(100), unique=True, nullable=False, index=True, comment="订单号")
    user_id = Column(Integer, ForeignKey(f'{get_table_name("user")}.id'), nullable=False, comment="用户ID")
    
    # 订单状态
    order_status = Column(String(20), default=OrderStatusEnum.PENDING, comment="订单状态")
    payment_status = Column(String(20), default="pending", comment="支付状态")
    shipping_status = Column(String(20), default="pending", comment="配送状态")
    
    # 金额信息
    subtotal = Column(Numeric(10, 2), nullable=False, comment="小计金额")
    tax_amount = Column(Numeric(10, 2), default=0, comment="税费")
    shipping_amount = Column(Numeric(10, 2), default=0, comment="配送费")
    discount_amount = Column(Numeric(10, 2), default=0, comment="折扣金额")
    total_amount = Column(Numeric(10, 2), nullable=False, comment="总金额")
    
    # 收货信息
    shipping_address = Column(JSON, nullable=True, comment="收货地址JSON")
    billing_address = Column(JSON, nullable=True, comment="账单地址JSON")
    
    # 联系信息
    customer_name = Column(String(255), nullable=True, comment="客户姓名")
    customer_email = Column(String(255), nullable=True, comment="客户邮箱")
    customer_phone = Column(String(20), nullable=True, comment="客户电话")
    
    # 备注信息
    notes = Column(Text, nullable=True, comment="订单备注")
    internal_notes = Column(Text, nullable=True, comment="内部备注")
    
    # 时间信息
    ordered_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), comment="下单时间")
    paid_at = Column(DateTime(timezone=True), nullable=True, comment="支付时间")
    shipped_at = Column(DateTime(timezone=True), nullable=True, comment="发货时间")
    delivered_at = Column(DateTime(timezone=True), nullable=True, comment="送达时间")
    
    # 关联关系
    user = relationship("User", back_populates="orders")
    items = relationship("OrderItem", back_populates="order")
    payments = relationship("Payment", back_populates="order")
    shipments = relationship("Shipment", back_populates="order")
    
    def __repr__(self):
        return f"<Order(id={self.id}, order_number='{self.order_number}', status='{self.order_status}', total={self.total_amount})>"
    
    def calculate_total(self):
        """计算订单总金额"""
        self.total_amount = self.subtotal + self.tax_amount + self.shipping_amount - self.discount_amount


class OrderItem(BaseModel):
    """订单项模型"""
    
    __tablename__ = get_table_name('order_item')
    
    order_id = Column(Integer, ForeignKey(f'{get_table_name("order")}.id'), nullable=False, comment="订单ID")
    product_id = Column(Integer, ForeignKey(f'{get_table_name("product")}.id'), nullable=False, comment="商品ID")
    variant_id = Column(Integer, ForeignKey(f'{get_table_name("product_variant")}.id'), nullable=True, comment="变体ID")
    
    # 商品信息快照
    product_name = Column(String(255), nullable=False, comment="商品名称快照")
    product_sku = Column(String(100), nullable=False, comment="商品SKU快照")
    product_image = Column(String(500), nullable=True, comment="商品图片快照")
    
    # 数量和价格
    quantity = Column(Integer, nullable=False, comment="数量")
    unit_price = Column(Numeric(10, 2), nullable=False, comment="单价")
    total_price = Column(Numeric(10, 2), nullable=False, comment="总价")
    
    # 关联关系
    order = relationship("Order", back_populates="items")
    product = relationship("Product", back_populates="order_items")
    variant = relationship("ProductVariant")
    
    def __repr__(self):
        return f"<OrderItem(id={self.id}, order_id={self.order_id}, product_id={self.product_id}, quantity={self.quantity})>"


class Payment(BaseModel):
    """支付记录模型"""
    
    __tablename__ = get_table_name('payment')
    
    order_id = Column(Integer, ForeignKey(f'{get_table_name("order")}.id'), nullable=False, comment="订单ID")
    
    # 支付信息
    payment_method = Column(String(50), nullable=False, comment="支付方式")
    payment_provider = Column(String(50), nullable=True, comment="支付提供商")
    transaction_id = Column(String(255), nullable=True, comment="交易ID")
    
    # 金额信息
    amount = Column(Numeric(10, 2), nullable=False, comment="支付金额")
    currency = Column(String(3), default="CNY", comment="货币")
    
    # 状态信息
    status = Column(String(20), default="pending", comment="支付状态")
    
    # 时间信息
    paid_at = Column(DateTime(timezone=True), nullable=True, comment="支付时间")
    
    # 额外信息
    gateway_response = Column(JSON, nullable=True, comment="网关响应JSON")
    
    # 关联关系
    order = relationship("Order", back_populates="payments")
    
    def __repr__(self):
        return f"<Payment(id={self.id}, order_id={self.order_id}, amount={self.amount}, status='{self.status}')>"


class Shipment(BaseModel):
    """配送记录模型"""
    
    __tablename__ = get_table_name('shipment')
    
    order_id = Column(Integer, ForeignKey(f'{get_table_name("order")}.id'), nullable=False, comment="订单ID")
    
    # 配送信息
    tracking_number = Column(String(100), nullable=True, comment="快递单号")
    carrier = Column(String(100), nullable=True, comment="承运商")
    shipping_method = Column(String(50), nullable=True, comment="配送方式")
    
    # 地址信息
    shipping_address = Column(JSON, nullable=True, comment="配送地址JSON")
    
    # 状态信息
    status = Column(String(20), default="pending", comment="配送状态")
    
    # 时间信息
    shipped_at = Column(DateTime(timezone=True), nullable=True, comment="发货时间")
    estimated_delivery = Column(DateTime(timezone=True), nullable=True, comment="预计送达时间")
    delivered_at = Column(DateTime(timezone=True), nullable=True, comment="实际送达时间")
    
    # 关联关系
    order = relationship("Order", back_populates="shipments")
    
    def __repr__(self):
        return f"<Shipment(id={self.id}, order_id={self.order_id}, tracking_number='{self.tracking_number}', status='{self.status}')>"


# 更新ProductReview模型以包含order关系
ProductReview.order = relationship("Order")
