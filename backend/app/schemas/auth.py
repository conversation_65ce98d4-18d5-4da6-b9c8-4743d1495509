"""
AR-System 认证相关的Pydantic模式
"""
from pydantic import BaseModel, EmailStr, field_validator
from typing import Optional, List
from datetime import datetime


class LoginRequest(BaseModel):
    """登录请求模式"""
    username: str
    password: str
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    
    @field_validator('username')
    @classmethod
    def username_must_not_be_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('用户名不能为空')
        return v.strip()

    @field_validator('password')
    @classmethod
    def password_must_not_be_empty(cls, v):
        if not v or len(v) < 6:
            raise ValueError('密码长度不能少于6位')
        return v


class RegisterRequest(BaseModel):
    """注册请求模式"""
    username: str
    email: EmailStr
    password: str
    full_name: Optional[str] = None
    
    @field_validator('username')
    @classmethod
    def username_validation(cls, v):
        if not v or not v.strip():
            raise ValueError('用户名不能为空')
        if len(v.strip()) < 3:
            raise ValueError('用户名长度不能少于3位')
        if len(v.strip()) > 50:
            raise ValueError('用户名长度不能超过50位')
        return v.strip()

    @field_validator('password')
    @classmethod
    def password_validation(cls, v):
        if not v or len(v) < 8:
            raise ValueError('密码长度不能少于8位')
        if len(v) > 128:
            raise ValueError('密码长度不能超过128位')
        return v

    @field_validator('full_name')
    @classmethod
    def full_name_validation(cls, v):
        if v and len(v.strip()) > 255:
            raise ValueError('全名长度不能超过255位')
        return v.strip() if v else None


class RefreshTokenRequest(BaseModel):
    """刷新令牌请求模式"""
    refresh_token: str


class ChangePasswordRequest(BaseModel):
    """修改密码请求模式"""
    old_password: str
    new_password: str
    
    @field_validator('new_password')
    @classmethod
    def new_password_validation(cls, v):
        if not v or len(v) < 8:
            raise ValueError('新密码长度不能少于8位')
        if len(v) > 128:
            raise ValueError('新密码长度不能超过128位')
        return v


class UserResponse(BaseModel):
    """用户响应模式"""
    id: int
    username: str
    email: str
    full_name: Optional[str] = None
    avatar_url: Optional[str] = None
    is_active: bool
    is_verified: bool
    is_superuser: bool
    phone: Optional[str] = None
    bio: Optional[str] = None
    location: Optional[str] = None
    website: Optional[str] = None
    language: str = "zh-CN"
    timezone: str = "Asia/Shanghai"
    theme: str = "light"
    two_factor_enabled: bool = False
    last_login_at: Optional[datetime] = None
    login_count: int = 0
    created_at: datetime
    updated_at: datetime
    
    model_config = {"from_attributes": True}


class LoginResponse(BaseModel):
    """登录响应模式"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserResponse


class TokenPayload(BaseModel):
    """令牌载荷模式"""
    user_id: int
    username: str
    email: str
    is_superuser: bool
    permissions: List[str] = []
    exp: Optional[int] = None
    type: str = "access"


class PermissionResponse(BaseModel):
    """权限响应模式"""
    id: int
    name: str
    description: Optional[str] = None
    resource: str
    action: str
    
    model_config = {"from_attributes": True}


class RoleResponse(BaseModel):
    """角色响应模式"""
    id: int
    name: str
    display_name: str
    description: Optional[str] = None
    is_active: bool
    permissions: List[PermissionResponse] = []
    created_at: datetime
    updated_at: datetime
    
    model_config = {"from_attributes": True}


class UserDetailResponse(UserResponse):
    """用户详细信息响应模式"""
    roles: List[RoleResponse] = []
    permissions: List[str] = []


class UserCreateRequest(BaseModel):
    """创建用户请求模式"""
    username: str
    email: EmailStr
    password: str
    full_name: Optional[str] = None
    is_active: bool = True
    is_superuser: bool = False
    role_ids: List[int] = []
    
    @field_validator('username')
    @classmethod
    def username_validation(cls, v):
        if not v or not v.strip():
            raise ValueError('用户名不能为空')
        if len(v.strip()) < 3:
            raise ValueError('用户名长度不能少于3位')
        if len(v.strip()) > 50:
            raise ValueError('用户名长度不能超过50位')
        return v.strip()


class UserUpdateRequest(BaseModel):
    """更新用户请求模式"""
    full_name: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    bio: Optional[str] = None
    location: Optional[str] = None
    website: Optional[str] = None
    language: Optional[str] = None
    timezone: Optional[str] = None
    theme: Optional[str] = None
    is_active: Optional[bool] = None
    role_ids: Optional[List[int]] = None
    
    @field_validator('phone')
    @classmethod
    def phone_validation(cls, v):
        if v and len(v.strip()) > 20:
            raise ValueError('手机号长度不能超过20位')
        return v.strip() if v else None

    @field_validator('bio')
    @classmethod
    def bio_validation(cls, v):
        if v and len(v.strip()) > 500:
            raise ValueError('个人简介长度不能超过500位')
        return v.strip() if v else None

    @field_validator('website')
    @classmethod
    def website_validation(cls, v):
        if v and len(v.strip()) > 500:
            raise ValueError('网站链接长度不能超过500位')
        return v.strip() if v else None


class UserListResponse(BaseModel):
    """用户列表响应模式"""
    users: List[UserResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


class SessionResponse(BaseModel):
    """会话响应模式"""
    id: int
    user_id: int
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    device_info: Optional[str] = None
    expires_at: datetime
    last_activity_at: datetime
    is_active: bool
    created_at: datetime
    
    model_config = {"from_attributes": True}


class ActivityResponse(BaseModel):
    """活动记录响应模式"""
    id: int
    user_id: int
    action: str
    resource: Optional[str] = None
    resource_id: Optional[str] = None
    details: Optional[str] = None
    ip_address: Optional[str] = None
    created_at: datetime
    
    model_config = {"from_attributes": True}
