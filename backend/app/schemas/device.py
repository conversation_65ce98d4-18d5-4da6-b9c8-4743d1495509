"""
AR-System 设备相关的Pydantic模式
"""
from pydantic import BaseModel, field_validator
from typing import Optional, List, Dict, Any
from datetime import datetime


class DeviceCreateRequest(BaseModel):
    """创建设备请求模式"""
    device_id: str
    name: str
    model: str
    manufacturer: str
    serial_number: Optional[str] = None
    device_type: str
    hardware_version: Optional[str] = None
    firmware_version: Optional[str] = None
    os_version: Optional[str] = None
    
    @field_validator('device_id')
    @classmethod
    def device_id_validation(cls, v):
        if not v or not v.strip():
            raise ValueError('设备ID不能为空')
        if len(v.strip()) > 100:
            raise ValueError('设备ID长度不能超过100位')
        return v.strip()

    @field_validator('name')
    @classmethod
    def name_validation(cls, v):
        if not v or not v.strip():
            raise ValueError('设备名称不能为空')
        if len(v.strip()) > 255:
            raise ValueError('设备名称长度不能超过255位')
        return v.strip()


class DeviceUpdateRequest(BaseModel):
    """更新设备请求模式"""
    name: Optional[str] = None
    device_status: Optional[str] = None
    battery_level: Optional[int] = None
    temperature: Optional[float] = None
    ip_address: Optional[str] = None
    mac_address: Optional[str] = None
    wifi_ssid: Optional[str] = None
    signal_strength: Optional[int] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    altitude: Optional[float] = None
    location_name: Optional[str] = None
    cpu_usage: Optional[float] = None
    memory_usage: Optional[float] = None
    storage_usage: Optional[float] = None
    configuration: Optional[Dict[str, Any]] = None
    capabilities: Optional[Dict[str, Any]] = None
    
    @field_validator('battery_level')
    @classmethod
    def battery_level_validation(cls, v):
        if v is not None and (v < 0 or v > 100):
            raise ValueError('电池电量必须在0-100之间')
        return v

    @field_validator('signal_strength')
    @classmethod
    def signal_strength_validation(cls, v):
        if v is not None and (v < -100 or v > 0):
            raise ValueError('信号强度必须在-100到0之间')
        return v


class DeviceResponse(BaseModel):
    """设备响应模式"""
    id: int
    device_id: str
    name: str
    model: str
    manufacturer: str
    serial_number: Optional[str] = None
    device_type: str
    hardware_version: Optional[str] = None
    firmware_version: Optional[str] = None
    os_version: Optional[str] = None
    device_status: str
    battery_level: Optional[int] = None
    temperature: Optional[float] = None
    ip_address: Optional[str] = None
    mac_address: Optional[str] = None
    wifi_ssid: Optional[str] = None
    signal_strength: Optional[int] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    altitude: Optional[float] = None
    location_name: Optional[str] = None
    cpu_usage: Optional[float] = None
    memory_usage: Optional[float] = None
    storage_usage: Optional[float] = None
    last_seen_at: Optional[datetime] = None
    first_connected_at: Optional[datetime] = None
    configuration: Optional[Dict[str, Any]] = None
    capabilities: Optional[Dict[str, Any]] = None
    owner_id: Optional[int] = None
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    model_config = {"from_attributes": True}


class DeviceListResponse(BaseModel):
    """设备列表响应模式"""
    devices: List[DeviceResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


class DeviceMetricResponse(BaseModel):
    """设备指标响应模式"""
    id: int
    device_id: int
    metric_name: str
    metric_value: float
    metric_unit: Optional[str] = None
    timestamp: datetime

    model_config = {"from_attributes": True}


class DeviceLogResponse(BaseModel):
    """设备日志响应模式"""
    id: int
    device_id: int
    log_level: str
    message: str
    source: Optional[str] = None
    error_code: Optional[str] = None
    stack_trace: Optional[str] = None
    context: Optional[Dict[str, Any]] = None
    timestamp: datetime

    model_config = {"from_attributes": True}


class DeviceCommandRequest(BaseModel):
    """设备命令请求模式"""
    command_type: str
    command_data: Optional[Dict[str, Any]] = None
    
    @field_validator('command_type')
    @classmethod
    def command_type_validation(cls, v):
        if not v or not v.strip():
            raise ValueError('命令类型不能为空')
        return v.strip()


class DeviceCommandResponse(BaseModel):
    """设备命令响应模式"""
    id: int
    device_id: int
    command_type: str
    command_data: Optional[Dict[str, Any]] = None
    status: str
    result: Optional[str] = None
    error_message: Optional[str] = None
    sent_at: datetime
    executed_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    sender_id: Optional[int] = None
    
    model_config = {"from_attributes": True}


class DeviceGroupCreateRequest(BaseModel):
    """创建设备组请求模式"""
    name: str
    description: Optional[str] = None
    group_type: str
    configuration: Optional[Dict[str, Any]] = None

    @field_validator('name')
    @classmethod
    def name_validation(cls, v):
        if not v or not v.strip():
            raise ValueError('组名称不能为空')
        if len(v.strip()) > 255:
            raise ValueError('组名称长度不能超过255位')
        return v.strip()


class DeviceGroupResponse(BaseModel):
    """设备组响应模式"""
    id: int
    name: str
    description: Optional[str] = None
    group_type: str
    configuration: Optional[Dict[str, Any]] = None
    is_active: bool
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class DeviceStatsResponse(BaseModel):
    """设备统计响应模式"""
    total_devices: int
    online_devices: int
    offline_devices: int
    error_devices: int
    device_types: Dict[str, int]
    manufacturers: Dict[str, int]
    recent_activities: List[Dict[str, Any]]


class DeviceHealthResponse(BaseModel):
    """设备健康状态响应模式"""
    device_id: int
    health_score: float  # 0-100
    issues: List[str]
    recommendations: List[str]
    last_check: datetime
    
    model_config = {"from_attributes": True}
