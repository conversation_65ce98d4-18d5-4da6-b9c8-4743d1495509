"""
权限管理相关的Pydantic模型
"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


class PermissionBase(BaseModel):
    """权限基础模型"""
    name: str = Field(..., description="权限名称", example="user:read")
    display_name: str = Field(..., description="权限显示名称", example="查看用户")
    description: Optional[str] = Field(None, description="权限描述")
    resource: str = Field(..., description="资源类型", example="user")
    action: str = Field(..., description="操作类型", example="read")
    module: Optional[str] = Field(None, description="所属模块", example="user_management")


class PermissionCreate(PermissionBase):
    """创建权限请求模型"""
    pass


class PermissionUpdate(BaseModel):
    """更新权限请求模型"""
    display_name: Optional[str] = None
    description: Optional[str] = None
    module: Optional[str] = None


class PermissionResponse(PermissionBase):
    """权限响应模型"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class RoleBase(BaseModel):
    """角色基础模型"""
    name: str = Field(..., description="角色名称", example="admin")
    display_name: str = Field(..., description="角色显示名称", example="管理员")
    description: Optional[str] = Field(None, description="角色描述")
    is_system: bool = Field(False, description="是否系统角色")


class RoleCreate(RoleBase):
    """创建角色请求模型"""
    permission_ids: List[int] = Field(default=[], description="权限ID列表")


class RoleUpdate(BaseModel):
    """更新角色请求模型"""
    display_name: Optional[str] = None
    description: Optional[str] = None
    permission_ids: Optional[List[int]] = None


class RoleResponse(RoleBase):
    """角色响应模型"""
    id: int
    permissions: List[PermissionResponse] = []
    user_count: int = Field(0, description="拥有此角色的用户数量")
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class UserPermissionResponse(BaseModel):
    """用户权限响应模型"""
    user_id: int
    username: str
    roles: List[RoleResponse] = []
    permissions: List[PermissionResponse] = []
    effective_permissions: List[str] = Field([], description="有效权限列表")


class PermissionCheckRequest(BaseModel):
    """权限检查请求模型"""
    user_id: int
    permission: str = Field(..., description="权限名称", example="user:read")
    resource_id: Optional[str] = Field(None, description="资源ID")


class PermissionCheckResponse(BaseModel):
    """权限检查响应模型"""
    has_permission: bool
    reason: Optional[str] = Field(None, description="权限检查结果说明")


class RolePermissionBatchRequest(BaseModel):
    """批量分配角色权限请求模型"""
    role_id: int
    permission_ids: List[int]
    action: str = Field(..., description="操作类型: add/remove/replace")


class UserRoleBatchRequest(BaseModel):
    """批量分配用户角色请求模型"""
    user_id: int
    role_ids: List[int]
    action: str = Field(..., description="操作类型: add/remove/replace")


class PermissionTreeNode(BaseModel):
    """权限树节点模型"""
    id: int
    name: str
    display_name: str
    type: str = Field(..., description="节点类型: module/resource/action")
    children: List['PermissionTreeNode'] = []
    checked: bool = Field(False, description="是否选中")
    parent_id: Optional[int] = None


class PermissionMatrixResponse(BaseModel):
    """权限矩阵响应模型"""
    roles: List[RoleResponse]
    permissions: List[PermissionResponse]
    matrix: Dict[int, List[int]] = Field(..., description="角色-权限矩阵，key为角色ID，value为权限ID列表")


class PermissionStatsResponse(BaseModel):
    """权限统计响应模型"""
    total_permissions: int
    total_roles: int
    total_users_with_roles: int
    most_used_permissions: List[Dict[str, Any]]
    role_distribution: List[Dict[str, Any]]


# 权限常量定义
class PermissionConstants:
    """权限常量"""
    
    # 系统权限
    SYSTEM_ADMIN = "system:admin"
    SYSTEM_CONFIG = "system:config"
    
    # 用户管理权限
    USER_READ = "user:read"
    USER_CREATE = "user:create"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"
    USER_RESET_PASSWORD = "user:reset_password"
    
    # 角色管理权限
    ROLE_READ = "role:read"
    ROLE_CREATE = "role:create"
    ROLE_UPDATE = "role:update"
    ROLE_DELETE = "role:delete"
    ROLE_ASSIGN = "role:assign"
    
    # 权限管理权限
    PERMISSION_READ = "permission:read"
    PERMISSION_CREATE = "permission:create"
    PERMISSION_UPDATE = "permission:update"
    PERMISSION_DELETE = "permission:delete"
    
    # 设备管理权限
    DEVICE_READ = "device:read"
    DEVICE_CREATE = "device:create"
    DEVICE_UPDATE = "device:update"
    DEVICE_DELETE = "device:delete"
    DEVICE_CONTROL = "device:control"
    
    # 应用管理权限
    APP_READ = "app:read"
    APP_INSTALL = "app:install"
    APP_UNINSTALL = "app:uninstall"
    APP_UPDATE = "app:update"
    
    # 数据分析权限
    ANALYTICS_READ = "analytics:read"
    ANALYTICS_EXPORT = "analytics:export"
    
    # 系统监控权限
    MONITOR_READ = "monitor:read"
    MONITOR_CONTROL = "monitor:control"


# 预定义角色
class DefaultRoles:
    """默认角色定义"""
    
    SUPER_ADMIN = {
        "name": "super_admin",
        "display_name": "超级管理员",
        "description": "拥有系统所有权限",
        "is_system": True,
        "permissions": [
            PermissionConstants.SYSTEM_ADMIN,
            PermissionConstants.SYSTEM_CONFIG,
            # 包含所有权限
        ]
    }
    
    ADMIN = {
        "name": "admin",
        "display_name": "管理员",
        "description": "拥有大部分管理权限",
        "is_system": True,
        "permissions": [
            PermissionConstants.USER_READ,
            PermissionConstants.USER_CREATE,
            PermissionConstants.USER_UPDATE,
            PermissionConstants.ROLE_READ,
            PermissionConstants.DEVICE_READ,
            PermissionConstants.DEVICE_CREATE,
            PermissionConstants.DEVICE_UPDATE,
            PermissionConstants.APP_READ,
            PermissionConstants.ANALYTICS_READ,
            PermissionConstants.MONITOR_READ,
        ]
    }
    
    USER_MANAGER = {
        "name": "user_manager",
        "display_name": "用户管理员",
        "description": "负责用户和角色管理",
        "is_system": False,
        "permissions": [
            PermissionConstants.USER_READ,
            PermissionConstants.USER_CREATE,
            PermissionConstants.USER_UPDATE,
            PermissionConstants.ROLE_READ,
            PermissionConstants.ROLE_ASSIGN,
        ]
    }
    
    DEVICE_MANAGER = {
        "name": "device_manager",
        "display_name": "设备管理员",
        "description": "负责设备管理",
        "is_system": False,
        "permissions": [
            PermissionConstants.DEVICE_READ,
            PermissionConstants.DEVICE_CREATE,
            PermissionConstants.DEVICE_UPDATE,
            PermissionConstants.DEVICE_CONTROL,
        ]
    }
    
    VIEWER = {
        "name": "viewer",
        "display_name": "查看者",
        "description": "只读权限",
        "is_system": False,
        "permissions": [
            PermissionConstants.USER_READ,
            PermissionConstants.DEVICE_READ,
            PermissionConstants.APP_READ,
            PermissionConstants.ANALYTICS_READ,
            PermissionConstants.MONITOR_READ,
        ]
    }


# 更新PermissionTreeNode的前向引用
PermissionTreeNode.model_rebuild()
