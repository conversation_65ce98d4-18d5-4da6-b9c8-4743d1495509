"""
AR-System 安全认证模块
"""
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
import jwt
from passlib.context import Crypt<PERSON>ontext
from fastapi import HTTPException, status

from app.core.config import JWTConfig

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """生成密码哈希"""
    return pwd_context.hash(password)


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """创建访问令牌"""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(
            minutes=JWTConfig.get_access_token_expire_minutes()
        )
    
    to_encode.update({"exp": expire, "type": "access"})
    
    encoded_jwt = jwt.encode(
        to_encode, 
        JWTConfig.get_secret_key(), 
        algorithm=JWTConfig.get_algorithm()
    )
    
    return encoded_jwt


def create_refresh_token(data: Dict[str, Any]) -> str:
    """创建刷新令牌"""
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + timedelta(
        days=JWTConfig.get_refresh_token_expire_days()
    )
    
    to_encode.update({"exp": expire, "type": "refresh"})
    
    encoded_jwt = jwt.encode(
        to_encode,
        JWTConfig.get_secret_key(),
        algorithm=JWTConfig.get_algorithm()
    )
    
    return encoded_jwt


def verify_token(token: str) -> Dict[str, Any]:
    """验证令牌"""
    try:
        payload = jwt.decode(
            token,
            JWTConfig.get_secret_key(),
            algorithms=[JWTConfig.get_algorithm()]
        )
        
        # 检查令牌类型
        token_type = payload.get("type")
        if token_type not in ["access", "refresh"]:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token type"
            )
        
        # 检查过期时间
        exp = payload.get("exp")
        if exp is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token missing expiration"
            )
        
        if datetime.now(timezone.utc) > datetime.fromtimestamp(exp, timezone.utc):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token expired"
            )
        
        return payload
        
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token expired"
        )
    except jwt.PyJWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )


def decode_token(token: str) -> Optional[Dict[str, Any]]:
    """解码令牌（不验证过期时间）"""
    try:
        payload = jwt.decode(
            token,
            JWTConfig.get_secret_key(),
            algorithms=[JWTConfig.get_algorithm()],
            options={"verify_exp": False}
        )
        return payload
    except jwt.JWTError:
        return None


class TokenManager:
    """令牌管理器"""
    
    @staticmethod
    def generate_tokens(user_data: Dict[str, Any]) -> Dict[str, str]:
        """生成访问令牌和刷新令牌"""
        access_token = create_access_token(data=user_data)
        refresh_token = create_refresh_token(data=user_data)
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer"
        }
    
    @staticmethod
    def refresh_access_token(refresh_token: str) -> str:
        """使用刷新令牌生成新的访问令牌"""
        payload = verify_token(refresh_token)
        
        # 检查是否为刷新令牌
        if payload.get("type") != "refresh":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
        
        # 移除令牌特定字段
        user_data = {k: v for k, v in payload.items() if k not in ["exp", "type"]}
        
        return create_access_token(data=user_data)


class PermissionChecker:
    """权限检查器"""
    
    @staticmethod
    def check_permission(user_permissions: list, required_permission: str) -> bool:
        """检查用户是否有指定权限"""
        return required_permission in user_permissions
    
    @staticmethod
    def check_role(user_roles: list, required_role: str) -> bool:
        """检查用户是否有指定角色"""
        return required_role in user_roles
    
    @staticmethod
    def is_superuser(user_data: Dict[str, Any]) -> bool:
        """检查是否为超级用户"""
        return user_data.get("is_superuser", False)


class SecurityUtils:
    """安全工具类"""
    
    @staticmethod
    def generate_api_key() -> str:
        """生成API密钥"""
        import secrets
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def validate_password_strength(password: str) -> bool:
        """验证密码强度"""
        if len(password) < 8:
            return False
        
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
        
        return sum([has_upper, has_lower, has_digit, has_special]) >= 3
    
    @staticmethod
    def sanitize_input(input_str: str) -> str:
        """清理输入字符串"""
        import html
        return html.escape(input_str.strip())


# 权限装饰器
def require_permission(permission: str):
    """权限装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 这里需要从请求中获取用户信息
            # 实际实现时需要结合FastAPI的依赖注入
            pass
        return wrapper
    return decorator


def require_role(role: str):
    """角色装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 这里需要从请求中获取用户信息
            # 实际实现时需要结合FastAPI的依赖注入
            pass
        return wrapper
    return decorator
