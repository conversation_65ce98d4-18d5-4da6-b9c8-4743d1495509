"""
AR-System 数据库配置和连接管理
"""
from sqlalchemy import create_engine, MetaData
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from typing import AsyncGenerator

from app.core.config import settings, DatabaseConfig
from app.models.base import Base

# 检查是否使用SQLite
is_sqlite = settings.DATABASE_URL.startswith("sqlite")

# 同步数据库引擎
if is_sqlite:
    engine = create_engine(
        settings.DATABASE_URL,
        echo=settings.DATABASE_ECHO,
        connect_args={"check_same_thread": False}  # SQLite特殊配置
    )
else:
    engine = create_engine(
        DatabaseConfig.get_database_url(),
        echo=settings.DATABASE_ECHO,
        pool_pre_ping=True,
        pool_recycle=300,
    )

# 异步数据库引擎
if is_sqlite:
    # SQLite使用aiosqlite
    async_engine = create_async_engine(
        settings.DATABASE_URL.replace("sqlite://", "sqlite+aiosqlite://"),
        echo=settings.DATABASE_ECHO,
        connect_args={"check_same_thread": False}
    )
else:
    async_engine = create_async_engine(
        DatabaseConfig.get_async_database_url(),
        echo=settings.DATABASE_ECHO,
        pool_pre_ping=True,
        pool_recycle=300,
    )

# 会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
AsyncSessionLocal = async_sessionmaker(
    async_engine, 
    class_=AsyncSession, 
    expire_on_commit=False
)

# 元数据
metadata = MetaData()


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """获取异步数据库会话"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


def get_session():
    """获取同步数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def init_db():
    """初始化数据库"""
    try:
        # 导入所有模型以确保它们被注册到Base.metadata
        import app.models  # 这会触发__init__.py中的所有导入

        # 打印调试信息
        print(f"📋 发现 {len(Base.metadata.tables)} 个表:")
        for table_name in Base.metadata.tables.keys():
            print(f"   - {table_name}")

        # 创建所有表
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        print("✅ 数据库表创建完成")

        # 创建默认数据
        await create_default_data()

    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        raise


async def create_default_data():
    """创建默认数据"""
    try:
        from app.models.user import User, Role, Permission
        from app.core.security import get_password_hash
        from sqlalchemy import select

        async with AsyncSessionLocal() as session:
            # 检查是否已有管理员用户
            result = await session.execute(
                select(User).where(User.username == "admin")
            )
            admin_user = result.scalar_one_or_none()

            if not admin_user:
                # 创建默认权限
                permissions = [
                    Permission(name="user:read", description="查看用户", resource="user", action="read"),
                    Permission(name="user:write", description="编辑用户", resource="user", action="write"),
                    Permission(name="user:delete", description="删除用户", resource="user", action="delete"),
                    Permission(name="device:read", description="查看设备", resource="device", action="read"),
                    Permission(name="device:write", description="编辑设备", resource="device", action="write"),
                    Permission(name="device:delete", description="删除设备", resource="device", action="delete"),
                    Permission(name="application:read", description="查看应用", resource="application", action="read"),
                    Permission(name="application:write", description="编辑应用", resource="application", action="write"),
                    Permission(name="application:delete", description="删除应用", resource="application", action="delete"),
                    Permission(name="analytics:read", description="查看分析", resource="analytics", action="read"),
                    Permission(name="ecommerce:read", description="查看电商", resource="ecommerce", action="read"),
                    Permission(name="ecommerce:write", description="编辑电商", resource="ecommerce", action="write"),
                    Permission(name="community:read", description="查看社区", resource="community", action="read"),
                    Permission(name="community:write", description="编辑社区", resource="community", action="write"),
                ]

                for perm in permissions:
                    session.add(perm)

                # 创建默认角色
                admin_role = Role(
                    name="admin",
                    display_name="系统管理员",
                    description="拥有所有权限的系统管理员",
                    is_active=True
                )
                session.add(admin_role)

                user_role = Role(
                    name="user",
                    display_name="普通用户",
                    description="普通用户角色",
                    is_active=True
                )
                session.add(user_role)

                # 提交权限和角色
                await session.commit()

                # 重新查询角色和权限
                admin_role_result = await session.execute(
                    select(Role).where(Role.name == "admin")
                )
                admin_role = admin_role_result.scalar_one()

                permission_result = await session.execute(select(Permission))
                all_permissions = permission_result.scalars().all()

                # 给管理员角色分配所有权限
                admin_role.permissions = list(all_permissions)

                # 创建默认管理员用户
                admin_user = User(
                    username="admin",
                    email="<EMAIL>",
                    full_name="系统管理员",
                    hashed_password=get_password_hash("1234"),
                    is_active=True,
                    is_superuser=True,
                    is_staff=True
                )
                session.add(admin_user)
                await session.commit()

                # 给用户分配管理员角色
                admin_user.roles = [admin_role]
                await session.commit()

                print("✅ 默认数据创建完成")
                print(f"✅ 管理员账户: admin / 1234")

    except Exception as e:
        print(f"❌ 默认数据创建失败: {e}")
        import traceback
        traceback.print_exc()


async def close_db():
    """关闭数据库连接"""
    await async_engine.dispose()
    engine.dispose()


# 数据库健康检查
async def check_database_health() -> bool:
    """检查数据库连接健康状态"""
    try:
        async with AsyncSessionLocal() as session:
            await session.execute("SELECT 1")
        return True
    except Exception:
        return False


# 数据库迁移相关
class DatabaseMigration:
    """数据库迁移工具"""
    
    @staticmethod
    async def upgrade():
        """升级数据库结构"""
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
    
    @staticmethod
    async def downgrade():
        """降级数据库结构"""
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
    
    @staticmethod
    async def reset():
        """重置数据库"""
        await DatabaseMigration.downgrade()
        await DatabaseMigration.upgrade()
        await create_default_data()


# 事务装饰器
def transactional(func):
    """事务装饰器"""
    async def wrapper(*args, **kwargs):
        async with AsyncSessionLocal() as session:
            try:
                result = await func(session, *args, **kwargs)
                await session.commit()
                return result
            except Exception:
                await session.rollback()
                raise
    return wrapper
