"""
AR-System 核心配置模块
"""
import os
from typing import List, Optional
from pydantic import field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 基础配置
    PROJECT_NAME: str = "AR-System Backend API"
    VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 数据库配置
    DATABASE_URL: str = "postgresql://ar_user:ar_password@localhost:5432/ar_system"
    DATABASE_ECHO: bool = False
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379"
    REDIS_DB: int = 0
    
    # JWT配置
    SECRET_KEY: str = "ar-system-super-secret-key-2024"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # CORS配置
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:3002",  # web-frontend
        "http://localhost:3003",  # dashboard-frontend
        "http://localhost:3004",  # web-frontend (new port)
        "http://*************:3000",
        "http://*************:3001"
    ]
    
    # 文件上传配置
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: List[str] = [
        ".jpg", ".jpeg", ".png", ".gif", ".bmp",
        ".pdf", ".doc", ".docx", ".txt",
        ".zip", ".rar", ".7z"
    ]
    
    # 分页配置
    DEFAULT_PAGE_SIZE: int = 20
    MAX_PAGE_SIZE: int = 100
    
    # 缓存配置
    CACHE_EXPIRE_SECONDS: int = 300  # 5分钟
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 邮件配置
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    SMTP_TLS: bool = True
    
    # 第三方服务配置
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_BASE_URL: str = "https://api.openai.com/v1"
    
    @field_validator("CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v

    @field_validator("DATABASE_URL", mode="before")
    @classmethod
    def assemble_db_connection(cls, v):
        if isinstance(v, str):
            return v
        return str(v)
    
    model_config = {
        "env_file": ".env",
        "case_sensitive": True,
        "extra": "ignore"
    }


# 创建全局配置实例
settings = Settings()


# 数据库配置
class DatabaseConfig:
    """数据库配置类"""
    
    @staticmethod
    def get_database_url() -> str:
        """获取数据库连接URL"""
        return settings.DATABASE_URL
    
    @staticmethod
    def get_async_database_url() -> str:
        """获取异步数据库连接URL"""
        if settings.DATABASE_URL.startswith("sqlite"):
            return settings.DATABASE_URL.replace("sqlite://", "sqlite+aiosqlite://")
        else:
            return settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")


# Redis配置
class RedisConfig:
    """Redis配置类"""
    
    @staticmethod
    def get_redis_url() -> str:
        """获取Redis连接URL"""
        return f"{settings.REDIS_URL}/{settings.REDIS_DB}"


# JWT配置
class JWTConfig:
    """JWT配置类"""
    
    @staticmethod
    def get_secret_key() -> str:
        """获取JWT密钥"""
        return settings.SECRET_KEY
    
    @staticmethod
    def get_algorithm() -> str:
        """获取JWT算法"""
        return settings.ALGORITHM
    
    @staticmethod
    def get_access_token_expire_minutes() -> int:
        """获取访问令牌过期时间（分钟）"""
        return settings.ACCESS_TOKEN_EXPIRE_MINUTES
    
    @staticmethod
    def get_refresh_token_expire_days() -> int:
        """获取刷新令牌过期时间（天）"""
        return settings.REFRESH_TOKEN_EXPIRE_DAYS


# 环境检查
def is_development() -> bool:
    """检查是否为开发环境"""
    return settings.DEBUG or os.getenv("NODE_ENV") == "development"


def is_production() -> bool:
    """检查是否为生产环境"""
    return os.getenv("NODE_ENV") == "production"


def is_testing() -> bool:
    """检查是否为测试环境"""
    return os.getenv("NODE_ENV") == "testing"
