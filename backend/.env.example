# AR-System Backend 环境配置示例
# 复制此文件为 .env 并修改相应配置

# 基础配置
PROJECT_NAME=AR-System Backend API
VERSION=1.0.0
DEBUG=true
NODE_ENV=development

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=postgresql://ar_user:ar_password@localhost:5432/ar_system
DATABASE_ECHO=false

# Redis配置
REDIS_URL=redis://localhost:6379
REDIS_DB=0

# JWT配置
SECRET_KEY=ar-system-super-secret-key-2024-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://*************:3000,http://*************:3001

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
ALLOWED_EXTENSIONS=.jpg,.jpeg,.png,.gif,.bmp,.pdf,.doc,.docx,.txt,.zip,.rar,.7z

# 分页配置
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100

# 缓存配置
CACHE_EXPIRE_SECONDS=300

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# 邮件配置（可选）
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_TLS=true

# 第三方服务配置（可选）
OPENAI_API_KEY=
OPENAI_BASE_URL=https://api.openai.com/v1
