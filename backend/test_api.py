#!/usr/bin/env python3
"""
AR-System API 测试脚本
"""
import asyncio
import httpx
import json
from typing import Dict, Any


class APITester:
    """API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.access_token = None
        
    async def test_health(self):
        """测试健康检查"""
        print("🔍 测试健康检查...")
        
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{self.base_url}/health")
            
            if response.status_code == 200:
                print("✅ 健康检查通过")
                print(f"   响应: {response.json()}")
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                print(f"   响应: {response.text}")
    
    async def test_register(self, username: str = "testuser", email: str = "<EMAIL>", password: str = "testpass123"):
        """测试用户注册"""
        print("📝 测试用户注册...")
        
        data = {
            "username": username,
            "email": email,
            "password": password,
            "full_name": "Test User"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/auth/register",
                json=data
            )
            
            if response.status_code == 200:
                print("✅ 用户注册成功")
                user_data = response.json()
                print(f"   用户ID: {user_data.get('id')}")
                print(f"   用户名: {user_data.get('username')}")
            else:
                print(f"❌ 用户注册失败: {response.status_code}")
                print(f"   响应: {response.text}")
    
    async def test_login(self, username: str = "testuser", password: str = "testpass123"):
        """测试用户登录"""
        print("🔐 测试用户登录...")
        
        data = {
            "username": username,
            "password": password
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/auth/login",
                json=data
            )
            
            if response.status_code == 200:
                print("✅ 用户登录成功")
                login_data = response.json()
                self.access_token = login_data.get("access_token")
                print(f"   访问令牌: {self.access_token[:20]}...")
                return True
            else:
                print(f"❌ 用户登录失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return False
    
    async def test_protected_endpoint(self):
        """测试受保护的端点"""
        if not self.access_token:
            print("❌ 没有访问令牌，跳过受保护端点测试")
            return
        
        print("🔒 测试受保护端点...")
        
        headers = {
            "Authorization": f"Bearer {self.access_token}"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/api/v1/auth/me",
                headers=headers
            )
            
            if response.status_code == 200:
                print("✅ 受保护端点访问成功")
                user_data = response.json()
                print(f"   当前用户: {user_data.get('username')}")
            else:
                print(f"❌ 受保护端点访问失败: {response.status_code}")
                print(f"   响应: {response.text}")
    
    async def test_users_list(self):
        """测试用户列表"""
        if not self.access_token:
            print("❌ 没有访问令牌，跳过用户列表测试")
            return
        
        print("👥 测试用户列表...")
        
        headers = {
            "Authorization": f"Bearer {self.access_token}"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/api/v1/users/",
                headers=headers
            )
            
            if response.status_code == 200:
                print("✅ 用户列表获取成功")
                users_data = response.json()
                print(f"   用户总数: {users_data.get('total', 0)}")
            elif response.status_code == 403:
                print("⚠️  用户列表需要管理员权限")
            else:
                print(f"❌ 用户列表获取失败: {response.status_code}")
                print(f"   响应: {response.text}")
    
    async def test_devices_list(self):
        """测试设备列表"""
        if not self.access_token:
            print("❌ 没有访问令牌，跳过设备列表测试")
            return
        
        print("📱 测试设备列表...")
        
        headers = {
            "Authorization": f"Bearer {self.access_token}"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/api/v1/devices/",
                headers=headers
            )
            
            if response.status_code == 200:
                print("✅ 设备列表获取成功")
                devices_data = response.json()
                print(f"   设备总数: {devices_data.get('total', 0)}")
            else:
                print(f"❌ 设备列表获取失败: {response.status_code}")
                print(f"   响应: {response.text}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始 AR-System API 测试...")
        print("=" * 50)
        
        # 基础测试
        await self.test_health()
        print()
        
        # 认证测试
        await self.test_register()
        print()
        
        login_success = await self.test_login()
        print()
        
        if login_success:
            await self.test_protected_endpoint()
            print()
            
            await self.test_users_list()
            print()
            
            await self.test_devices_list()
            print()
        
        print("=" * 50)
        print("🎉 API 测试完成！")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AR-System API 测试工具")
    parser.add_argument(
        "--url", 
        default="http://localhost:8000",
        help="API 基础URL"
    )
    parser.add_argument(
        "--username",
        default="testuser",
        help="测试用户名"
    )
    parser.add_argument(
        "--email",
        default="<EMAIL>",
        help="测试邮箱"
    )
    parser.add_argument(
        "--password",
        default="testpass123",
        help="测试密码"
    )
    
    args = parser.parse_args()
    
    tester = APITester(args.url)
    
    try:
        await tester.run_all_tests()
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    asyncio.run(main())
