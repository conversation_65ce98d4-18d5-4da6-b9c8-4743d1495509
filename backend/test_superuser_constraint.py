#!/usr/bin/env python3
"""
测试超级管理员唯一性约束
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import select
from app.core.database import AsyncSessionLocal
from app.models.user import User

async def test_superuser_constraint():
    """测试超级管理员约束"""
    try:
        async with AsyncSessionLocal() as session:
            # 查询所有超级管理员
            stmt = select(User).where(User.is_superuser == True)
            result = await session.execute(stmt)
            superusers = result.scalars().all()
            
            print("🔍 当前超级管理员列表：")
            print("=" * 50)
            
            if not superusers:
                print("❌ 警告：系统中没有超级管理员！")
                return
            
            for i, user in enumerate(superusers, 1):
                print(f"{i}. ID: {user.id}")
                print(f"   用户名: {user.username}")
                print(f"   邮箱: {user.email}")
                print(f"   是否激活: {user.is_active}")
                print(f"   创建时间: {user.created_at}")
                print("-" * 30)
            
            # 验证约束
            superuser_count = len(superusers)
            print(f"\n📊 统计信息：")
            print(f"超级管理员总数: {superuser_count}")
            
            if superuser_count == 1:
                print("✅ 符合安全策略：系统只有一个超级管理员")
            elif superuser_count == 0:
                print("❌ 安全风险：系统没有超级管理员")
            else:
                print(f"⚠️ 安全风险：系统有 {superuser_count} 个超级管理员")
                print("建议：保持只有一个超级管理员以确保系统安全")
            
            # 查询所有用户的权限状态
            print(f"\n👥 所有用户权限状态：")
            print("=" * 50)
            
            all_users_stmt = select(User).order_by(User.id)
            all_result = await session.execute(all_users_stmt)
            all_users = all_result.scalars().all()
            
            for user in all_users:
                status = "🔴 超级管理员" if user.is_superuser else "🔵 普通用户"
                active = "✅ 激活" if user.is_active else "❌ 未激活"
                print(f"{user.id:2d}. {user.username:15s} {status:10s} {active}")
            
            print(f"\n📋 系统安全检查完成")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_superuser_constraint())
