#!/usr/bin/env python3
"""
创建完整的角色体系
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import select
from app.core.database import AsyncSessionLocal
from app.models.user import Role, Permission

async def create_complete_roles():
    """创建完整的角色体系"""
    try:
        async with AsyncSessionLocal() as session:
            # 检查是否已存在角色
            stmt = select(Role)
            result = await session.execute(stmt)
            existing_roles = result.scalars().all()
            
            print(f"当前角色数量: {len(existing_roles)}")
            for role in existing_roles:
                print(f"- {role.name}: {role.display_name}")
            
            # 定义完整的角色体系
            roles_to_create = [
                {
                    "name": "super_admin",
                    "display_name": "超级管理员", 
                    "description": "拥有系统所有权限的最高级别管理员",
                    "is_system": True
                },
                {
                    "name": "system_admin", 
                    "display_name": "系统管理员",
                    "description": "负责系统配置和用户管理的管理员", 
                    "is_system": True
                },
                {
                    "name": "developer",
                    "display_name": "开发者",
                    "description": "拥有开发工具和API访问权限的开发人员",
                    "is_system": False
                },
                {
                    "name": "manager", 
                    "display_name": "部门经理",
                    "description": "部门级别的管理权限，可管理本部门用户",
                    "is_system": False
                },
                {
                    "name": "operator",
                    "display_name": "操作员", 
                    "description": "具有基本操作权限的工作人员",
                    "is_system": False
                }
            ]
            
            # 检查哪些角色需要创建
            existing_role_names = {role.name for role in existing_roles}
            
            for role_data in roles_to_create:
                if role_data["name"] not in existing_role_names:
                    print(f"\n创建角色: {role_data['display_name']}")
                    
                    new_role = Role(
                        name=role_data["name"],
                        display_name=role_data["display_name"], 
                        description=role_data["description"],
                        is_system=role_data["is_system"],
                        is_active=True,
                        status="active"
                    )
                    
                    session.add(new_role)
                    print(f"✅ 角色 {role_data['display_name']} 已添加")
                else:
                    print(f"⏭️ 角色 {role_data['display_name']} 已存在，跳过")
            
            # 提交更改
            await session.commit()
            
            # 验证创建结果
            print(f"\n📊 角色创建完成，验证结果:")
            stmt = select(Role).order_by(Role.id)
            result = await session.execute(stmt)
            all_roles = result.scalars().all()
            
            print(f"总角色数量: {len(all_roles)}")
            print("=" * 50)
            for i, role in enumerate(all_roles, 1):
                system_flag = "🔒 系统" if role.is_system else "👤 自定义"
                status_flag = "✅ 激活" if role.is_active else "❌ 停用"
                print(f"{i:2d}. {role.display_name:12s} ({role.name:15s}) {system_flag} {status_flag}")
                print(f"    描述: {role.description}")
                print("-" * 30)
            
            print("🎉 完整角色体系创建完成！")
            
    except Exception as e:
        print(f"❌ 创建角色失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(create_complete_roles())
