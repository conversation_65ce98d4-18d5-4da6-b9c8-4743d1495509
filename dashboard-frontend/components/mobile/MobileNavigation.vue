<template>
  <div class="mobile-navigation">
    <!-- 移动端顶部导航栏 -->
    <div class="mobile-header quantum-data-stream quantum-matrix-bg">
      <div class="header-content">
        <!-- 菜单按钮 -->
        <button @click="toggleSidebar" class="menu-btn">
          <i :class="sidebarOpen ? 'i-carbon-close' : 'i-carbon-menu'" class="menu-icon"></i>
        </button>

        <!-- Logo和标题 -->
        <div class="header-brand">
          <div class="brand-logo quantum-energy-ring">
            <i class="i-carbon-cube text-[var(--quantum-primary)]"></i>
          </div>
          <h1 class="brand-title quantum-text-neon">AR-System</h1>
        </div>

        <!-- 右侧操作 -->
        <div class="header-actions">
          <!-- 搜索按钮 -->
          <button @click="toggleSearch" class="action-btn">
            <i class="i-carbon-search"></i>
          </button>

          <!-- 通知按钮 -->
          <button @click="toggleNotifications" class="action-btn">
            <i class="i-carbon-notification"></i>
            <span v-if="notificationCount > 0" class="notification-badge">{{ notificationCount }}</span>
          </button>

          <!-- 用户头像 -->
          <button @click="toggleUserMenu" class="user-avatar">
            <img src="/avatars/user.svg" alt="User" class="avatar-image" />
          </button>
        </div>
      </div>
    </div>

    <!-- 移动端侧边栏 -->
    <div
      v-if="sidebarOpen"
      class="mobile-sidebar-overlay"
      @click="closeSidebar"
    ></div>

    <div
      class="mobile-sidebar quantum-card-hologram"
      :class="{ 'sidebar-open': sidebarOpen }"
    >
      <!-- 侧边栏头部 -->
      <div class="sidebar-header">
        <div class="user-profile">
          <div class="profile-avatar quantum-energy-ring">
            <img src="/avatars/user.svg" alt="User" class="profile-image" />
          </div>
          <div class="profile-info">
            <h3 class="profile-name quantum-text-matrix">Alex Chen</h3>
            <p class="profile-role text-[var(--quantum-fg-secondary)]">系统管理员</p>
          </div>
        </div>
      </div>

      <!-- 导航菜单 -->
      <div class="sidebar-nav">
        <div class="nav-section">
          <h4 class="nav-section-title">主要功能</h4>
          <div class="nav-items">
            <NuxtLink
              v-for="item in mainNavItems"
              :key="item.path"
              :to="item.path"
              @click="closeSidebar"
              class="nav-item"
              :class="{ active: isActiveRoute(item.path) }"
            >
              <i :class="item.icon" class="nav-icon"></i>
              <span class="nav-text">{{ item.label }}</span>
              <span v-if="item.badge" class="nav-badge">{{ item.badge }}</span>
            </NuxtLink>
          </div>
        </div>

        <div class="nav-section">
          <h4 class="nav-section-title">管理工具</h4>
          <div class="nav-items">
            <NuxtLink
              v-for="item in managementNavItems"
              :key="item.path"
              :to="item.path"
              @click="closeSidebar"
              class="nav-item"
              :class="{ active: isActiveRoute(item.path) }"
            >
              <i :class="item.icon" class="nav-icon"></i>
              <span class="nav-text">{{ item.label }}</span>
              <span v-if="item.badge" class="nav-badge">{{ item.badge }}</span>
            </NuxtLink>
          </div>
        </div>
      </div>

      <!-- 侧边栏底部 -->
      <div class="sidebar-footer">
        <button @click="toggleTheme" class="footer-btn">
          <i :class="isDarkMode ? 'i-carbon-sun' : 'i-carbon-moon'" class="footer-icon"></i>
          <span class="footer-text">{{ isDarkMode ? '浅色模式' : '深色模式' }}</span>
        </button>

        <button @click="logout" class="footer-btn logout-btn">
          <i class="i-carbon-logout footer-icon"></i>
          <span class="footer-text">退出登录</span>
        </button>
      </div>
    </div>

    <!-- 移动端搜索面板 -->
    <div
      v-if="searchOpen"
      class="mobile-search-panel quantum-card-hologram"
    >
      <div class="search-header">
        <h3 class="search-title quantum-text-matrix">搜索</h3>
        <button @click="closeSearch" class="close-btn">
          <i class="i-carbon-close"></i>
        </button>
      </div>

      <div class="search-content">
        <SmartSearch
          placeholder="搜索功能、数据或设置..."
          :suggestions="searchSuggestions"
          :quick-actions="quickActions"
          auto-focus
          @search="onSearch"
          @select="onSelectSuggestion"
        />
      </div>
    </div>

    <!-- 移动端通知面板 -->
    <div
      v-if="notificationsOpen"
      class="mobile-notifications-panel quantum-card-hologram"
    >
      <div class="notifications-header">
        <h3 class="notifications-title quantum-text-matrix">通知</h3>
        <button @click="closeNotifications" class="close-btn">
          <i class="i-carbon-close"></i>
        </button>
      </div>

      <div class="notifications-content">
        <div v-if="notifications.length === 0" class="empty-notifications">
          <i class="i-carbon-notification-off text-4xl text-[var(--quantum-fg-muted)] mb-4"></i>
          <p class="empty-text">暂无新通知</p>
        </div>

        <div v-else class="notifications-list">
          <div
            v-for="notification in notifications"
            :key="notification.id"
            class="notification-item"
            :class="{ 'notification-unread': !notification.read }"
          >
            <div class="notification-icon quantum-energy-ring">
              <i :class="[notification.icon, getNotificationIconClass(notification.type)]"></i>
            </div>
            <div class="notification-content">
              <h4 class="notification-title">{{ notification.title }}</h4>
              <p class="notification-message">{{ notification.message }}</p>
              <span class="notification-time">{{ formatTime(notification.timestamp) }}</span>
            </div>
            <button @click="markAsRead(notification)" class="notification-action">
              <i class="i-carbon-checkmark"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 移动端用户菜单 -->
    <div
      v-if="userMenuOpen"
      class="mobile-user-menu quantum-card-hologram"
    >
      <div class="user-menu-header">
        <div class="user-info">
          <div class="user-avatar-large quantum-energy-ring">
            <img src="/avatars/user.svg" alt="User" class="avatar-large" />
          </div>
          <div class="user-details">
            <h3 class="user-name quantum-text-matrix">Alex Chen</h3>
            <p class="user-email text-[var(--quantum-fg-secondary)]"><EMAIL></p>
            <span class="user-status online">在线</span>
          </div>
        </div>
        <button @click="closeUserMenu" class="close-btn">
          <i class="i-carbon-close"></i>
        </button>
      </div>

      <div class="user-menu-content">
        <div class="menu-section">
          <button
            v-for="action in userActions"
            :key="action.key"
            @click="handleUserAction(action)"
            class="menu-action"
          >
            <i :class="action.icon" class="action-icon"></i>
            <span class="action-text">{{ action.label }}</span>
            <i class="i-carbon-chevron-right action-arrow"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 移动端底部导航栏 -->
    <div class="mobile-bottom-nav quantum-data-stream">
      <NuxtLink
        v-for="item in bottomNavItems"
        :key="item.path"
        :to="item.path"
        class="bottom-nav-item"
        :class="{ active: isActiveRoute(item.path) }"
      >
        <i :class="item.icon" class="bottom-nav-icon"></i>
        <span class="bottom-nav-text">{{ item.label }}</span>
        <span v-if="item.badge" class="bottom-nav-badge">{{ item.badge }}</span>
      </NuxtLink>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 状态管理
const sidebarOpen = ref(false)
const searchOpen = ref(false)
const notificationsOpen = ref(false)
const userMenuOpen = ref(false)
const isDarkMode = ref(false)
const notificationCount = ref(3)

// 导航数据
const mainNavItems = ref([
  { path: '/', label: '系统概览', icon: 'i-carbon-dashboard', badge: '' },
  { path: '/analytics', label: '数据分析', icon: 'i-carbon-analytics', badge: 'Live' },
  { path: '/devices', label: '设备管理', icon: 'i-carbon-devices', badge: '24' },
  { path: '/applications', label: '应用管理', icon: 'i-carbon-application', badge: '156' }
])

const managementNavItems = ref([
  { path: '/users', label: '用户管理', icon: 'i-carbon-user-multiple', badge: '2.4K' },
  { path: '/ecommerce', label: '电商管理', icon: 'i-carbon-shopping-cart', badge: 'New' },
  { path: '/notifications', label: '通知中心', icon: 'i-carbon-notification', badge: '8' },
  { path: '/settings', label: '系统设置', icon: 'i-carbon-settings', badge: '' }
])

const bottomNavItems = ref([
  { path: '/', label: '首页', icon: 'i-carbon-home', badge: '' },
  { path: '/analytics', label: '分析', icon: 'i-carbon-analytics', badge: '' },
  { path: '/devices', label: '设备', icon: 'i-carbon-devices', badge: '24' },
  { path: '/users', label: '用户', icon: 'i-carbon-user-multiple', badge: '' },
  { path: '/settings', label: '设置', icon: 'i-carbon-settings', badge: '' }
])

// 搜索建议
const searchSuggestions = ref([
  { id: '1', title: '用户管理', description: '管理系统用户和权限', type: '功能', icon: 'i-carbon-user-multiple' },
  { id: '2', title: '设备监控', description: '查看设备状态和性能', type: '功能', icon: 'i-carbon-devices' },
  { id: '3', title: '数据分析', description: '查看系统数据和报表', type: '功能', icon: 'i-carbon-analytics' }
])

// 快捷操作
const quickActions = ref([
  { key: 'create-user', label: '创建用户', icon: 'i-carbon-user-plus', handler: () => console.log('创建用户') },
  { key: 'add-device', label: '添加设备', icon: 'i-carbon-add', handler: () => console.log('添加设备') },
  { key: 'generate-report', label: '生成报表', icon: 'i-carbon-document', handler: () => console.log('生成报表') }
])

// 通知数据
const notifications = ref([
  {
    id: 1,
    title: '系统更新',
    message: '新版本 v2.1.0 已发布，包含性能优化和新功能',
    type: 'info',
    icon: 'i-carbon-update-now',
    timestamp: new Date(Date.now() - 300000),
    read: false
  },
  {
    id: 2,
    title: '设备离线',
    message: 'AR-Vision Pro #001 已离线超过5分钟',
    type: 'warning',
    icon: 'i-carbon-warning',
    timestamp: new Date(Date.now() - 600000),
    read: false
  },
  {
    id: 3,
    title: '备份完成',
    message: '系统数据备份已成功完成',
    type: 'success',
    icon: 'i-carbon-checkmark',
    timestamp: new Date(Date.now() - 900000),
    read: true
  }
])

// 用户操作
const userActions = ref([
  { key: 'profile', label: '个人资料', icon: 'i-carbon-user' },
  { key: 'preferences', label: '偏好设置', icon: 'i-carbon-settings-adjust' },
  { key: 'security', label: '安全设置', icon: 'i-carbon-security' },
  { key: 'help', label: '帮助中心', icon: 'i-carbon-help' },
  { key: 'feedback', label: '意见反馈', icon: 'i-carbon-chat' }
])

// 方法
const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value
  if (sidebarOpen.value) {
    closeOtherPanels('sidebar')
  }
}

const closeSidebar = () => {
  sidebarOpen.value = false
}

const toggleSearch = () => {
  searchOpen.value = !searchOpen.value
  if (searchOpen.value) {
    closeOtherPanels('search')
  }
}

const closeSearch = () => {
  searchOpen.value = false
}

const toggleNotifications = () => {
  notificationsOpen.value = !notificationsOpen.value
  if (notificationsOpen.value) {
    closeOtherPanels('notifications')
  }
}

const closeNotifications = () => {
  notificationsOpen.value = false
}

const toggleUserMenu = () => {
  userMenuOpen.value = !userMenuOpen.value
  if (userMenuOpen.value) {
    closeOtherPanels('userMenu')
  }
}

const closeUserMenu = () => {
  userMenuOpen.value = false
}

const closeOtherPanels = (except: string) => {
  if (except !== 'sidebar') sidebarOpen.value = false
  if (except !== 'search') searchOpen.value = false
  if (except !== 'notifications') notificationsOpen.value = false
  if (except !== 'userMenu') userMenuOpen.value = false
}

const isActiveRoute = (path: string) => {
  const route = useRoute()
  return route.path === path || route.path.startsWith(path + '/')
}

const toggleTheme = () => {
  isDarkMode.value = !isDarkMode.value
  // 实现主题切换逻辑
}

const logout = () => {
  console.log('退出登录')
  closeSidebar()
}

const onSearch = (query: string) => {
  console.log('搜索:', query)
  closeSearch()
}

const onSelectSuggestion = (suggestion: any) => {
  console.log('选择建议:', suggestion)
  closeSearch()
}

const getNotificationIconClass = (type: string) => {
  const classes = {
    info: 'text-[var(--quantum-primary)]',
    warning: 'text-[var(--quantum-warning)]',
    error: 'text-[var(--quantum-error)]',
    success: 'text-[var(--quantum-success)]'
  }
  return classes[type as keyof typeof classes] || 'text-[var(--quantum-fg-muted)]'
}

const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / (1000 * 60))

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`

  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}小时前`

  const days = Math.floor(hours / 24)
  return `${days}天前`
}

const markAsRead = (notification: any) => {
  notification.read = true
  notificationCount.value = notifications.value.filter(n => !n.read).length
}

const handleUserAction = (action: any) => {
  console.log('用户操作:', action.key)
  closeUserMenu()
}

// 监听屏幕尺寸变化
const handleResize = () => {
  if (window.innerWidth >= 1024) {
    closeOtherPanels('')
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* 移动端导航容器 */
.mobile-navigation {
  display: none;
}

@media (max-width: 1023px) {
  .mobile-navigation {
    display: block;
  }
}

/* 移动端顶部导航栏 */
.mobile-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 4rem;
  border-bottom: 1px solid var(--quantum-border-color);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 1rem;
}

.menu-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.5rem;
  transition: all var(--quantum-transition-fast);
  cursor: pointer;
}

.menu-btn:hover {
  background: var(--quantum-bg-elevated);
}

.menu-icon {
  font-size: 1.25rem;
  color: var(--quantum-fg-primary);
  transition: transform var(--quantum-transition-fast);
}

.header-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
  justify-content: center;
  margin: 0 1rem;
}

.brand-logo {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-title {
  font-size: 1.25rem;
  font-weight: 700;
  letter-spacing: 0.05em;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-btn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.5rem;
  transition: all var(--transition-fast);
  cursor: pointer;
}

.action-btn:hover {
  background: var(--quantum-bg-elevated);
}

.notification-badge {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  background: var(--quantum-error);
  color: white;
  font-size: 0.625rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 0.75rem;
  min-width: 1rem;
  height: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  transition: all var(--quantum-transition-fast);
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 0 0 2px var(--quantum-primary);
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 移动端侧边栏 */
.mobile-sidebar-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1100;
  backdrop-filter: blur(4px);
  animation: overlayFadeIn 0.3s ease-out;
}

.mobile-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 20rem;
  max-width: 85vw;
  z-index: 1200;
  transform: translateX(-100%);
  transition: transform var(--quantum-transition-normal);
  overflow-y: auto;
  border-right: 1px solid var(--quantum-border-color);
}

.sidebar-open {
  transform: translateX(0);
}

.sidebar-header {
  padding: 1.5rem 1rem 1rem;
  border-bottom: 1px solid var(--quantum-border-subtle);
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.profile-avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  overflow: hidden;
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-info {
  flex: 1;
  min-width: 0;
}

.profile-name {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.profile-role {
  font-size: 0.875rem;
}

/* 导航菜单 */
.sidebar-nav {
  padding: 1rem 0;
}

.nav-section {
  margin-bottom: 1.5rem;
}

.nav-section-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--quantum-fg-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0 1rem;
  margin-bottom: 0.5rem;
}

.nav-items {
  padding: 0 0.5rem;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 0.5rem;
  border-radius: 0.5rem;
  transition: all var(--quantum-transition-fast);
  cursor: pointer;
  text-decoration: none;
  color: var(--quantum-fg-primary);
  margin-bottom: 0.25rem;
}

.nav-item:hover {
  background: var(--quantum-bg-elevated);
  transform: translateX(2px);
}

.nav-item.active {
  background: var(--quantum-primary);
  color: white;
  box-shadow: var(--quantum-glow-primary);
}

.nav-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.nav-text {
  flex: 1;
  font-weight: 500;
}

.nav-badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  background: var(--quantum-bg-elevated);
  border-radius: 0.75rem;
  color: var(--quantum-fg-secondary);
}

.nav-item.active .nav-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* 侧边栏底部 */
.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid var(--quantum-border-subtle);
  margin-top: auto;
}

.footer-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.5rem;
  transition: all var(--quantum-transition-fast);
  cursor: pointer;
  text-align: left;
  margin-bottom: 0.5rem;
}

.footer-btn:hover {
  background: var(--quantum-bg-elevated);
}

.logout-btn:hover {
  background: var(--quantum-error);
  color: white;
}

.footer-icon {
  font-size: 1.125rem;
}

.footer-text {
  font-weight: 500;
}

/* 移动端面板通用样式 */
.mobile-search-panel,
.mobile-notifications-panel,
.mobile-user-menu {
  position: fixed;
  top: 4rem;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1100;
  overflow-y: auto;
  animation: panelSlideIn 0.3s ease-out;
}

/* 搜索面板 */
.search-header,
.notifications-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--quantum-border-subtle);
}

.search-title,
.notifications-title {
  font-size: 1.25rem;
  font-weight: 600;
}

.close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 0.375rem;
  transition: all var(--quantum-transition-fast);
  cursor: pointer;
}

.close-btn:hover {
  background: var(--quantum-bg-elevated);
}

.search-content {
  padding: 1rem;
}

/* 通知面板 */
.notifications-content {
  padding: 1rem;
}

.empty-notifications {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
}

.empty-text {
  color: var(--quantum-fg-muted);
  font-size: 0.875rem;
}

.notifications-list {
  space-y: 0.75rem;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background: var(--quantum-bg-elevated);
  border-radius: 0.75rem;
  border-left: 3px solid transparent;
  transition: all var(--quantum-transition-fast);
}

.notification-unread {
  border-left-color: var(--quantum-primary);
  background: var(--quantum-bg-surface);
}

.notification-icon {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--quantum-fg-primary);
}

.notification-message {
  font-size: 0.875rem;
  color: var(--quantum-fg-secondary);
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

.notification-time {
  font-size: 0.75rem;
  color: var(--quantum-fg-muted);
}

.notification-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 0.375rem;
  transition: all var(--quantum-transition-fast);
  cursor: pointer;
  flex-shrink: 0;
}

.notification-action:hover {
  background: var(--quantum-primary);
  color: white;
}

/* 用户菜单 */
.user-menu-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 1.5rem 1rem 1rem;
  border-bottom: 1px solid var(--quantum-border-subtle);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.user-avatar-large {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  overflow: hidden;
}

.avatar-large {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.user-email {
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.user-status {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.125rem 0.5rem;
  border-radius: 0.75rem;
}

.user-status.online {
  background: var(--quantum-success);
  color: white;
}

.user-menu-content {
  padding: 1rem;
}

.menu-section {
  space-y: 0.25rem;
}

.menu-action {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.5rem;
  transition: all var(--quantum-transition-fast);
  cursor: pointer;
  text-align: left;
}

.menu-action:hover {
  background: var(--quantum-bg-elevated);
  transform: translateX(2px);
}

.action-icon {
  font-size: 1.125rem;
  color: var(--quantum-primary);
  flex-shrink: 0;
}

.action-text {
  flex: 1;
  font-weight: 500;
  color: var(--quantum-fg-primary);
}

.action-arrow {
  font-size: 0.875rem;
  color: var(--quantum-fg-muted);
}

/* 移动端底部导航栏 */
.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  display: flex;
  height: 4rem;
  border-top: 1px solid var(--quantum-border-color);
  padding: 0.5rem;
  gap: 0.25rem;
}

.bottom-nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all var(--quantum-transition-fast);
  cursor: pointer;
  text-decoration: none;
  color: var(--quantum-fg-muted);
  position: relative;
}

.bottom-nav-item:hover,
.bottom-nav-item.active {
  color: var(--quantum-primary);
  background: var(--quantum-bg-elevated);
}

.bottom-nav-icon {
  font-size: 1.25rem;
}

.bottom-nav-text {
  font-size: 0.625rem;
  font-weight: 500;
  text-align: center;
}

.bottom-nav-badge {
  position: absolute;
  top: 0.25rem;
  right: 0.75rem;
  background: var(--quantum-error);
  color: white;
  font-size: 0.5rem;
  font-weight: 600;
  padding: 0.125rem 0.25rem;
  border-radius: 0.5rem;
  min-width: 0.75rem;
  height: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 动画 */
@keyframes overlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes panelSlideIn {
  from {
    opacity: 0;
    transform: translateY(-1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式优化 */
@media (max-width: 640px) {
  .mobile-sidebar {
    width: 100vw;
    max-width: none;
  }

  .header-content {
    padding: 0 0.75rem;
  }

  .brand-title {
    font-size: 1.125rem;
  }

  .bottom-nav-text {
    font-size: 0.5rem;
  }
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .mobile-header {
    padding-top: env(safe-area-inset-top);
  }

  .mobile-bottom-nav {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .mobile-sidebar {
    padding-top: env(safe-area-inset-top);
  }
}
</style>