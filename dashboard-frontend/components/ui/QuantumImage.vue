<template>
  <div 
    class="lazy-image-container"
    :class="{ 'loading': isLoading, 'error': hasError }"
    :style="{ width: width, height: height }"
  >
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-placeholder">
      <div class="loading-skeleton quantum-data-stream"></div>
      <div class="loading-spinner">
        <i class="i-carbon-circle-dash animate-spin text-[var(--quantum-primary)]"></i>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="hasError" class="error-placeholder">
      <div class="error-icon quantum-energy-ring w-12 h-12 flex items-center justify-center">
        <i class="i-carbon-image text-[var(--quantum-error)]"></i>
      </div>
      <p class="error-text text-sm text-[var(--quantum-fg-muted)] mt-2">图片加载失败</p>
      <button @click="retry" class="retry-btn text-xs text-[var(--quantum-primary)] mt-1 hover:underline">
        重试
      </button>
    </div>

    <!-- 实际图片 -->
    <img
      v-else
      ref="imageRef"
      :src="currentSrc"
      :alt="alt"
      :class="imageClass"
      @load="onLoad"
      @error="onError"
      class="lazy-image"
    />

    <!-- 渐进式加载遮罩 -->
    <div v-if="showProgressiveMask" class="progressive-mask"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

interface Props {
  src: string
  alt?: string
  width?: string
  height?: string
  placeholder?: string
  lazy?: boolean
  progressive?: boolean
  retryCount?: number
  imageClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  alt: '',
  width: '100%',
  height: 'auto',
  placeholder: '',
  lazy: true,
  progressive: false,
  retryCount: 3,
  imageClass: ''
})

// 状态管理
const isLoading = ref(true)
const hasError = ref(false)
const isIntersecting = ref(false)
const currentRetryCount = ref(0)
const showProgressiveMask = ref(false)
const imageRef = ref<HTMLImageElement>()

// 计算当前图片源
const currentSrc = computed(() => {
  if (!props.lazy || isIntersecting.value) {
    return props.src
  }
  return props.placeholder || ''
})

// Intersection Observer
let observer: IntersectionObserver | null = null

const setupIntersectionObserver = () => {
  if (!props.lazy || typeof window === 'undefined') {
    isIntersecting.value = true
    return
  }

  observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          isIntersecting.value = true
          observer?.disconnect()
        }
      })
    },
    {
      rootMargin: '50px',
      threshold: 0.1
    }
  )
}

// 图片加载处理
const onLoad = () => {
  isLoading.value = false
  hasError.value = false
  
  if (props.progressive) {
    showProgressiveMask.value = true
    setTimeout(() => {
      showProgressiveMask.value = false
    }, 300)
  }
}

const onError = () => {
  if (currentRetryCount.value < props.retryCount) {
    currentRetryCount.value++
    setTimeout(() => {
      if (imageRef.value) {
        imageRef.value.src = props.src
      }
    }, 1000 * currentRetryCount.value)
  } else {
    isLoading.value = false
    hasError.value = true
  }
}

const retry = () => {
  hasError.value = false
  isLoading.value = true
  currentRetryCount.value = 0
  if (imageRef.value) {
    imageRef.value.src = props.src
  }
}

// 监听src变化
watch(() => props.src, () => {
  isLoading.value = true
  hasError.value = false
  currentRetryCount.value = 0
})

onMounted(() => {
  setupIntersectionObserver()
  
  if (observer && imageRef.value?.parentElement) {
    observer.observe(imageRef.value.parentElement)
  }
  
  // 如果不使用懒加载，直接开始加载
  if (!props.lazy) {
    isIntersecting.value = true
  }
})

onUnmounted(() => {
  observer?.disconnect()
})
</script>

<style scoped>
.lazy-image-container {
  position: relative;
  overflow: hidden;
  background: var(--quantum-bg-elevated);
  border-radius: 0.5rem;
  transition: all var(--quantum-transition-fast);
}

.lazy-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity var(--quantum-transition-normal);
}

.loading-placeholder {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--quantum-bg-elevated);
}

.loading-skeleton {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.loading-spinner {
  position: relative;
  z-index: 1;
}

.error-placeholder {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--quantum-bg-elevated);
  border: 1px dashed var(--quantum-border-color);
}

.error-text {
  text-align: center;
}

.retry-btn {
  cursor: pointer;
  transition: color var(--quantum-transition-fast);
}

.progressive-mask {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%
  );
  animation: progressive-reveal 0.3s ease-out;
}

/* 动画 */
@keyframes skeleton-loading {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes progressive-reveal {
  0% {
    opacity: 1;
    transform: scale(1.05);
  }
  100% {
    opacity: 0;
    transform: scale(1);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式优化 */
@media (max-width: 768px) {
  .loading-spinner {
    font-size: 1.5rem;
  }
  
  .error-icon {
    width: 2rem;
    height: 2rem;
  }
}
</style>
