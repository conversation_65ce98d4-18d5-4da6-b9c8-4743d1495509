<template>
  <div class="quantum-font-switcher">
    <div class="quantum-font-container">
      <select 
        v-model="currentQuantumFont" 
        @change="handleQuantumFontChange"
        class="quantum-font-select"
      >
        <option 
          v-for="font in quantumFontOptions" 
          :key="font.value" 
          :value="font.value"
        >
          {{ font.label }}
        </option>
      </select>
      <div class="quantum-font-preview">
        <span class="quantum-font-preview-text" :style="{ fontFamily: currentQuantumFont }">
          {{ t('quantum.font.preview') }}
        </span>
      </div>
    </div>
    
    <!-- 量子字体切换动画 -->
    <Transition name="quantum-font-transition">
      <div v-if="isFontChanging" class="quantum-font-change-overlay">
        <div class="quantum-font-particles">
          <div v-for="i in 15" :key="i" class="quantum-font-particle" :style="{ '--index': i }"></div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 量子字体状态
const currentQuantumFont = ref('Inter')
const isFontChanging = ref(false)

// 量子字体选项
const quantumFontOptions = ref([
  { 
    name: 'Inter', 
    label: 'Inter (量子现代)', 
    value: 'Inter, "Noto Sans SC", sans-serif',
    description: '现代量子界面字体'
  },
  { 
    name: 'Orbitron', 
    label: 'Orbitron (量子科技)', 
    value: 'Orbitron, "Noto Sans SC", sans-serif',
    description: '科技感量子字体'
  },
  { 
    name: 'Rajdhani', 
    label: 'Rajdhani (量子未来)', 
    value: 'Rajdhani, "Noto Sans SC", sans-serif',
    description: '未来感量子字体'
  },
  { 
    name: 'JetBrains Mono', 
    label: 'JetBrains Mono (量子代码)', 
    value: 'JetBrains Mono, "Noto Sans SC", monospace',
    description: '代码专用量子字体'
  },
  { 
    name: 'Fira Code', 
    label: 'Fira Code (量子编程)', 
    value: 'Fira Code, "Noto Sans SC", monospace',
    description: '编程专用量子字体'
  }
])

// 多语言翻译
const t = (key: string) => {
  const translations = {
    'quantum.font.preview': 'QUANTUM 量子字体预览 123',
    'quantum.font.changing': '量子字体切换中...',
  }
  return translations[key] || key
}

// 量子字体切换逻辑
const handleQuantumFontChange = async () => {
  isFontChanging.value = true
  
  // 创建量子字体切换效果
  createQuantumFontEffect()
  
  setTimeout(() => {
    setQuantumFontFamily(currentQuantumFont.value)
    isFontChanging.value = false
  }, 600)
}

// 设置量子字体
const setQuantumFontFamily = (fontValue: string) => {
  // 更新CSS变量
  document.documentElement.style.setProperty('--quantum-font-family-sans', fontValue)
  document.documentElement.style.setProperty('--font-family-sans', fontValue)
  
  // 保存到本地存储
  const fontName = quantumFontOptions.value.find(f => f.value === fontValue)?.name || 'Inter'
  localStorage.setItem('quantum-dashboard-font', fontName)
  
  // 触发量子字体变化事件
  document.dispatchEvent(new CustomEvent('quantum-font-changed', { 
    detail: { fontName, fontValue } 
  }))
}

// 创建量子字体切换效果
const createQuantumFontEffect = () => {
  // 创建量子文字粒子爆发效果
  const container = document.createElement('div')
  container.className = 'quantum-font-burst'
  document.body.appendChild(container)
  
  // 创建文字粒子
  const text = 'QUANTUM FONT'
  for (let i = 0; i < text.length; i++) {
    const particle = document.createElement('div')
    particle.className = 'quantum-text-particle'
    particle.textContent = text[i]
    particle.style.setProperty('--index', i.toString())
    particle.style.setProperty('--char', `"${text[i]}"`)
    container.appendChild(particle)
  }
  
  setTimeout(() => {
    if (document.body.contains(container)) {
      document.body.removeChild(container)
    }
  }, 1000)
}

// 初始化量子字体
onMounted(() => {
  const savedFont = localStorage.getItem('quantum-dashboard-font')
  if (savedFont) {
    const fontOption = quantumFontOptions.value.find(f => f.name === savedFont)
    if (fontOption) {
      currentQuantumFont.value = fontOption.value
      setQuantumFontFamily(fontOption.value)
    }
  }
})
</script>

<style scoped>
.quantum-font-switcher {
  position: relative;
}

.quantum-font-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.quantum-font-select {
  padding: 0.5rem 0.75rem;
  background: var(--quantum-bg-elevated, rgba(26, 26, 46, 0.8));
  border: 1px solid var(--quantum-border-color, rgba(0, 255, 255, 0.3));
  border-radius: 0.5rem;
  color: var(--quantum-fg-primary, #ffffff);
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--quantum-transition-fast, 150ms);
  backdrop-filter: blur(10px);
}

.quantum-font-select:hover {
  border-color: var(--quantum-color-primary, #00ffff);
  box-shadow: var(--quantum-glow-primary, 0 0 15px rgba(0, 255, 255, 0.4));
}

.quantum-font-select:focus {
  outline: none;
  border-color: var(--quantum-color-primary, #00ffff);
  box-shadow: var(--quantum-glow-primary, 0 0 20px rgba(0, 255, 255, 0.6));
}

.quantum-font-select option {
  background: var(--quantum-bg-surface, #2a2a3e);
  color: var(--quantum-fg-primary, #ffffff);
  padding: 0.5rem;
}

.quantum-font-preview {
  padding: 0.75rem;
  background: var(--quantum-bg-surface, rgba(42, 42, 62, 0.6));
  border: 1px solid var(--quantum-border-subtle, rgba(0, 255, 255, 0.2));
  border-radius: 0.5rem;
  text-align: center;
  backdrop-filter: blur(5px);
  position: relative;
  overflow: hidden;
}

.quantum-font-preview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(0, 255, 255, 0.1), 
    transparent
  );
  animation: quantumFontPreviewScan 3s ease-in-out infinite;
  pointer-events: none;
}

.quantum-font-preview-text {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--quantum-color-primary, #00ffff);
  text-shadow: var(--quantum-glow-primary, 0 0 8px rgba(0, 255, 255, 0.4));
  letter-spacing: 0.05em;
  transition: all var(--quantum-transition-normal, 300ms);
  position: relative;
  z-index: 1;
}

/* 量子字体切换动画遮罩 */
.quantum-font-change-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 17, 34, 0.8);
  z-index: 9999;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantum-font-particles {
  position: relative;
  width: 200px;
  height: 200px;
}

.quantum-font-particle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: var(--quantum-color-accent, #00ff41);
  border-radius: 50%;
  animation: quantumFontParticle 0.6s ease-out;
  animation-delay: calc(var(--index) * 0.03s);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 10px var(--quantum-color-accent, #00ff41);
}

/* 量子字体切换过渡动画 */
.quantum-font-transition-enter-active,
.quantum-font-transition-leave-active {
  transition: all 0.6s ease-out;
}

.quantum-font-transition-enter-from,
.quantum-font-transition-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

/* 动画关键帧 */
@keyframes quantumFontPreviewScan {
  0%, 100% { 
    transform: translateX(-100%); 
    opacity: 0; 
  }
  50% { 
    transform: translateX(0); 
    opacity: 1; 
  }
}

@keyframes quantumFontParticle {
  0% { 
    transform: translate(-50%, -50%) scale(0) rotate(0deg); 
    opacity: 1; 
  }
  50% { 
    transform: translate(-50%, -50%) scale(1.5) rotate(180deg); 
    opacity: 0.8; 
  }
  100% { 
    transform: translate(-50%, -50%) scale(0) rotate(360deg) translateX(100px); 
    opacity: 0; 
  }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .quantum-font-select {
    font-size: 0.75rem;
    padding: 0.375rem 0.5rem;
  }
  
  .quantum-font-preview-text {
    font-size: 0.75rem;
  }
  
  .quantum-font-preview {
    padding: 0.5rem;
  }
}
</style>

<style>
/* 全局量子字体切换效果 */
.quantum-font-burst {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9998;
  pointer-events: none;
  display: flex;
  gap: 0.5rem;
}

.quantum-text-particle {
  font-family: var(--font-mono);
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  color: var(--quantum-primary);
  text-shadow: 0 0 15px var(--quantum-primary);
  animation: quantumTextParticle 1s ease-out;
  animation-delay: calc(var(--index) * 0.1s);
  animation-fill-mode: both;
}

@keyframes quantumTextParticle {
  0% { 
    transform: translateY(0) scale(1) rotate(0deg); 
    opacity: 1; 
  }
  50% { 
    transform: translateY(-50px) scale(1.5) rotate(180deg); 
    opacity: 0.8; 
  }
  100% { 
    transform: translateY(-100px) scale(0) rotate(360deg); 
    opacity: 0; 
  }
}

/* 量子字体变量更新动画 */
html[data-quantum-font-changing] * {
  transition: font-family var(--transition-normal) ease-out !important;
}

/* 量子字体系统变量 */
:root {
  --quantum-font-family-sans: 'Inter', 'Noto Sans SC', sans-serif;
  --quantum-font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;
  --quantum-font-family-display: 'Orbitron', 'Inter', sans-serif;
  --quantum-font-family-cyber: 'Orbitron', 'Rajdhani', sans-serif;
  --quantum-font-family-code: 'JetBrains Mono', 'Source Code Pro', monospace;
}

/* 量子字体应用类 */
.quantum-font-sans {
  font-family: var(--quantum-font-family-sans);
}

.quantum-font-mono {
  font-family: var(--quantum-font-family-mono);
}

.quantum-font-display {
  font-family: var(--quantum-font-family-display);
}

.quantum-font-cyber {
  font-family: var(--quantum-font-family-cyber);
}

.quantum-font-code {
  font-family: var(--quantum-font-family-code);
}

/* 量子字体大小系统 */
.quantum-text-xs { font-size: 0.75rem; }
.quantum-text-sm { font-size: 0.875rem; }
.quantum-text-base { font-size: 1rem; }
.quantum-text-lg { font-size: 1.125rem; }
.quantum-text-xl { font-size: 1.25rem; }
.quantum-text-2xl { font-size: 1.5rem; }
.quantum-text-3xl { font-size: 1.875rem; }
.quantum-text-4xl { font-size: 2.25rem; }

/* 量子字体权重 */
.quantum-font-light { font-weight: 300; }
.quantum-font-normal { font-weight: 400; }
.quantum-font-medium { font-weight: 500; }
.quantum-font-semibold { font-weight: 600; }
.quantum-font-bold { font-weight: 700; }
.quantum-font-extrabold { font-weight: 800; }
.quantum-font-black { font-weight: 900; }

/* 量子字体效果 */
.quantum-text-glow {
  text-shadow: var(--quantum-glow-primary, 0 0 10px rgba(0, 255, 255, 0.5));
}

.quantum-text-neon {
  color: var(--quantum-color-primary, #00ffff);
  text-shadow: var(--quantum-glow-primary, 0 0 15px rgba(0, 255, 255, 0.6));
  font-weight: 700;
  letter-spacing: 0.05em;
}

.quantum-text-matrix {
  font-family: var(--quantum-font-family-cyber);
  color: var(--quantum-color-accent, #00ff41);
  text-shadow: 0 0 10px var(--quantum-color-accent, #00ff41);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.quantum-text-code {
  font-family: var(--quantum-font-family-code);
  background: rgba(0, 255, 255, 0.1);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  border: 1px solid rgba(0, 255, 255, 0.3);
}
</style>
