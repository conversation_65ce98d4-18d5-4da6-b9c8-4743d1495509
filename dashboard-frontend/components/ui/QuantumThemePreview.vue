<template>
  <div class="quantum-theme-preview-container">
    <div class="quantum-preview-header">
      <h3 class="quantum-preview-title">{{ t('quantum.theme.preview.title') }}</h3>
      <div class="quantum-theme-controls">
        <QuantumThemeSwitcher />
        <QuantumFontSwitcher />
      </div>
    </div>
    
    <div class="quantum-preview-grid">
      <!-- 量子深色主题预览 -->
      <div class="quantum-preview-card" data-theme="dark">
        <div class="quantum-preview-content">
          <h4 class="quantum-preview-theme-title">{{ t('quantum.theme.dark') }}</h4>
          <p class="quantum-preview-description">
            {{ t('quantum.theme.dark.description') }}
          </p>
          <div class="quantum-preview-elements">
            <button class="quantum-btn-pulse quantum-preview-btn">{{ t('quantum.common.button') }}</button>
            <div class="quantum-card-hologram quantum-preview-card-mini">
              <span class="quantum-preview-data">99.9%</span>
              <div class="quantum-preview-particles">
                <div v-for="i in 5" :key="i" class="quantum-preview-particle"></div>
              </div>
            </div>
            <div class="quantum-preview-progress">
              <div class="quantum-progress-bar">
                <div class="quantum-progress-fill" style="width: 75%"></div>
              </div>
            </div>
          </div>
          <div class="quantum-preview-stats">
            <div class="quantum-stat-item">
              <span class="quantum-stat-value">156</span>
              <span class="quantum-stat-label">NODES</span>
            </div>
            <div class="quantum-stat-item">
              <span class="quantum-stat-value">2.4K</span>
              <span class="quantum-stat-label">USERS</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 量子浅色主题预览 -->
      <div class="quantum-preview-card" data-theme="light">
        <div class="quantum-preview-content">
          <h4 class="quantum-preview-theme-title">{{ t('quantum.theme.light') }}</h4>
          <p class="quantum-preview-description">
            {{ t('quantum.theme.light.description') }}
          </p>
          <div class="quantum-preview-elements">
            <button class="quantum-btn-pulse quantum-preview-btn">{{ t('quantum.common.button') }}</button>
            <div class="quantum-card-hologram quantum-preview-card-mini">
              <span class="quantum-preview-data">99.9%</span>
              <div class="quantum-preview-particles">
                <div v-for="i in 5" :key="i" class="quantum-preview-particle"></div>
              </div>
            </div>
            <div class="quantum-preview-progress">
              <div class="quantum-progress-bar">
                <div class="quantum-progress-fill" style="width: 85%"></div>
              </div>
            </div>
          </div>
          <div class="quantum-preview-stats">
            <div class="quantum-stat-item">
              <span class="quantum-stat-value">156</span>
              <span class="quantum-stat-label">NODES</span>
            </div>
            <div class="quantum-stat-item">
              <span class="quantum-stat-value">2.4K</span>
              <span class="quantum-stat-label">USERS</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 量子特性展示 -->
    <div class="quantum-features-showcase">
      <h4 class="quantum-features-title">{{ t('quantum.features.title') }}</h4>
      <div class="quantum-features-grid">
        <div v-for="feature in quantumFeatures" :key="feature.id" class="quantum-feature-item">
          <div class="quantum-feature-icon">
            <i :class="feature.icon"></i>
          </div>
          <div class="quantum-feature-content">
            <h5 class="quantum-feature-name">{{ feature.name }}</h5>
            <p class="quantum-feature-desc">{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 多语言翻译
const t = (key: string) => {
  const translations = {
    'quantum.theme.preview.title': '量子主题预览',
    'quantum.theme.dark': '量子深色主题',
    'quantum.theme.light': '量子浅色主题',
    'quantum.theme.dark.description': '深邃太空感的量子深色风格，强烈发光效果与数据流动画',
    'quantum.theme.light.description': '纯净科技感的量子浅色风格，柔和发光与玻璃质感',
    'quantum.common.button': '量子按钮',
    'quantum.features.title': '量子特性',
  }
  return translations[key] || key
}

// 量子特性数据
const quantumFeatures = ref([
  {
    id: 1,
    name: '量子发光',
    description: '动态量子发光效果',
    icon: 'i-carbon-flash'
  },
  {
    id: 2,
    name: '量子全息',
    description: '全息扫描与闪烁',
    icon: 'i-carbon-3d-cursor'
  },
  {
    id: 3,
    name: '量子数据流',
    description: '背景数据流动画',
    icon: 'i-carbon-flow'
  },
  {
    id: 4,
    name: '量子粒子',
    description: '粒子系统动画',
    icon: 'i-carbon-dot-mark'
  },
  {
    id: 5,
    name: '量子响应式',
    description: '多维空间适配',
    icon: 'i-carbon-devices'
  },
  {
    id: 6,
    name: '量子交互',
    description: '智能交互反馈',
    icon: 'i-carbon-touch-interaction'
  }
])
</script>

<style scoped>
.quantum-theme-preview-container {
  padding: var(--space-6);
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.75rem;
  backdrop-filter: blur(10px);
}

.quantum-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--quantum-border-color);
}

.quantum-preview-title {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--quantum-primary);
  font-family: var(--font-mono);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  text-shadow: var(--quantum-glow-primary);
}

.quantum-theme-controls {
  display: flex;
  gap: var(--space-4);
  align-items: center;
}

.quantum-preview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-8);
  margin-bottom: var(--space-8);
}

.quantum-preview-card {
  padding: var(--space-6);
  border: 1px solid;
  border-radius: 0.75rem;
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
}

.quantum-preview-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* 量子深色主题预览样式 */
.quantum-preview-card[data-theme="dark"] {
  background: var(--quantum-bg-primary);
  border-color: var(--quantum-border-color);
  color: var(--quantum-fg-primary);
  box-shadow: var(--quantum-glow-primary);
}

/* 量子浅色主题预览样式 */
.quantum-preview-card[data-theme="light"] {
  background: var(--quantum-bg-primary);
  border-color: var(--quantum-border-color);
  color: var(--quantum-fg-primary);
  box-shadow: var(--quantum-glow-primary);
}

.quantum-preview-theme-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  font-family: 'Courier New', monospace;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.quantum-preview-card[data-theme="dark"] .quantum-preview-theme-title {
  color: var(--quantum-primary);
  text-shadow: var(--quantum-glow-primary);
}

.quantum-preview-card[data-theme="light"] .quantum-preview-theme-title {
  color: var(--quantum-primary);
  text-shadow: var(--quantum-glow-primary);
}

.quantum-preview-description {
  font-size: 0.875rem;
  margin-bottom: 1rem;
  line-height: 1.5;
  opacity: 0.9;
}

.quantum-preview-elements {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.quantum-preview-btn {
  padding: var(--space-2) var(--space-4);
  border: 1px solid;
  border-radius: 0.5rem;
  background: transparent;
  font-family: var(--font-mono);
  font-size: var(--text-xs);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.quantum-preview-card[data-theme="dark"] .quantum-preview-btn {
  border-color: var(--quantum-primary);
  color: var(--quantum-primary);
}

.quantum-preview-card[data-theme="dark"] .quantum-preview-btn:hover {
  background: var(--quantum-bg-elevated);
  box-shadow: var(--quantum-glow-primary);
}

.quantum-preview-card[data-theme="light"] .quantum-preview-btn {
  border-color: var(--quantum-primary);
  color: var(--quantum-primary);
}

.quantum-preview-card[data-theme="light"] .quantum-preview-btn:hover {
  background: var(--quantum-bg-elevated);
  box-shadow: var(--quantum-glow-primary);
}

.quantum-preview-card-mini {
  padding: 0.75rem;
  border: 1px solid;
  border-radius: 0.5rem;
  text-align: center;
  position: relative;
  backdrop-filter: blur(5px);
}

.quantum-preview-card[data-theme="dark"] .quantum-preview-card-mini {
  background: var(--quantum-bg-elevated);
  border-color: var(--quantum-border-color);
}

.quantum-preview-card[data-theme="light"] .quantum-preview-card-mini {
  background: var(--quantum-bg-elevated);
  border-color: var(--quantum-border-color);
}

.quantum-preview-data {
  font-family: 'Courier New', monospace;
  font-weight: 700;
  font-size: 1.25rem;
}

.quantum-preview-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.quantum-preview-particle {
  position: absolute;
  width: 2px;
  height: 2px;
  border-radius: 50%;
  animation: quantumPreviewParticle 2s ease-in-out infinite;
}

.quantum-preview-card[data-theme="dark"] .quantum-preview-particle {
  background: var(--quantum-accent);
  box-shadow: 0 0 4px var(--quantum-accent);
}

.quantum-preview-card[data-theme="light"] .quantum-preview-particle {
  background: var(--quantum-accent);
  box-shadow: 0 0 4px var(--quantum-accent);
}

.quantum-preview-particle:nth-child(1) { top: 20%; left: 20%; animation-delay: 0s; }
.quantum-preview-particle:nth-child(2) { top: 60%; left: 80%; animation-delay: 0.4s; }
.quantum-preview-particle:nth-child(3) { top: 80%; left: 30%; animation-delay: 0.8s; }
.quantum-preview-particle:nth-child(4) { top: 40%; left: 70%; animation-delay: 1.2s; }
.quantum-preview-particle:nth-child(5) { top: 10%; left: 60%; animation-delay: 1.6s; }

.quantum-preview-progress {
  margin-top: 0.5rem;
}

.quantum-progress-bar {
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.quantum-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--quantum-primary), var(--quantum-accent));
  border-radius: 2px;
  transition: width var(--transition-normal);
  position: relative;
}

.quantum-progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: quantumProgressScan 2s linear infinite;
}

.quantum-preview-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

.quantum-stat-item {
  text-align: center;
  padding: 0.5rem;
  border: 1px solid;
  border-radius: 0.375rem;
  backdrop-filter: blur(5px);
}

.quantum-preview-card[data-theme="dark"] .quantum-stat-item {
  background: var(--quantum-bg-elevated);
  border-color: var(--quantum-border-color);
}

.quantum-preview-card[data-theme="light"] .quantum-stat-item {
  background: var(--quantum-bg-elevated);
  border-color: var(--quantum-border-color);
}

.quantum-stat-value {
  display: block;
  font-family: 'Courier New', monospace;
  font-weight: 700;
  font-size: 1rem;
}

.quantum-stat-label {
  display: block;
  font-size: 0.625rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  opacity: 0.8;
  margin-top: 0.25rem;
}

/* 量子特性展示 */
.quantum-features-showcase {
  border-top: 1px solid var(--quantum-border-color);
  padding-top: var(--space-6);
}

.quantum-features-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--quantum-primary);
  margin-bottom: var(--space-4);
  font-family: var(--font-mono);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.quantum-features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.quantum-feature-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  background: var(--quantum-bg-elevated);
  transition: all var(--transition-fast);
}

.quantum-feature-item:hover {
  border-color: var(--quantum-primary);
  background: rgba(0, 255, 255, 0.1);
  transform: translateY(-2px);
}

.quantum-feature-icon {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--quantum-primary);
  color: #000;
  font-size: 1rem;
}

.quantum-feature-name {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-1);
  color: var(--quantum-fg-primary);
}

.quantum-feature-desc {
  font-size: var(--text-xs);
  opacity: 0.8;
  line-height: 1.4;
}

/* 动画 */
@keyframes quantumPreviewParticle {
  0%, 100% { 
    transform: translateY(0) scale(1); 
    opacity: 0.6; 
  }
  50% { 
    transform: translateY(-8px) scale(1.2); 
    opacity: 1; 
  }
}

@keyframes quantumProgressScan {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 响应式 */
@media (max-width: 768px) {
  .quantum-preview-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .quantum-preview-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .quantum-features-grid {
    grid-template-columns: 1fr;
  }
}
</style>
