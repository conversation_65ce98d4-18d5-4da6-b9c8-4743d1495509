<template>
  <div 
    class="responsive-grid"
    :class="[
      `grid-${columns}`,
      `gap-${gap}`,
      { 'grid-auto-fit': autoFit },
      { 'grid-equal-height': equalHeight }
    ]"
    :style="gridStyle"
  >
    <div
      v-for="(item, index) in items"
      :key="getItemKey(item, index)"
      class="grid-item"
      :class="[
        getItemClass(item, index),
        { 'item-loading': isLoading(item) }
      ]"
      :style="getItemStyle(item, index)"
    >
      <!-- 加载状态 -->
      <div v-if="isLoading(item)" class="item-loading-overlay">
        <div class="loading-skeleton quantum-data-stream"></div>
        <div class="loading-spinner">
          <i class="i-carbon-circle-dash animate-spin text-[var(--quantum-primary)]"></i>
        </div>
      </div>

      <!-- 实际内容 -->
      <slot 
        v-else
        :item="item" 
        :index="index"
        :isVisible="isItemVisible(index)"
        :breakpoint="currentBreakpoint"
      >
        <div class="default-grid-item quantum-card-hologram p-4 rounded-lg">
          <h3 class="item-title text-lg font-semibold quantum-text-matrix mb-2">
            {{ getItemTitle(item) }}
          </h3>
          <p class="item-content text-sm text-[var(--quantum-fg-secondary)]">
            {{ getItemContent(item) }}
          </p>
        </div>
      </slot>
    </div>

    <!-- 空状态 -->
    <div v-if="items.length === 0 && !loading" class="grid-empty-state" :style="{ gridColumn: '1 / -1' }">
      <div class="empty-content quantum-card-hologram p-8 rounded-xl text-center">
        <div class="empty-icon quantum-energy-ring w-16 h-16 mx-auto mb-4 flex items-center justify-center">
          <i class="i-carbon-no-image text-3xl text-[var(--quantum-fg-muted)]"></i>
        </div>
        <h3 class="empty-title text-lg font-semibold quantum-text-matrix mb-2">暂无数据</h3>
        <p class="empty-description text-sm text-[var(--quantum-fg-secondary)] mb-4">
          {{ emptyText || '当前网格为空，请添加一些内容' }}
        </p>
        <slot name="empty-action"></slot>
      </div>
    </div>

    <!-- 加载更多 -->
    <div v-if="hasMore && !loading" class="grid-load-more" :style="{ gridColumn: '1 / -1' }">
      <button @click="loadMore" class="load-more-btn quantum-card-hologram p-4 rounded-lg w-full">
        <i class="i-carbon-add text-[var(--quantum-primary)] mr-2"></i>
        <span>加载更多</span>
      </button>
    </div>

    <!-- 全局加载状态 -->
    <div v-if="loading" class="grid-loading" :style="{ gridColumn: '1 / -1' }">
      <div class="loading-content quantum-card-hologram p-6 rounded-lg text-center">
        <div class="loading-spinner mb-3">
          <i class="i-carbon-circle-dash animate-spin text-[var(--quantum-primary)] text-2xl"></i>
        </div>
        <p class="loading-text text-sm text-[var(--quantum-fg-secondary)]">{{ loadingText || '加载中...' }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

interface ResponsiveColumns {
  xs?: number
  sm?: number
  md?: number
  lg?: number
  xl?: number
  '2xl'?: number
}

interface Props {
  items: any[]
  columns?: number | ResponsiveColumns
  gap?: number | string
  minItemWidth?: string
  maxItemWidth?: string
  autoFit?: boolean
  equalHeight?: boolean
  loading?: boolean
  hasMore?: boolean
  emptyText?: string
  loadingText?: string
  keyField?: string
  itemClass?: string | ((item: any, index: number) => string)
  itemStyle?: object | ((item: any, index: number) => object)
  virtualScroll?: boolean
  itemHeight?: number
}

const props = withDefaults(defineProps<Props>(), {
  items: () => [],
  columns: () => ({ xs: 1, sm: 2, md: 3, lg: 4, xl: 5, '2xl': 6 }),
  gap: 4,
  minItemWidth: '200px',
  maxItemWidth: 'none',
  autoFit: false,
  equalHeight: false,
  loading: false,
  hasMore: false,
  emptyText: '',
  loadingText: '',
  keyField: 'id',
  itemClass: '',
  itemStyle: () => ({}),
  virtualScroll: false,
  itemHeight: 200
})

const emit = defineEmits<{
  loadMore: []
  itemClick: [item: any, index: number]
  itemVisible: [item: any, index: number]
}>()

// 响应式状态
const currentBreakpoint = ref('lg')
const containerWidth = ref(0)
const visibleItems = ref(new Set<number>())

// 断点定义
const breakpoints = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
}

// 计算当前列数
const currentColumns = computed(() => {
  if (typeof props.columns === 'number') {
    return props.columns
  }
  
  const responsive = props.columns as ResponsiveColumns
  const bp = currentBreakpoint.value as keyof ResponsiveColumns
  
  // 从当前断点向下查找
  const bpKeys = Object.keys(breakpoints) as (keyof ResponsiveColumns)[]
  const currentIndex = bpKeys.indexOf(bp)
  
  for (let i = currentIndex; i >= 0; i--) {
    const key = bpKeys[i]
    if (responsive[key] !== undefined) {
      return responsive[key]!
    }
  }
  
  return 1
})

// 计算网格样式
const gridStyle = computed(() => {
  const styles: any = {}
  
  if (props.autoFit) {
    styles.gridTemplateColumns = `repeat(auto-fit, minmax(${props.minItemWidth}, 1fr))`
  } else {
    styles.gridTemplateColumns = `repeat(${currentColumns.value}, 1fr)`
  }
  
  if (props.maxItemWidth !== 'none') {
    styles.maxWidth = `calc(${currentColumns.value} * ${props.maxItemWidth} + ${currentColumns.value - 1} * var(--grid-gap))`
    styles.margin = '0 auto'
  }
  
  if (props.equalHeight) {
    styles.gridAutoRows = '1fr'
  }
  
  return styles
})

// 方法
const getItemKey = (item: any, index: number) => {
  if (props.keyField && item[props.keyField] !== undefined) {
    return item[props.keyField]
  }
  return index
}

const getItemClass = (item: any, index: number) => {
  if (typeof props.itemClass === 'function') {
    return props.itemClass(item, index)
  }
  return props.itemClass
}

const getItemStyle = (item: any, index: number) => {
  if (typeof props.itemStyle === 'function') {
    return props.itemStyle(item, index)
  }
  return props.itemStyle
}

const getItemTitle = (item: any) => {
  return item.title || item.name || `项目 ${item.id || ''}`
}

const getItemContent = (item: any) => {
  return item.description || item.content || '暂无描述'
}

const isLoading = (item: any) => {
  return item._loading || false
}

const isItemVisible = (index: number) => {
  return visibleItems.value.has(index)
}

const loadMore = () => {
  emit('loadMore')
}

// 响应式断点检测
const updateBreakpoint = () => {
  const width = window.innerWidth
  
  for (const [bp, minWidth] of Object.entries(breakpoints).reverse()) {
    if (width >= minWidth) {
      currentBreakpoint.value = bp
      break
    }
  }
}

// 容器宽度检测
const updateContainerWidth = () => {
  // 这里可以添加容器宽度检测逻辑
  containerWidth.value = window.innerWidth
}

// Intersection Observer for visibility tracking
let intersectionObserver: IntersectionObserver | null = null

const setupIntersectionObserver = () => {
  if (typeof window === 'undefined' || !('IntersectionObserver' in window)) return
  
  intersectionObserver = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        const index = parseInt(entry.target.getAttribute('data-index') || '0')
        
        if (entry.isIntersecting) {
          visibleItems.value.add(index)
          emit('itemVisible', props.items[index], index)
        } else {
          visibleItems.value.delete(index)
        }
      })
    },
    {
      rootMargin: '50px',
      threshold: 0.1
    }
  )
}

// 监听器
const resizeObserver = ref<ResizeObserver>()

const handleResize = () => {
  updateBreakpoint()
  updateContainerWidth()
}

// 防抖的resize处理
let resizeTimer: NodeJS.Timeout | null = null
const debouncedResize = () => {
  if (resizeTimer) clearTimeout(resizeTimer)
  resizeTimer = setTimeout(handleResize, 100)
}

onMounted(() => {
  updateBreakpoint()
  updateContainerWidth()
  setupIntersectionObserver()
  
  window.addEventListener('resize', debouncedResize)
  
  // 设置ResizeObserver用于容器大小变化
  if (typeof ResizeObserver !== 'undefined') {
    resizeObserver.value = new ResizeObserver(debouncedResize)
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', debouncedResize)
  intersectionObserver?.disconnect()
  resizeObserver.value?.disconnect()
  if (resizeTimer) clearTimeout(resizeTimer)
})

// 监听items变化，更新可见性追踪
watch(() => props.items.length, () => {
  visibleItems.value.clear()
})

// 暴露方法
defineExpose({
  refresh: () => {
    updateBreakpoint()
    updateContainerWidth()
  },
  getCurrentBreakpoint: () => currentBreakpoint.value,
  getCurrentColumns: () => currentColumns.value
})
</script>

<style scoped>
.responsive-grid {
  display: grid;
  width: 100%;
  --grid-gap: 1rem;
}

/* 间距类 */
.gap-1 { gap: 0.25rem; --grid-gap: 0.25rem; }
.gap-2 { gap: 0.5rem; --grid-gap: 0.5rem; }
.gap-3 { gap: 0.75rem; --grid-gap: 0.75rem; }
.gap-4 { gap: 1rem; --grid-gap: 1rem; }
.gap-5 { gap: 1.25rem; --grid-gap: 1.25rem; }
.gap-6 { gap: 1.5rem; --grid-gap: 1.5rem; }
.gap-8 { gap: 2rem; --grid-gap: 2rem; }

/* 网格项 */
.grid-item {
  position: relative;
  overflow: hidden;
  transition: all var(--quantum-transition-fast);
}

.grid-item:hover {
  transform: translateY(-2px);
}

/* 加载状态 */
.item-loading {
  pointer-events: none;
}

.item-loading-overlay {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--quantum-bg-surface);
  border-radius: 0.5rem;
  z-index: 10;
}

.loading-skeleton {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.loading-spinner {
  position: relative;
  z-index: 1;
}

/* 默认网格项样式 */
.default-grid-item {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all var(--quantum-transition-fast);
}

.default-grid-item:hover {
  border-color: var(--quantum-border-glow);
  box-shadow: var(--quantum-glow-primary);
}

.item-title {
  margin-bottom: 0.5rem;
}

.item-content {
  flex: 1;
  line-height: 1.5;
}

/* 空状态 */
.grid-empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.empty-content {
  max-width: 24rem;
  width: 100%;
}

/* 加载更多 */
.grid-load-more {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.load-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--quantum-transition-fast);
  cursor: pointer;
  border: 1px solid var(--quantum-border-color);
}

.load-more-btn:hover {
  border-color: var(--quantum-primary);
  background: var(--quantum-bg-elevated);
  transform: translateY(-1px);
}

/* 全局加载状态 */
.grid-loading {
  display: flex;
  justify-content: center;
  padding: 2rem 0;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 200px;
}

/* 等高网格 */
.grid-equal-height .grid-item {
  display: flex;
  flex-direction: column;
}

.grid-equal-height .grid-item > * {
  flex: 1;
}

/* 动画 */
@keyframes skeleton-loading {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式优化 */
@media (max-width: 640px) {
  .responsive-grid {
    --grid-gap: 0.75rem;
    gap: 0.75rem;
  }
  
  .grid-item {
    min-height: 150px;
  }
  
  .empty-content {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .responsive-grid {
    --grid-gap: 0.5rem;
    gap: 0.5rem;
  }
  
  .default-grid-item {
    padding: 0.75rem;
  }
  
  .item-title {
    font-size: 1rem;
  }
  
  .item-content {
    font-size: 0.8rem;
  }
}

/* 性能优化 */
.grid-item {
  contain: layout style paint;
}

/* 自动适应网格 */
.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(var(--min-item-width, 200px), 1fr)) !important;
}

/* 打印样式 */
@media print {
  .responsive-grid {
    display: block;
  }
  
  .grid-item {
    break-inside: avoid;
    margin-bottom: 1rem;
  }
  
  .loading-spinner,
  .load-more-btn {
    display: none;
  }
}
</style>
