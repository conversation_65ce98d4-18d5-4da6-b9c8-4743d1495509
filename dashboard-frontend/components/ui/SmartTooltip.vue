<template>
  <div 
    ref="triggerRef"
    class="tooltip-trigger"
    @mouseenter="onMouseEnter"
    @mouseleave="onMouseLeave"
    @focus="onFocus"
    @blur="onBlur"
    @click="onClick"
  >
    <slot></slot>
    
    <!-- Tooltip内容 -->
    <Teleport to="body">
      <div
        v-if="isVisible"
        ref="tooltipRef"
        class="smart-tooltip"
        :class="[
          `tooltip-${placement}`,
          `tooltip-${theme}`,
          { 'tooltip-interactive': interactive }
        ]"
        :style="tooltipStyle"
        @mouseenter="onTooltipMouseEnter"
        @mouseleave="onTooltipMouseLeave"
      >
        <!-- 箭头 -->
        <div class="tooltip-arrow" :class="`arrow-${placement}`"></div>
        
        <!-- 内容 -->
        <div class="tooltip-content">
          <!-- 标题 -->
          <div v-if="title" class="tooltip-title">{{ title }}</div>
          
          <!-- 主要内容 -->
          <div class="tooltip-body">
            <slot name="content">
              {{ content }}
            </slot>
          </div>
          
          <!-- 快捷键提示 -->
          <div v-if="shortcut" class="tooltip-shortcut">
            <kbd class="shortcut-key">{{ shortcut }}</kbd>
          </div>
          
          <!-- 操作按钮 -->
          <div v-if="actions && actions.length > 0" class="tooltip-actions">
            <button
              v-for="action in actions"
              :key="action.key"
              @click="handleAction(action)"
              class="tooltip-action-btn"
              :class="action.type || 'secondary'"
            >
              <i v-if="action.icon" :class="action.icon" class="action-icon"></i>
              {{ action.label }}
            </button>
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

interface TooltipAction {
  key: string
  label: string
  icon?: string
  type?: 'primary' | 'secondary' | 'danger'
  handler: () => void
}

interface Props {
  content?: string
  title?: string
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'auto'
  trigger?: 'hover' | 'click' | 'focus' | 'manual'
  theme?: 'dark' | 'light' | 'quantum'
  delay?: number
  hideDelay?: number
  interactive?: boolean
  disabled?: boolean
  shortcut?: string
  actions?: TooltipAction[]
  maxWidth?: string
  zIndex?: number
}

const props = withDefaults(defineProps<Props>(), {
  content: '',
  title: '',
  placement: 'auto',
  trigger: 'hover',
  theme: 'quantum',
  delay: 100,
  hideDelay: 100,
  interactive: false,
  disabled: false,
  shortcut: '',
  actions: () => [],
  maxWidth: '300px',
  zIndex: 9999
})

const emit = defineEmits<{
  show: []
  hide: []
  action: [action: TooltipAction]
}>()

// 引用
const triggerRef = ref<HTMLElement>()
const tooltipRef = ref<HTMLElement>()

// 状态
const isVisible = ref(false)
const currentPlacement = ref(props.placement)
const tooltipPosition = ref({ x: 0, y: 0 })

// 定时器
let showTimer: NodeJS.Timeout | null = null
let hideTimer: NodeJS.Timeout | null = null

// 计算样式
const tooltipStyle = computed(() => ({
  position: 'absolute',
  left: `${tooltipPosition.value.x}px`,
  top: `${tooltipPosition.value.y}px`,
  maxWidth: props.maxWidth,
  zIndex: props.zIndex
}))

// 方法
const show = async () => {
  if (props.disabled || isVisible.value) return
  
  clearTimeout(hideTimer)
  
  showTimer = setTimeout(async () => {
    isVisible.value = true
    emit('show')
    
    await nextTick()
    updatePosition()
  }, props.delay)
}

const hide = () => {
  if (!isVisible.value) return
  
  clearTimeout(showTimer)
  
  hideTimer = setTimeout(() => {
    isVisible.value = false
    emit('hide')
  }, props.hideDelay)
}

const updatePosition = () => {
  if (!triggerRef.value || !tooltipRef.value) return
  
  const triggerRect = triggerRef.value.getBoundingClientRect()
  const tooltipRect = tooltipRef.value.getBoundingClientRect()
  const viewport = {
    width: window.innerWidth,
    height: window.innerHeight
  }
  
  let placement = props.placement
  let x = 0
  let y = 0
  
  // 自动计算最佳位置
  if (placement === 'auto') {
    const spaceTop = triggerRect.top
    const spaceBottom = viewport.height - triggerRect.bottom
    const spaceLeft = triggerRect.left
    const spaceRight = viewport.width - triggerRect.right
    
    if (spaceTop > tooltipRect.height && spaceTop > spaceBottom) {
      placement = 'top'
    } else if (spaceBottom > tooltipRect.height) {
      placement = 'bottom'
    } else if (spaceRight > tooltipRect.width) {
      placement = 'right'
    } else {
      placement = 'left'
    }
  }
  
  // 计算位置
  switch (placement) {
    case 'top':
      x = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2
      y = triggerRect.top - tooltipRect.height - 8
      break
    case 'bottom':
      x = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2
      y = triggerRect.bottom + 8
      break
    case 'left':
      x = triggerRect.left - tooltipRect.width - 8
      y = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2
      break
    case 'right':
      x = triggerRect.right + 8
      y = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2
      break
  }
  
  // 边界检查和调整
  x = Math.max(8, Math.min(x, viewport.width - tooltipRect.width - 8))
  y = Math.max(8, Math.min(y, viewport.height - tooltipRect.height - 8))
  
  currentPlacement.value = placement
  tooltipPosition.value = { x, y }
}

// 事件处理
const onMouseEnter = () => {
  if (props.trigger === 'hover') {
    show()
  }
}

const onMouseLeave = () => {
  if (props.trigger === 'hover' && !props.interactive) {
    hide()
  }
}

const onFocus = () => {
  if (props.trigger === 'focus') {
    show()
  }
}

const onBlur = () => {
  if (props.trigger === 'focus') {
    hide()
  }
}

const onClick = () => {
  if (props.trigger === 'click') {
    if (isVisible.value) {
      hide()
    } else {
      show()
    }
  }
}

const onTooltipMouseEnter = () => {
  if (props.interactive) {
    clearTimeout(hideTimer)
  }
}

const onTooltipMouseLeave = () => {
  if (props.interactive && props.trigger === 'hover') {
    hide()
  }
}

const handleAction = (action: TooltipAction) => {
  action.handler()
  emit('action', action)
  if (!props.interactive) {
    hide()
  }
}

// 键盘事件
const onKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isVisible.value) {
    hide()
  }
}

// 点击外部关闭
const onClickOutside = (event: MouseEvent) => {
  if (
    props.trigger === 'click' &&
    isVisible.value &&
    triggerRef.value &&
    tooltipRef.value &&
    !triggerRef.value.contains(event.target as Node) &&
    !tooltipRef.value.contains(event.target as Node)
  ) {
    hide()
  }
}

// 暴露方法
defineExpose({
  show,
  hide,
  isVisible: () => isVisible.value
})

onMounted(() => {
  document.addEventListener('keydown', onKeydown)
  document.addEventListener('click', onClickOutside)
  window.addEventListener('scroll', updatePosition)
  window.addEventListener('resize', updatePosition)
})

onUnmounted(() => {
  clearTimeout(showTimer)
  clearTimeout(hideTimer)
  document.removeEventListener('keydown', onKeydown)
  document.removeEventListener('click', onClickOutside)
  window.removeEventListener('scroll', updatePosition)
  window.removeEventListener('resize', updatePosition)
})
</script>

<style scoped>
.tooltip-trigger {
  display: inline-block;
}

.smart-tooltip {
  position: absolute;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.4;
  box-shadow: var(--quantum-shadow-lg);
  backdrop-filter: blur(10px);
  animation: tooltipFadeIn 0.2s ease-out;
  transform-origin: center;
}

/* 主题样式 */
.tooltip-quantum {
  background: var(--quantum-bg-surface);
  border: 1px solid var(--quantum-border-glow);
  color: var(--quantum-fg-primary);
}

.tooltip-dark {
  background: var(--quantum-bg-surface);
  color: var(--quantum-fg-primary);
  border: 1px solid var(--quantum-border-color);
}

.tooltip-light {
  background: var(--quantum-bg-surface);
  border: 1px solid var(--quantum-border-color);
  color: var(--quantum-fg-primary);
  box-shadow: var(--quantum-shadow-normal);
}

/* 箭头 */
.tooltip-arrow {
  position: absolute;
  width: 8px;
  height: 8px;
  transform: rotate(45deg);
}

.tooltip-quantum .tooltip-arrow {
  background: var(--quantum-bg-surface);
  border: 1px solid var(--quantum-border-glow);
}

.tooltip-dark .tooltip-arrow {
  background: var(--quantum-bg-surface);
  border: 1px solid var(--quantum-border-color);
}

.tooltip-light .tooltip-arrow {
  background: var(--quantum-bg-surface);
  border: 1px solid var(--quantum-border-color);
}

/* 箭头位置 */
.arrow-top {
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  border-top: none;
  border-left: none;
}

.arrow-bottom {
  top: -5px;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  border-bottom: none;
  border-right: none;
}

.arrow-left {
  right: -5px;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  border-left: none;
  border-bottom: none;
}

.arrow-right {
  left: -5px;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  border-right: none;
  border-top: none;
}

/* 内容 */
.tooltip-content {
  padding: 0.75rem;
}

.tooltip-title {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--quantum-fg-primary);
  font-size: 0.9rem;
}

.tooltip-body {
  margin-bottom: 0.5rem;
}

.tooltip-body:last-child {
  margin-bottom: 0;
}

/* 快捷键 */
.tooltip-shortcut {
  margin-top: 0.5rem;
  text-align: right;
}

.shortcut-key {
  display: inline-block;
  padding: 0.125rem 0.375rem;
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-family: monospace;
  color: var(--quantum-fg-secondary);
}

/* 操作按钮 */
.tooltip-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;
  padding-top: 0.5rem;
  border-top: 1px solid var(--quantum-border-subtle);
}

.tooltip-action-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--quantum-transition-fast);
  border: 1px solid transparent;
}

.tooltip-action-btn.primary {
  background: var(--quantum-primary);
  color: white;
}

.tooltip-action-btn.primary:hover {
  background: var(--quantum-primary-hover);
}

.tooltip-action-btn.secondary {
  background: var(--quantum-bg-elevated);
  color: var(--quantum-fg-secondary);
  border-color: var(--quantum-border-color);
}

.tooltip-action-btn.secondary:hover {
  background: var(--quantum-bg-hover);
  color: var(--quantum-fg-primary);
}

.tooltip-action-btn.danger {
  background: var(--quantum-error);
  color: white;
}

.tooltip-action-btn.danger:hover {
  background: var(--quantum-error-hover);
}

.action-icon {
  font-size: 0.75rem;
}

/* 交互式tooltip */
.tooltip-interactive {
  cursor: default;
}

/* 动画 */
@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式 */
@media (max-width: 768px) {
  .smart-tooltip {
    max-width: calc(100vw - 2rem) !important;
    font-size: 0.8rem;
  }
  
  .tooltip-content {
    padding: 0.5rem;
  }
  
  .tooltip-actions {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .tooltip-action-btn {
    justify-content: center;
  }
}
</style>
