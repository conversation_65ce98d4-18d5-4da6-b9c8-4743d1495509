<template>
  <div class="quantum-globe-container" ref="globeContainer">
    <!-- 🌍 真正的3D地球画布 -->
    <div ref="threeContainer" class="three-globe-canvas"></div>

    <!-- 🎛️ 地球控制面板 -->
    <div class="quantum-globe-controls">
      <div class="control-group">
        <button
          @click="toggleRotation"
          class="quantum-control-btn"
          :class="{ active: isRotating }"
        >
          <i class="i-carbon-rotate-360"></i>
          <span>{{ isRotating ? 'PAUSE' : 'ROTATE' }}</span>
        </button>

        <button
          @click="resetView"
          class="quantum-control-btn"
        >
          <i class="i-carbon-reset"></i>
          <span>RESET</span>
        </button>

        <button
          @click="toggleWireframe"
          class="quantum-control-btn"
          :class="{ active: showWireframe }"
        >
          <i class="i-carbon-grid"></i>
          <span>GRID</span>
        </button>

        <button
          @click="toggleAtmosphere"
          class="quantum-control-btn"
          :class="{ active: showAtmosphere }"
        >
          <i class="i-carbon-cloud"></i>
          <span>ATMOSPHERE</span>
        </button>
      </div>

      <div class="control-group">
        <label class="quantum-control-label">
          <span>Visitor Intensity</span>
          <input
            v-model="visitorIntensity"
            type="range"
            min="0"
            max="100"
            class="quantum-slider"
            @input="updateVisitorIntensity"
          >
        </label>

        <label class="quantum-control-label">
          <span>Rotation Speed</span>
          <input
            v-model="rotationSpeed"
            type="range"
            min="0"
            max="5"
            step="0.1"
            class="quantum-slider"
          >
        </label>
      </div>
    </div>

    <!-- 🏷️ 信息提示框 -->
    <div
      v-if="hoveredPoint"
      class="quantum-tooltip"
      :style="tooltipStyle"
    >
      <div class="tooltip-header">
        <span class="country-flag">{{ hoveredPoint.flag }}</span>
        <span class="country-name">{{ hoveredPoint.country }}</span>
      </div>
      <div class="tooltip-stats">
        <div class="stat-item">
          <span class="stat-label">Visitors:</span>
          <span class="stat-value">{{ formatNumber(hoveredPoint.visitors) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Sessions:</span>
          <span class="stat-value">{{ formatNumber(hoveredPoint.sessions) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Growth:</span>
          <span class="stat-value" :class="getGrowthClass(hoveredPoint.growth)">
            {{ hoveredPoint.growth > 0 ? '+' : '' }}{{ hoveredPoint.growth }}%
          </span>
        </div>
      </div>
    </div>

    <!-- 📊 实时数据流 -->
    <div class="quantum-data-streams" v-if="showDataStreams">
      <div
        v-for="stream in activeDataStreams"
        :key="stream.id"
        class="data-stream-particle"
        :style="stream.style"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue'
import * as THREE from 'three'

// Props
interface Props {
  visitorData?: Array<{
    id: string
    country: string
    flag: string
    lat: number
    lng: number
    visitors: number
    sessions: number
    growth: number
  }>
  showConnections?: boolean
  autoRotate?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visitorData: () => [],
  showConnections: true,
  autoRotate: true
})

// Refs
const globeContainer = ref<HTMLDivElement>()
const threeContainer = ref<HTMLDivElement>()

// Three.js 核心对象
let scene: THREE.Scene
let camera: THREE.PerspectiveCamera
let renderer: THREE.WebGLRenderer
let earth: THREE.Mesh
let atmosphere: THREE.Mesh
let earthGroup: THREE.Group
let visitorPoints: THREE.Group
let dataStreams: THREE.Group

// 状态
const isRotating = ref(props.autoRotate)
const showWireframe = ref(false)
const showAtmosphere = ref(true)
const showDataStreams = ref(true)
const visitorIntensity = ref(75)
const rotationSpeed = ref(1)
const hoveredPoint = ref(null)
const tooltipStyle = ref({})
const activeDataStreams = ref([])

// 鼠标控制
let mouseX = 0
let mouseY = 0
let targetRotationX = 0
let targetRotationY = 0

// 模拟访客数据
const defaultVisitorData = [
  { id: '1', country: 'United States', flag: '🇺🇸', lat: 39.8283, lng: -98.5795, visitors: 456000, sessions: 1200000, growth: 12.5 },
  { id: '2', country: 'China', flag: '🇨🇳', lat: 35.8617, lng: 104.1954, visitors: 389000, sessions: 980000, growth: 18.2 },
  { id: '3', country: 'Japan', flag: '🇯🇵', lat: 36.2048, lng: 138.2529, visitors: 234000, sessions: 650000, growth: 8.7 },
  { id: '4', country: 'Germany', flag: '🇩🇪', lat: 51.1657, lng: 10.4515, visitors: 189000, sessions: 520000, growth: 15.3 },
  { id: '5', country: 'United Kingdom', flag: '🇬🇧', lat: 55.3781, lng: -3.4360, visitors: 167000, sessions: 445000, growth: 9.8 },
  { id: '6', country: 'France', flag: '🇫🇷', lat: 46.2276, lng: 2.2137, visitors: 145000, sessions: 380000, growth: 11.2 },
  { id: '7', country: 'Canada', flag: '🇨🇦', lat: 56.1304, lng: -106.3468, visitors: 123000, sessions: 320000, growth: 14.7 },
  { id: '8', country: 'Australia', flag: '🇦🇺', lat: -25.2744, lng: 133.7751, visitors: 98000, sessions: 260000, growth: 16.4 },
  { id: '9', country: 'Brazil', flag: '🇧🇷', lat: -14.2350, lng: -51.9253, visitors: 87000, sessions: 230000, growth: 22.1 },
  { id: '10', country: 'India', flag: '🇮🇳', lat: 20.5937, lng: 78.9629, visitors: 76000, sessions: 195000, growth: 28.5 },
  { id: '11', country: 'Russia', flag: '🇷🇺', lat: 61.5240, lng: 105.3188, visitors: 65000, sessions: 175000, growth: -3.2 },
  { id: '12', country: 'South Korea', flag: '🇰🇷', lat: 35.9078, lng: 127.7669, visitors: 54000, sessions: 145000, growth: 19.8 }
]

const visitorData = computed(() => props.visitorData.length > 0 ? props.visitorData : defaultVisitorData)

// Three.js 初始化
const initThreeJS = () => {
  if (!threeContainer.value) return

  // 场景
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0x000011)

  // 相机
  camera = new THREE.PerspectiveCamera(75, threeContainer.value.clientWidth / threeContainer.value.clientHeight, 0.1, 1000)
  camera.position.z = 3

  // 渲染器
  renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true })
  renderer.setSize(threeContainer.value.clientWidth, threeContainer.value.clientHeight)
  renderer.setPixelRatio(window.devicePixelRatio)
  renderer.shadowMap.enabled = true
  renderer.shadowMap.type = THREE.PCFSoftShadowMap
  threeContainer.value.appendChild(renderer.domElement)

  // 创建地球组
  earthGroup = new THREE.Group()
  scene.add(earthGroup)

  // 创建访客点组
  visitorPoints = new THREE.Group()
  earthGroup.add(visitorPoints)

  // 创建数据流组
  dataStreams = new THREE.Group()
  earthGroup.add(dataStreams)

  // 创建地球
  createEarth()

  // 创建大气层
  createAtmosphere()

  // 创建访客点
  createVisitorPoints()

  // 创建星空背景
  createStarField()

  // 添加光照
  addLighting()

  // 添加事件监听
  addEventListeners()
}

// 创建地球
const createEarth = () => {
  const geometry = new THREE.SphereGeometry(1, 64, 64)

  // 地球材质
  const earthMaterial = new THREE.ShaderMaterial({
    uniforms: {
      time: { value: 0 },
      showWireframe: { value: showWireframe.value },
      primaryColor: { value: new THREE.Color(0x00d4ff) },
      secondaryColor: { value: new THREE.Color(0xff6b9d) },
      accentColor: { value: new THREE.Color(0x00ff88) }
    },
    vertexShader: `
      varying vec2 vUv;
      varying vec3 vPosition;
      varying vec3 vNormal;

      void main() {
        vUv = uv;
        vPosition = position;
        vNormal = normalize(normalMatrix * normal);
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `,
    fragmentShader: `
      uniform float time;
      uniform bool showWireframe;
      uniform vec3 primaryColor;
      uniform vec3 secondaryColor;
      uniform vec3 accentColor;

      varying vec2 vUv;
      varying vec3 vPosition;
      varying vec3 vNormal;

      // 简化的大陆形状函数
      float continents(vec2 uv) {
        float land = 0.0;

        // 北美洲
        vec2 na = uv - vec2(0.2, 0.7);
        land += smoothstep(0.15, 0.1, length(na)) * 0.8;

        // 南美洲
        vec2 sa = uv - vec2(0.25, 0.3);
        land += smoothstep(0.08, 0.05, length(sa)) * 0.7;

        // 欧洲
        vec2 eu = uv - vec2(0.52, 0.75);
        land += smoothstep(0.06, 0.04, length(eu)) * 0.6;

        // 非洲
        vec2 af = uv - vec2(0.55, 0.5);
        land += smoothstep(0.1, 0.07, length(af)) * 0.8;

        // 亚洲
        vec2 as = uv - vec2(0.7, 0.7);
        land += smoothstep(0.15, 0.12, length(as)) * 0.9;

        // 澳洲
        vec2 au = uv - vec2(0.8, 0.2);
        land += smoothstep(0.05, 0.03, length(au)) * 0.5;

        return clamp(land, 0.0, 1.0);
      }

      void main() {
        vec2 uv = vUv;

        // 基础海洋颜色
        vec3 oceanColor = mix(vec3(0.0, 0.1, 0.3), primaryColor * 0.3, 0.5);

        // 大陆颜色
        float landMask = continents(uv);
        vec3 landColor = mix(accentColor * 0.4, accentColor * 0.8, landMask);

        // 混合海洋和陆地
        vec3 baseColor = mix(oceanColor, landColor, landMask);

        // 网格线
        if (showWireframe) {
          float grid = 0.0;
          grid += smoothstep(0.98, 1.0, sin(uv.x * 3.14159 * 12.0));
          grid += smoothstep(0.98, 1.0, sin(uv.y * 3.14159 * 6.0));
          baseColor = mix(baseColor, primaryColor, grid * 0.5);
        }

        // 量子发光效果
        float fresnel = pow(1.0 - dot(vNormal, vec3(0.0, 0.0, 1.0)), 2.0);
        vec3 glowColor = primaryColor * fresnel * 0.5;

        // 时间动画
        float pulse = sin(time * 2.0) * 0.1 + 0.9;

        gl_FragColor = vec4(baseColor + glowColor * pulse, 1.0);
      }
    `,
    transparent: true
  })

  earth = new THREE.Mesh(geometry, earthMaterial)
  earth.castShadow = true
  earth.receiveShadow = true
  earthGroup.add(earth)
}

// 创建大气层
const createAtmosphere = () => {
  const geometry = new THREE.SphereGeometry(1.02, 32, 32)
  const material = new THREE.ShaderMaterial({
    uniforms: {
      time: { value: 0 },
      primaryColor: { value: new THREE.Color(0x00d4ff) }
    },
    vertexShader: `
      varying vec3 vNormal;
      void main() {
        vNormal = normalize(normalMatrix * normal);
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `,
    fragmentShader: `
      uniform float time;
      uniform vec3 primaryColor;
      varying vec3 vNormal;

      void main() {
        float fresnel = pow(1.0 - dot(vNormal, vec3(0.0, 0.0, 1.0)), 3.0);
        float pulse = sin(time * 1.5) * 0.2 + 0.8;
        gl_FragColor = vec4(primaryColor, fresnel * 0.3 * pulse);
      }
    `,
    transparent: true,
    side: THREE.BackSide
  })

  atmosphere = new THREE.Mesh(geometry, material)
  atmosphere.visible = showAtmosphere.value
  earthGroup.add(atmosphere)
}

// 方法
const toggleRotation = () => {
  isRotating.value = !isRotating.value
}

const resetView = () => {
  if (earthGroup) {
    earthGroup.rotation.x = 0
    earthGroup.rotation.y = 0
    camera.position.set(0, 0, 3)
  }
}

const toggleWireframe = () => {
  showWireframe.value = !showWireframe.value
  if (earth && earth.material) {
    earth.material.uniforms.showWireframe.value = showWireframe.value
  }
}

const toggleAtmosphere = () => {
  showAtmosphere.value = !showAtmosphere.value
  if (atmosphere) {
    atmosphere.visible = showAtmosphere.value
  }
}

// 创建访客点
const createVisitorPoints = () => {
  visitorData.value.forEach(point => {
    // 将经纬度转换为3D坐标
    const phi = (90 - point.lat) * (Math.PI / 180)
    const theta = (point.lng + 180) * (Math.PI / 180)

    const x = -(1.01 * Math.sin(phi) * Math.cos(theta))
    const y = 1.01 * Math.cos(phi)
    const z = 1.01 * Math.sin(phi) * Math.sin(theta)

    // 创建访客点几何体
    const size = Math.max(0.01, Math.min(0.05, point.visitors / 200000))
    const geometry = new THREE.SphereGeometry(size, 8, 8)

    // 访客点材质
    const material = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        intensity: { value: point.visitors / 500000 },
        primaryColor: { value: new THREE.Color(0x00d4ff) },
        accentColor: { value: new THREE.Color(0x00ff88) }
      },
      vertexShader: `
        uniform float time;
        varying vec3 vPosition;

        void main() {
          vPosition = position;
          vec3 pos = position;
          pos += normal * sin(time * 3.0) * 0.01;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform float intensity;
        uniform vec3 primaryColor;
        uniform vec3 accentColor;
        varying vec3 vPosition;

        void main() {
          float pulse = sin(time * 2.0 + intensity * 10.0) * 0.5 + 0.5;
          vec3 color = mix(primaryColor, accentColor, intensity);
          gl_FragColor = vec4(color, 0.8 + pulse * 0.2);
        }
      `,
      transparent: true
    })

    const pointMesh = new THREE.Mesh(geometry, material)
    pointMesh.position.set(x, y, z)
    pointMesh.userData = point
    visitorPoints.add(pointMesh)

    // 创建发光环
    const ringGeometry = new THREE.RingGeometry(size * 1.5, size * 2, 16)
    const ringMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        primaryColor: { value: new THREE.Color(0x00d4ff) }
      },
      vertexShader: `
        uniform float time;
        void main() {
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform vec3 primaryColor;

        void main() {
          float pulse = sin(time * 4.0) * 0.3 + 0.7;
          gl_FragColor = vec4(primaryColor, 0.3 * pulse);
        }
      `,
      transparent: true,
      side: THREE.DoubleSide
    })

    const ring = new THREE.Mesh(ringGeometry, ringMaterial)
    ring.position.set(x, y, z)
    ring.lookAt(0, 0, 0)
    visitorPoints.add(ring)
  })
}

// 创建星空背景
const createStarField = () => {
  const starGeometry = new THREE.BufferGeometry()
  const starCount = 1000
  const positions = new Float32Array(starCount * 3)

  for (let i = 0; i < starCount * 3; i += 3) {
    positions[i] = (Math.random() - 0.5) * 100
    positions[i + 1] = (Math.random() - 0.5) * 100
    positions[i + 2] = (Math.random() - 0.5) * 100
  }

  starGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))

  const starMaterial = new THREE.PointsMaterial({
    color: 0xffffff,
    size: 0.5,
    transparent: true,
    opacity: 0.8
  })

  const stars = new THREE.Points(starGeometry, starMaterial)
  scene.add(stars)
}

// 添加光照
const addLighting = () => {
  // 环境光
  const ambientLight = new THREE.AmbientLight(0x404040, 0.3)
  scene.add(ambientLight)

  // 主光源
  const directionalLight = new THREE.DirectionalLight(0xffffff, 1)
  directionalLight.position.set(5, 3, 5)
  directionalLight.castShadow = true
  scene.add(directionalLight)

  // 量子光源
  const quantumLight = new THREE.PointLight(0x00d4ff, 0.5, 10)
  quantumLight.position.set(2, 2, 2)
  scene.add(quantumLight)
}

// 添加事件监听
const addEventListeners = () => {
  const canvas = renderer.domElement

  canvas.addEventListener('mousemove', onMouseMove)
  canvas.addEventListener('click', onMouseClick)
  canvas.addEventListener('wheel', onMouseWheel)
  window.addEventListener('resize', onWindowResize)
}

const onMouseMove = (event: MouseEvent) => {
  const rect = renderer.domElement.getBoundingClientRect()
  mouseX = ((event.clientX - rect.left) / rect.width) * 2 - 1
  mouseY = -((event.clientY - rect.top) / rect.height) * 2 + 1

  // 射线检测
  const raycaster = new THREE.Raycaster()
  raycaster.setFromCamera(new THREE.Vector2(mouseX, mouseY), camera)

  const intersects = raycaster.intersectObjects(visitorPoints.children)
  if (intersects.length > 0) {
    const point = intersects[0].object.userData
    if (point) {
      hoveredPoint.value = point
      tooltipStyle.value = {
        left: `${event.clientX + 10}px`,
        top: `${event.clientY - 10}px`
      }
    }
  } else {
    hoveredPoint.value = null
  }
}

const onMouseClick = (event: MouseEvent) => {
  // 处理点击事件
}

const onMouseWheel = (event: WheelEvent) => {
  event.preventDefault()
  const delta = event.deltaY > 0 ? 1.1 : 0.9
  camera.position.z = Math.max(1.5, Math.min(10, camera.position.z * delta))
}

const onWindowResize = () => {
  if (!threeContainer.value) return

  camera.aspect = threeContainer.value.clientWidth / threeContainer.value.clientHeight
  camera.updateProjectionMatrix()
  renderer.setSize(threeContainer.value.clientWidth, threeContainer.value.clientHeight)
}

// 更新访客强度
const updateVisitorIntensity = () => {
  // 更新访客点的可见性和大小
}

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(0) + 'K'
  }
  return num.toString()
}

const getGrowthClass = (growth: number) => {
  return growth > 0 ? 'positive-growth' : 'negative-growth'
}

// 动画循环
let animationFrame: number
let time = 0

const animate = () => {
  time += 0.01

  if (earthGroup) {
    // 自动旋转
    if (isRotating.value) {
      earthGroup.rotation.y += rotationSpeed.value * 0.005
    }

    // 更新着色器时间
    if (earth && earth.material) {
      earth.material.uniforms.time.value = time
    }

    if (atmosphere && atmosphere.material) {
      atmosphere.material.uniforms.time.value = time
    }

    // 更新访客点动画
    visitorPoints.children.forEach(child => {
      if (child.material && child.material.uniforms) {
        child.material.uniforms.time.value = time
      }
    })
  }

  renderer.render(scene, camera)
  animationFrame = requestAnimationFrame(animate)
}

// 生命周期
onMounted(async () => {
  await nextTick()
  initThreeJS()
  animate()
})

onUnmounted(() => {
  if (animationFrame) {
    cancelAnimationFrame(animationFrame)
  }

  // 清理Three.js资源
  if (renderer) {
    const canvas = renderer.domElement
    canvas.removeEventListener('mousemove', onMouseMove)
    canvas.removeEventListener('click', onMouseClick)
    canvas.removeEventListener('wheel', onMouseWheel)
    window.removeEventListener('resize', onWindowResize)

    if (threeContainer.value && threeContainer.value.contains(canvas)) {
      threeContainer.value.removeChild(canvas)
    }

    renderer.dispose()
  }

  // 清理几何体和材质
  if (scene) {
    scene.traverse((object) => {
      if (object instanceof THREE.Mesh) {
        if (object.geometry) object.geometry.dispose()
        if (object.material) {
          if (Array.isArray(object.material)) {
            object.material.forEach(material => material.dispose())
          } else {
            object.material.dispose()
          }
        }
      }
    })
  }
})
</script>

<style scoped>
.quantum-globe-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center,
    rgba(0, 212, 255, 0.05) 0%,
    rgba(0, 0, 17, 0.95) 70%
  );
  border-radius: 1rem;
  overflow: hidden;
  border: 1px solid var(--quantum-border-color);
}

.three-globe-canvas {
  width: 100%;
  height: 100%;
  cursor: grab;
}

.three-globe-canvas:active {
  cursor: grabbing;
}

.three-globe-canvas canvas {
  border-radius: 1rem;
}

.quantum-globe-controls {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  z-index: 10;
}

.control-group {
  display: flex;
  gap: 0.5rem;
  background: var(--quantum-bg-glass);
  backdrop-filter: blur(10px);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  padding: 0.5rem;
}

.quantum-control-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  background: transparent;
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.25rem;
  color: var(--quantum-fg-secondary);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.quantum-control-btn:hover,
.quantum-control-btn.active {
  background: var(--quantum-primary);
  color: white;
  border-color: var(--quantum-primary);
  box-shadow: var(--quantum-glow-primary);
}

.quantum-control-label {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: var(--quantum-fg-secondary);
}

.quantum-slider {
  width: 100px;
  height: 4px;
  background: var(--quantum-border-color);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.quantum-data-streams {
  position: absolute;
  inset: 0;
  pointer-events: none;
  z-index: 5;
}

.data-stream-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--quantum-primary);
  border-radius: 50%;
  box-shadow: 0 0 10px var(--quantum-primary);
  animation: quantumStreamFlow 3s ease-in-out infinite;
}

.quantum-tooltip {
  position: absolute;
  background: var(--quantum-bg-surface);
  backdrop-filter: blur(20px);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  padding: 0.75rem;
  z-index: 20;
  min-width: 200px;
  box-shadow: var(--quantum-shadow-elevated);
}

.tooltip-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--quantum-border-color);
}

.country-flag {
  font-size: 1.25rem;
}

.country-name {
  font-weight: 600;
  color: var(--quantum-fg-primary);
}

.tooltip-stats {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
}

.stat-label {
  color: var(--quantum-fg-secondary);
}

.stat-value {
  font-weight: 600;
  color: var(--quantum-fg-primary);
}

.positive-growth {
  color: var(--quantum-success);
}

.negative-growth {
  color: var(--quantum-error);
}

.quantum-connections {
  position: absolute;
  inset: 0;
  pointer-events: none;
  z-index: 5;
}

.quantum-connection-line {
  stroke-width: 2;
  fill: none;
  animation: quantumConnectionFlow 3s ease-in-out infinite;
}

@keyframes quantumStreamFlow {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
}

@keyframes quantumGlow {
  0%, 100% {
    box-shadow: 0 0 10px var(--quantum-primary);
  }
  50% {
    box-shadow: 0 0 20px var(--quantum-primary), 0 0 30px var(--quantum-accent);
  }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .quantum-globe-controls {
    top: 0.5rem;
    right: 0.5rem;
  }

  .control-group {
    padding: 0.25rem;
    gap: 0.25rem;
  }

  .quantum-control-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.625rem;
  }

  .quantum-slider {
    width: 60px;
  }
}

@media (max-width: 480px) {
  .quantum-globe-controls {
    flex-direction: row;
    top: auto;
    bottom: 0.5rem;
    right: 0.5rem;
    left: 0.5rem;
  }

  .control-group {
    flex: 1;
    justify-content: center;
  }
}
</style>
