<template>
  <div v-if="showMonitor" class="performance-monitor">
    <div class="monitor-toggle" @click="toggleExpanded">
      <i :class="expanded ? 'i-carbon-chevron-down' : 'i-carbon-chevron-up'" class="toggle-icon"></i>
      <span class="monitor-title">性能监控</span>
      <div class="performance-indicator" :class="getPerformanceClass()">
        <span class="indicator-dot"></span>
      </div>
    </div>

    <div v-if="expanded" class="monitor-content">
      <!-- 核心性能指标 -->
      <div class="metrics-section">
        <h4 class="section-title">核心指标</h4>
        <div class="metrics-grid">
          <div class="metric-item">
            <span class="metric-label">FPS</span>
            <span class="metric-value" :class="getFPSClass()">{{ currentFPS }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">内存</span>
            <span class="metric-value">{{ formatMemory(memoryUsage) }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">DOM节点</span>
            <span class="metric-value">{{ domNodeCount }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">网络</span>
            <span class="metric-value" :class="getNetworkClass()">{{ networkStatus }}</span>
          </div>
        </div>
      </div>

      <!-- 页面加载性能 -->
      <div class="loading-section">
        <h4 class="section-title">加载性能</h4>
        <div class="loading-metrics">
          <div class="loading-item">
            <span class="loading-label">首次内容绘制</span>
            <span class="loading-value">{{ formatTime(performanceMetrics.fcp) }}</span>
          </div>
          <div class="loading-item">
            <span class="loading-label">最大内容绘制</span>
            <span class="loading-value">{{ formatTime(performanceMetrics.lcp) }}</span>
          </div>
          <div class="loading-item">
            <span class="loading-label">首次输入延迟</span>
            <span class="loading-value">{{ formatTime(performanceMetrics.fid) }}</span>
          </div>
          <div class="loading-item">
            <span class="loading-label">累积布局偏移</span>
            <span class="loading-value">{{ performanceMetrics.cls.toFixed(3) }}</span>
          </div>
        </div>
      </div>

      <!-- 资源统计 -->
      <div class="resources-section">
        <h4 class="section-title">资源统计</h4>
        <div class="resources-list">
          <div class="resource-item">
            <span class="resource-type">JS</span>
            <span class="resource-count">{{ resourceCounts.script }}</span>
            <span class="resource-size">{{ formatSize(resourceSizes.script) }}</span>
          </div>
          <div class="resource-item">
            <span class="resource-type">CSS</span>
            <span class="resource-count">{{ resourceCounts.stylesheet }}</span>
            <span class="resource-size">{{ formatSize(resourceSizes.stylesheet) }}</span>
          </div>
          <div class="resource-item">
            <span class="resource-type">IMG</span>
            <span class="resource-count">{{ resourceCounts.image }}</span>
            <span class="resource-size">{{ formatSize(resourceSizes.image) }}</span>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="actions-section">
        <button @click="clearCache" class="action-btn">
          <i class="i-carbon-clean"></i>
          清理缓存
        </button>
        <button @click="optimizePerformance" class="action-btn">
          <i class="i-carbon-flash"></i>
          性能优化
        </button>
        <button @click="exportReport" class="action-btn">
          <i class="i-carbon-document-export"></i>
          导出报告
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'

interface Props {
  enabled?: boolean
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
  autoHide?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  enabled: true,
  position: 'bottom-right',
  autoHide: false
})

// 状态
const showMonitor = ref(props.enabled)
const expanded = ref(false)
const currentFPS = ref(60)
const memoryUsage = ref(0)
const domNodeCount = ref(0)
const networkStatus = ref('good')

// 性能指标
const performanceMetrics = ref({
  fcp: 0, // First Contentful Paint
  lcp: 0, // Largest Contentful Paint
  fid: 0, // First Input Delay
  cls: 0  // Cumulative Layout Shift
})

// 资源统计
const resourceCounts = ref({
  script: 0,
  stylesheet: 0,
  image: 0
})

const resourceSizes = ref({
  script: 0,
  stylesheet: 0,
  image: 0
})

// 定时器
let fpsTimer: NodeJS.Timeout | null = null
let memoryTimer: NodeJS.Timeout | null = null
let frameCount = 0
let lastTime = performance.now()

// 计算属性
const getPerformanceClass = () => {
  const avgScore = (currentFPS.value / 60 + (memoryUsage.value < 50 ? 1 : 0.5)) / 2
  if (avgScore > 0.8) return 'performance-good'
  if (avgScore > 0.6) return 'performance-warning'
  return 'performance-poor'
}

const getFPSClass = () => {
  if (currentFPS.value >= 55) return 'fps-good'
  if (currentFPS.value >= 30) return 'fps-warning'
  return 'fps-poor'
}

const getNetworkClass = () => {
  switch (networkStatus.value) {
    case 'good': return 'network-good'
    case 'slow': return 'network-warning'
    case 'offline': return 'network-poor'
    default: return ''
  }
}

// 方法
const toggleExpanded = () => {
  expanded.value = !expanded.value
}

const formatTime = (time: number) => {
  if (time === 0) return '-'
  return `${time.toFixed(1)}ms`
}

const formatMemory = (memory: number) => {
  return `${memory.toFixed(1)}MB`
}

const formatSize = (size: number) => {
  if (size < 1024) return `${size}B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
  return `${(size / (1024 * 1024)).toFixed(1)}MB`
}

// FPS监控
const measureFPS = () => {
  const now = performance.now()
  frameCount++
  
  if (now - lastTime >= 1000) {
    currentFPS.value = Math.round((frameCount * 1000) / (now - lastTime))
    frameCount = 0
    lastTime = now
  }
  
  requestAnimationFrame(measureFPS)
}

// 内存监控
const measureMemory = () => {
  if ('memory' in performance) {
    const memory = (performance as any).memory
    memoryUsage.value = memory.usedJSHeapSize / (1024 * 1024)
  }
}

// DOM节点计数
const countDOMNodes = () => {
  domNodeCount.value = document.querySelectorAll('*').length
}

// 网络状态监控
const monitorNetwork = () => {
  if ('connection' in navigator) {
    const connection = (navigator as any).connection
    const effectiveType = connection.effectiveType
    
    switch (effectiveType) {
      case '4g':
        networkStatus.value = 'good'
        break
      case '3g':
        networkStatus.value = 'slow'
        break
      case '2g':
      case 'slow-2g':
        networkStatus.value = 'poor'
        break
      default:
        networkStatus.value = 'good'
    }
  }
}

// 性能指标收集
const collectPerformanceMetrics = () => {
  if ('PerformanceObserver' in window) {
    // FCP监控
    const fcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint')
      if (fcpEntry) {
        performanceMetrics.value.fcp = fcpEntry.startTime
      }
    })
    fcpObserver.observe({ entryTypes: ['paint'] })

    // LCP监控
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1]
      performanceMetrics.value.lcp = lastEntry.startTime
    })
    lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })

    // FID监控
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach(entry => {
        performanceMetrics.value.fid = entry.processingStart - entry.startTime
      })
    })
    fidObserver.observe({ entryTypes: ['first-input'] })

    // CLS监控
    const clsObserver = new PerformanceObserver((list) => {
      let clsValue = 0
      const entries = list.getEntries()
      entries.forEach(entry => {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value
        }
      })
      performanceMetrics.value.cls = clsValue
    })
    clsObserver.observe({ entryTypes: ['layout-shift'] })
  }
}

// 资源统计
const analyzeResources = () => {
  const resources = performance.getEntriesByType('resource')
  
  const counts = { script: 0, stylesheet: 0, image: 0 }
  const sizes = { script: 0, stylesheet: 0, image: 0 }
  
  resources.forEach((resource: any) => {
    const type = resource.initiatorType
    if (type in counts) {
      counts[type as keyof typeof counts]++
      sizes[type as keyof typeof sizes] += resource.transferSize || 0
    }
  })
  
  resourceCounts.value = counts
  resourceSizes.value = sizes
}

// 操作方法
const clearCache = () => {
  if ('caches' in window) {
    caches.keys().then(names => {
      names.forEach(name => {
        caches.delete(name)
      })
    })
  }
  console.log('缓存已清理')
}

const optimizePerformance = () => {
  // 清理未使用的事件监听器
  // 优化图片加载
  // 压缩DOM
  console.log('性能优化完成')
}

const exportReport = () => {
  const report = {
    timestamp: new Date().toISOString(),
    fps: currentFPS.value,
    memory: memoryUsage.value,
    domNodes: domNodeCount.value,
    network: networkStatus.value,
    metrics: performanceMetrics.value,
    resources: {
      counts: resourceCounts.value,
      sizes: resourceSizes.value
    }
  }
  
  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `performance-report-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
}

onMounted(() => {
  if (!props.enabled) return
  
  // 启动FPS监控
  measureFPS()
  
  // 启动内存监控
  memoryTimer = setInterval(() => {
    measureMemory()
    countDOMNodes()
    monitorNetwork()
  }, 1000)
  
  // 收集性能指标
  collectPerformanceMetrics()
  
  // 分析资源
  setTimeout(analyzeResources, 2000)
  
  // 自动隐藏
  if (props.autoHide) {
    setTimeout(() => {
      showMonitor.value = false
    }, 10000)
  }
})

onUnmounted(() => {
  if (memoryTimer) {
    clearInterval(memoryTimer)
  }
})
</script>

<style scoped>
.performance-monitor {
  position: fixed;
  z-index: 9999;
  background: var(--quantum-bg-surface);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  box-shadow: var(--quantum-shadow-lg);
  backdrop-filter: blur(10px);
  font-size: 0.75rem;
  min-width: 200px;
  max-width: 300px;
}

/* 位置样式 */
.performance-monitor {
  bottom: 1rem;
  right: 1rem;
}

.monitor-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  cursor: pointer;
  border-bottom: 1px solid var(--quantum-border-color);
  transition: background-color var(--quantum-transition-fast);
}

.monitor-toggle:hover {
  background: var(--quantum-bg-elevated);
}

.toggle-icon {
  transition: transform var(--quantum-transition-fast);
}

.monitor-title {
  flex: 1;
  font-weight: 600;
  color: var(--quantum-fg-primary);
}

.performance-indicator {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.indicator-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.performance-good .indicator-dot {
  background: var(--quantum-success);
}

.performance-warning .indicator-dot {
  background: var(--quantum-warning);
}

.performance-poor .indicator-dot {
  background: var(--quantum-error);
}

.monitor-content {
  padding: 0.75rem;
  max-height: 400px;
  overflow-y: auto;
}

.section-title {
  font-weight: 600;
  color: var(--quantum-fg-secondary);
  margin-bottom: 0.5rem;
  font-size: 0.7rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0.5rem;
  background: var(--quantum-bg-elevated);
  border-radius: 0.25rem;
}

.metric-label {
  color: var(--quantum-fg-muted);
  font-size: 0.65rem;
}

.metric-value {
  font-weight: 600;
  font-size: 0.7rem;
}

.fps-good { color: var(--quantum-success); }
.fps-warning { color: var(--quantum-warning); }
.fps-poor { color: var(--quantum-error); }

.network-good { color: var(--quantum-success); }
.network-warning { color: var(--quantum-warning); }
.network-poor { color: var(--quantum-error); }

.loading-metrics,
.resources-list {
  space-y: 0.25rem;
  margin-bottom: 1rem;
}

.loading-item,
.resource-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0;
  border-bottom: 1px solid var(--quantum-border-subtle);
}

.loading-label,
.resource-type {
  color: var(--quantum-fg-muted);
  font-size: 0.65rem;
  flex: 1;
}

.loading-value,
.resource-count,
.resource-size {
  font-weight: 500;
  font-size: 0.65rem;
  color: var(--quantum-fg-primary);
}

.resource-item {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: 0.5rem;
}

.actions-section {
  display: flex;
  gap: 0.25rem;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.25rem;
  color: var(--quantum-fg-secondary);
  font-size: 0.65rem;
  cursor: pointer;
  transition: all var(--quantum-transition-fast);
}

.action-btn:hover {
  background: var(--quantum-primary);
  color: white;
  border-color: var(--quantum-primary);
}

/* 滚动条 */
.monitor-content::-webkit-scrollbar {
  width: 4px;
}

.monitor-content::-webkit-scrollbar-track {
  background: var(--quantum-bg-elevated);
}

.monitor-content::-webkit-scrollbar-thumb {
  background: var(--quantum-border-color);
  border-radius: 2px;
}

/* 动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 响应式 */
@media (max-width: 768px) {
  .performance-monitor {
    bottom: 0.5rem;
    right: 0.5rem;
    left: 0.5rem;
    max-width: none;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
}
</style>
