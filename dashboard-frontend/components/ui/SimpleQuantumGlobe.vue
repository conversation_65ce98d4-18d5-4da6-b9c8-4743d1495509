<template>
  <div class="simple-quantum-globe" ref="container">
    <div ref="threeContainer" class="three-container"></div>
    
    <!-- 控制面板 -->
    <div class="globe-controls">
      <button @click="toggleView" class="control-btn" :class="{ active: is3D }">
        <i :class="is3D ? 'i-carbon-earth-filled' : 'i-carbon-map'"></i>
        {{ is3D ? '3D' : '2D' }}
      </button>
    </div>
    
    <!-- 访客信息 -->
    <div v-if="hoveredCountry" class="country-tooltip" :style="tooltipStyle">
      <div class="tooltip-header">
        <span class="flag">{{ hoveredCountry.flag }}</span>
        <div class="country-info">
          <h4 class="country-name">{{ hoveredCountry.country }}</h4>
          <p class="country-id">ID: {{ hoveredCountry.id }}</p>
        </div>
      </div>

      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-icon">👥</div>
          <div class="stat-content">
            <span class="stat-label">Visitors</span>
            <span class="stat-value">{{ formatNumber(hoveredCountry.visitors) }}</span>
          </div>
        </div>

        <div class="stat-item">
          <div class="stat-icon">📊</div>
          <div class="stat-content">
            <span class="stat-label">Sessions</span>
            <span class="stat-value">{{ formatNumber(hoveredCountry.sessions) }}</span>
          </div>
        </div>

        <div class="stat-item">
          <div class="stat-icon">📈</div>
          <div class="stat-content">
            <span class="stat-label">Growth</span>
            <span class="stat-value" :class="getGrowthClass(hoveredCountry.growth)">
              {{ hoveredCountry.growth > 0 ? '+' : '' }}{{ hoveredCountry.growth }}%
            </span>
          </div>
        </div>

        <div class="stat-item">
          <div class="stat-icon">⚡</div>
          <div class="stat-content">
            <span class="stat-label">Conversion</span>
            <span class="stat-value">{{ ((hoveredCountry.sessions / hoveredCountry.visitors) * 100).toFixed(1) }}%</span>
          </div>
        </div>
      </div>

      <div class="tooltip-footer">
        <div class="activity-indicator">
          <div class="pulse-dot"></div>
          <span>Live Data</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, computed, watch } from 'vue'
import * as THREE from 'three'

// Props
interface VisitorData {
  id: string
  country: string
  flag: string
  lat: number
  lng: number
  visitors: number
  sessions: number
  growth: number
}

interface Props {
  visitorData?: VisitorData[]
}

const props = withDefaults(defineProps<Props>(), {
  visitorData: () => [
    { id: '1', country: 'United States', flag: '🇺🇸', lat: 39.8283, lng: -98.5795, visitors: 456000, sessions: 1200000, growth: 12.5 },
    { id: '2', country: 'China', flag: '🇨🇳', lat: 35.8617, lng: 104.1954, visitors: 389000, sessions: 980000, growth: 18.2 },
    { id: '3', country: 'Japan', flag: '🇯🇵', lat: 36.2048, lng: 138.2529, visitors: 234000, sessions: 650000, growth: 8.7 },
    { id: '4', country: 'Germany', flag: '🇩🇪', lat: 51.1657, lng: 10.4515, visitors: 189000, sessions: 520000, growth: 15.3 },
    { id: '5', country: 'United Kingdom', flag: '🇬🇧', lat: 55.3781, lng: -3.4360, visitors: 167000, sessions: 445000, growth: 9.8 }
  ]
})

// Refs
const container = ref<HTMLDivElement>()
const threeContainer = ref<HTMLDivElement>()

// Three.js objects
let scene: THREE.Scene
let camera: THREE.PerspectiveCamera
let renderer: THREE.WebGLRenderer
let earth: THREE.Mesh
let earthGroup: THREE.Group
let visitorPoints: THREE.Group

// State
const is3D = ref(true)
const hoveredCountry = ref<VisitorData | null>(null)
const tooltipStyle = ref({})

// Theme
const isDark = ref(false)

// 检测主题
const updateTheme = () => {
  if (typeof window !== 'undefined') {
    isDark.value = document.documentElement.getAttribute('data-theme') === 'dark'
    // 只在场景存在时更新颜色
    if (scene && earth) {
      updateSceneColors()
    }
  }
}

// 监听主题变化
watch(isDark, () => {
  // 只在场景存在时更新颜色
  if (scene && earth) {
    updateSceneColors()
  }
})

// 主题颜色
const themeColors = computed(() => {
  if (isDark.value) {
    return {
      primary: new THREE.Color(0x00d4ff),    // 量子蓝
      secondary: new THREE.Color(0x0066cc),  // 深蓝
      accent: new THREE.Color(0x00ff88),     // 量子绿
      background: new THREE.Color(0x000011)  // 深空黑
    }
  } else {
    return {
      primary: new THREE.Color(0x2196f3),    // 明亮蓝色
      secondary: new THREE.Color(0x1976d2),  // 深蓝色
      accent: new THREE.Color(0x4caf50),     // 明亮绿色
      background: new THREE.Color(0xffffff)  // 纯白背景
    }
  }
})

// Animation
let animationId: number
let time = 0

// 绘制清晰可见的地球纹理
const drawEarthTexture = (ctx: CanvasRenderingContext2D, width: number, height: number, isDarkTheme: boolean) => {
  // 清除画布
  ctx.clearRect(0, 0, width, height)

  // 根据主题设置颜色
  const oceanColor = isDarkTheme ? '#001122' : '#4a90e2'
  const landColor = isDarkTheme ? '#2d4a22' : '#228B22'

  // 绘制海洋背景
  ctx.fillStyle = oceanColor
  ctx.fillRect(0, 0, width, height)

  // 绘制大陆 - 更大更明显
  ctx.fillStyle = landColor

  // 北美洲 - 更大
  ctx.fillRect(width * 0.08, height * 0.15, width * 0.25, height * 0.35)

  // 南美洲
  ctx.fillRect(width * 0.20, height * 0.45, width * 0.12, height * 0.4)

  // 欧洲
  ctx.fillRect(width * 0.46, height * 0.15, width * 0.12, height * 0.15)

  // 非洲
  ctx.fillRect(width * 0.48, height * 0.28, width * 0.14, height * 0.45)

  // 亚洲 - 更大
  ctx.fillRect(width * 0.58, height * 0.08, width * 0.35, height * 0.4)

  // 澳洲
  ctx.fillRect(width * 0.76, height * 0.65, width * 0.16, height * 0.12)

  // 添加一些岛屿让纹理更明显
  ctx.fillStyle = landColor
  ctx.fillRect(width * 0.35, height * 0.08, width * 0.04, height * 0.1) // 格陵兰
  ctx.fillRect(width * 0.78, height * 0.35, width * 0.02, height * 0.08) // 日本
  ctx.fillRect(width * 0.47, height * 0.22, width * 0.015, height * 0.04) // 英国

  console.log('地球纹理已绘制', { width, height, isDarkTheme, oceanColor, landColor })
}



// Initialize Three.js
const initThree = () => {
  if (!threeContainer.value) return

  // Scene
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0x000011)

  // Camera
  const width = threeContainer.value.clientWidth
  const height = threeContainer.value.clientHeight
  camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000)
  camera.position.z = 3

  // Renderer
  renderer = new THREE.WebGLRenderer({ antialias: true })
  renderer.setSize(width, height)
  renderer.setPixelRatio(window.devicePixelRatio)
  threeContainer.value.appendChild(renderer.domElement)

  // Earth group
  earthGroup = new THREE.Group()
  scene.add(earthGroup)

  // Visitor points group
  visitorPoints = new THREE.Group()
  earthGroup.add(visitorPoints)

  // Create earth
  createEarth()
  
  // Create visitor points
  createVisitorPoints()
  
  // Add lights
  addLights()
  
  // Add event listeners
  addEventListeners()
}

// Create earth
const createEarth = () => {
  // 高质量球体几何 - 更多细分
  const geometry = new THREE.SphereGeometry(1, 128, 64)

  // 创建高分辨率地球纹理
  const canvas = document.createElement('canvas')
  canvas.width = 2048  // 提高分辨率
  canvas.height = 1024
  const ctx = canvas.getContext('2d')!

  // 绘制地球纹理
  drawEarthTexture(ctx, canvas.width, canvas.height, isDark.value)

  const texture = new THREE.CanvasTexture(canvas)
  texture.wrapS = THREE.RepeatWrapping
  texture.wrapT = THREE.ClampToEdgeWrapping
  texture.needsUpdate = true

  // 获取当前主题颜色
  const colors = themeColors.value

  // 使用标准材质而不是着色器材质来测试纹理
  const material = new THREE.MeshBasicMaterial({
    map: texture,
    transparent: true,
    opacity: 0.9
  })

  earth = new THREE.Mesh(geometry, material)
  earthGroup.add(earth)

  // 创建更精细的大气层
  const atmosphereGeometry = new THREE.SphereGeometry(1.02, 64, 32)
  const atmosphereMaterial = new THREE.ShaderMaterial({
    uniforms: {
      time: { value: 0 },
      primaryColor: { value: colors.primary }
    },
    vertexShader: `
      varying vec3 vNormal;
      varying vec3 vPosition;
      void main() {
        vNormal = normalize(normalMatrix * normal);
        vPosition = position;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `,
    fragmentShader: `
      uniform float time;
      uniform vec3 primaryColor;
      varying vec3 vNormal;
      varying vec3 vPosition;

      void main() {
        float fresnel = pow(1.0 - dot(vNormal, vec3(0.0, 0.0, 1.0)), 3.0);
        float pulse = sin(time * 1.0) * 0.1 + 0.9;

        // 更柔和的大气层效果
        float intensity = fresnel * 0.15 * pulse;

        gl_FragColor = vec4(primaryColor, intensity);
      }
    `,
    transparent: true,
    side: THREE.BackSide,
    blending: THREE.AdditiveBlending
  })

  const atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial)
  earthGroup.add(atmosphere)
}

// Create visitor points
const createVisitorPoints = () => {
  props.visitorData.forEach(data => {
    // Convert lat/lng to 3D coordinates
    const phi = (90 - data.lat) * (Math.PI / 180)
    const theta = (data.lng + 180) * (Math.PI / 180)

    const x = -(1.02 * Math.sin(phi) * Math.cos(theta))
    const y = 1.02 * Math.cos(phi)
    const z = 1.02 * Math.sin(phi) * Math.sin(theta)

    // Create point with shader material - 更小更协调的大小
    const normalizedVisitors = Math.log(data.visitors + 1) / Math.log(500000 + 1) // 对数缩放
    const size = Math.max(0.008, Math.min(0.025, normalizedVisitors * 0.03)) // 大幅缩小
    const geometry = new THREE.SphereGeometry(size, 12, 8) // 减少几何复杂度

    // 颜色基于增长率
    const colors = themeColors.value
    const growthColor = data.growth > 0
      ? colors.accent // 使用主题的accent颜色表示增长
      : new THREE.Color(0xff4444) // 红色表示下降

    const material = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        intensity: { value: normalizedVisitors },
        growthRate: { value: data.growth / 100 },
        primaryColor: { value: growthColor },
        accentColor: { value: colors.primary }
      },
      vertexShader: `
        uniform float time;
        uniform float intensity;
        uniform float growthRate;
        varying vec3 vPosition;
        varying vec3 vNormal;

        void main() {
          vPosition = position;
          vNormal = normalize(normalMatrix * normal);

          vec3 pos = position;
          // 基于访客数量的脉冲效果
          float pulse = sin(time * (2.0 + intensity * 3.0)) * (0.05 + intensity * 0.1) + 1.0;
          pos *= pulse;

          gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform float intensity;
        uniform float growthRate;
        uniform vec3 primaryColor;
        uniform vec3 accentColor;
        varying vec3 vPosition;
        varying vec3 vNormal;

        void main() {
          // 基于增长率的颜色强度
          float colorIntensity = 0.5 + abs(growthRate) * 2.0;
          vec3 color = primaryColor * colorIntensity;

          // 边缘发光
          float fresnel = pow(1.0 - dot(vNormal, vec3(0.0, 0.0, 1.0)), 1.5);
          color += fresnel * accentColor * (0.3 + intensity * 0.4);

          // 基于访客数量的脉冲
          float pulse = sin(time * 2.0 + intensity * 8.0) * (0.2 + intensity * 0.3) + 0.8;

          gl_FragColor = vec4(color, 0.7 + pulse * 0.3);
        }
      `,
      transparent: true
    })

    const point = new THREE.Mesh(geometry, material)
    point.position.set(x, y, z)
    point.userData = data
    visitorPoints.add(point)

    // 创建更小的发光环
    const ringGeometry = new THREE.RingGeometry(size * 2, size * 3, 8)
    const ringMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        primaryColor: { value: colors.primary }
      },
      vertexShader: `
        uniform float time;
        void main() {
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform vec3 primaryColor;

        void main() {
          float pulse = sin(time * 4.0) * 0.4 + 0.6;
          gl_FragColor = vec4(primaryColor, 0.2 * pulse);
        }
      `,
      transparent: true,
      side: THREE.DoubleSide
    })

    const ring = new THREE.Mesh(ringGeometry, ringMaterial)
    ring.position.set(x, y, z)
    ring.lookAt(0, 0, 0)
    visitorPoints.add(ring)
  })
}

// Add lights
const addLights = () => {
  const ambientLight = new THREE.AmbientLight(0x404040, 0.4)
  scene.add(ambientLight)
  
  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
  directionalLight.position.set(1, 1, 1)
  scene.add(directionalLight)
}

// Mouse wheel handler
const onMouseWheel = (event: WheelEvent) => {
  event.preventDefault()
  const delta = event.deltaY > 0 ? 1.1 : 0.9

  if (is3D.value) {
    // 3D模式：调整Z轴距离
    camera.position.z = Math.max(1.5, Math.min(8, camera.position.z * delta))
  } else {
    // 2D模式：调整Y轴距离
    camera.position.y = Math.max(1.5, Math.min(8, camera.position.y * delta))
  }
}

// Touch handlers for mobile
const onTouchStart = (event: TouchEvent) => {
  if (event.touches.length === 2) {
    // 双指缩放开始
    const touch1 = event.touches[0]
    const touch2 = event.touches[1]
    const distance = Math.sqrt(
      Math.pow(touch2.clientX - touch1.clientX, 2) +
      Math.pow(touch2.clientY - touch1.clientY, 2)
    )
    renderer.domElement.dataset.lastTouchDistance = distance.toString()
  }
}

const onTouchMove = (event: TouchEvent) => {
  event.preventDefault()

  if (event.touches.length === 1) {
    // 单指移动 - 检测悬停
    const touch = event.touches[0]
    const rect = renderer.domElement.getBoundingClientRect()
    const mouse = new THREE.Vector2()
    mouse.x = ((touch.clientX - rect.left) / rect.width) * 2 - 1
    mouse.y = -((touch.clientY - rect.top) / rect.height) * 2 + 1

    const raycaster = new THREE.Raycaster()
    raycaster.setFromCamera(mouse, camera)

    const intersects = raycaster.intersectObjects(visitorPoints.children)
    if (intersects.length > 0) {
      hoveredCountry.value = intersects[0].object.userData as VisitorData
      tooltipStyle.value = {
        left: `${touch.clientX + 10}px`,
        top: `${touch.clientY - 10}px`
      }
    } else {
      hoveredCountry.value = null
    }
  } else if (event.touches.length === 2) {
    // 双指缩放
    const touch1 = event.touches[0]
    const touch2 = event.touches[1]
    const distance = Math.sqrt(
      Math.pow(touch2.clientX - touch1.clientX, 2) +
      Math.pow(touch2.clientY - touch1.clientY, 2)
    )

    const lastDistance = parseFloat(renderer.domElement.dataset.lastTouchDistance || '0')
    if (lastDistance > 0) {
      const delta = distance > lastDistance ? 0.95 : 1.05

      if (is3D.value) {
        camera.position.z = Math.max(1.5, Math.min(8, camera.position.z * delta))
      } else {
        camera.position.y = Math.max(1.5, Math.min(8, camera.position.y * delta))
      }
    }

    renderer.domElement.dataset.lastTouchDistance = distance.toString()
  }
}

const onTouchEnd = () => {
  hoveredCountry.value = null
  delete renderer.domElement.dataset.lastTouchDistance
}

// Add event listeners
const addEventListeners = () => {
  renderer.domElement.addEventListener('mousemove', onMouseMove)
  renderer.domElement.addEventListener('wheel', onMouseWheel)
  renderer.domElement.addEventListener('touchstart', onTouchStart, { passive: false })
  renderer.domElement.addEventListener('touchmove', onTouchMove, { passive: false })
  renderer.domElement.addEventListener('touchend', onTouchEnd)
  window.addEventListener('resize', onWindowResize)
}

// Mouse move handler
const onMouseMove = (event: MouseEvent) => {
  const rect = renderer.domElement.getBoundingClientRect()
  const mouse = new THREE.Vector2()
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

  const raycaster = new THREE.Raycaster()
  raycaster.setFromCamera(mouse, camera)

  const intersects = raycaster.intersectObjects(visitorPoints.children)
  if (intersects.length > 0) {
    hoveredCountry.value = intersects[0].object.userData as VisitorData
    tooltipStyle.value = {
      left: `${event.clientX + 10}px`,
      top: `${event.clientY - 10}px`
    }
  } else {
    hoveredCountry.value = null
  }
}

// Window resize handler
const onWindowResize = () => {
  if (!threeContainer.value) return
  
  const width = threeContainer.value.clientWidth
  const height = threeContainer.value.clientHeight
  
  camera.aspect = width / height
  camera.updateProjectionMatrix()
  renderer.setSize(width, height)
}

// Animation loop
const animate = () => {
  time += 0.01

  // 恒定旋转速度
  if (earthGroup) {
    earthGroup.rotation.y += 0.003 // 固定的慢速旋转
  }

  // 更新着色器时间
  if (earth && earth.material && (earth.material as any).uniforms) {
    (earth.material as any).uniforms.time.value = time
  }

  // 更新大气层
  earthGroup.children.forEach(child => {
    const mesh = child as THREE.Mesh
    if (mesh.material && (mesh.material as any).uniforms && (mesh.material as any).uniforms.time) {
      (mesh.material as any).uniforms.time.value = time
    }
  })

  // 更新访客点
  visitorPoints.children.forEach(child => {
    const mesh = child as THREE.Mesh
    if (mesh.material && (mesh.material as any).uniforms && (mesh.material as any).uniforms.time) {
      (mesh.material as any).uniforms.time.value = time
    }
  })

  renderer.render(scene, camera)
  animationId = requestAnimationFrame(animate)
}

// 更新场景颜色
const updateSceneColors = () => {
  if (!scene) return

  const colors = themeColors.value

  // 只更新背景颜色，避免复杂的材质更新
  scene.background = colors.background
}

// Control methods
const toggleView = () => {
  is3D.value = !is3D.value
  if (camera) {
    if (is3D.value) {
      // 3D视图
      camera.position.set(0, 0, 3)
      if (earthGroup) {
        earthGroup.rotation.x = 0
      }
    } else {
      // 2D视图（俯视）
      camera.position.set(0, 3, 0)
      camera.lookAt(0, 0, 0)
      if (earthGroup) {
        earthGroup.rotation.x = -Math.PI / 2
      }
    }
  }
}

// Utility methods
const formatNumber = (num: number) => {
  if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M'
  if (num >= 1000) return (num / 1000).toFixed(0) + 'K'
  return num.toString()
}

const getGrowthClass = (growth: number) => {
  return growth > 0 ? 'positive' : 'negative'
}

// Lifecycle
onMounted(async () => {
  await nextTick()
  updateTheme()
  initThree()
  animate()

  // 监听主题变化
  if (typeof window !== 'undefined') {
    const observer = new MutationObserver(updateTheme)
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-theme']
    })
  }
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
  
  if (renderer) {
    renderer.domElement.removeEventListener('mousemove', onMouseMove)
    renderer.domElement.removeEventListener('wheel', onMouseWheel)
    renderer.domElement.removeEventListener('touchstart', onTouchStart)
    renderer.domElement.removeEventListener('touchmove', onTouchMove)
    renderer.domElement.removeEventListener('touchend', onTouchEnd)
    window.removeEventListener('resize', onWindowResize)
    
    if (threeContainer.value && threeContainer.value.contains(renderer.domElement)) {
      threeContainer.value.removeChild(renderer.domElement)
    }
    
    renderer.dispose()
  }
})
</script>

<style scoped>
.simple-quantum-globe {
  position: relative;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(0, 17, 34, 0.9) 0%, rgba(0, 0, 0, 1) 100%);
  border-radius: 1rem;
  overflow: hidden;
  border: 1px solid var(--quantum-border-color);
}

.three-container {
  width: 100%;
  height: 100%;
}

.globe-controls {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 10;
}

.control-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  color: var(--quantum-fg-secondary);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(20px);
}

.control-btn:hover,
.control-btn.active {
  background: var(--quantum-primary);
  color: white;
  border-color: var(--quantum-primary);
  box-shadow: 0 0 15px var(--quantum-primary);
  transform: translateY(-1px);
}

.country-tooltip {
  position: fixed;
  background: var(--quantum-bg-surface);
  border: 1px solid var(--quantum-border-color);
  border-radius: 1rem;
  padding: 1rem;
  z-index: 20;
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  min-width: 280px;
  max-width: 320px;
  animation: tooltipSlideIn 0.3s ease-out;
}

.tooltip-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--quantum-border-color);
}

.flag {
  font-size: 2rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.country-info {
  flex: 1;
}

.country-name {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--quantum-fg-primary);
  margin: 0 0 0.25rem 0;
}

.country-id {
  font-size: 0.75rem;
  color: var(--quantum-fg-muted);
  margin: 0;
  font-family: monospace;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: var(--quantum-bg-elevated);
  border-radius: 0.5rem;
  border: 1px solid var(--quantum-border-color);
}

.stat-icon {
  font-size: 1.25rem;
  opacity: 0.8;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--quantum-fg-secondary);
  font-weight: 500;
}

.stat-value {
  font-size: 0.875rem;
  font-weight: 700;
  color: var(--quantum-fg-primary);
}

.tooltip-footer {
  display: flex;
  justify-content: center;
  padding-top: 0.75rem;
  border-top: 1px solid var(--quantum-border-color);
}

.activity-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: var(--quantum-fg-muted);
}

.pulse-dot {
  width: 8px;
  height: 8px;
  background: var(--quantum-success);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.positive {
  color: var(--quantum-success);
}

.negative {
  color: var(--quantum-error);
}

/* 动画 */
@keyframes tooltipSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .globe-controls {
    top: 0.5rem;
    right: 0.5rem;
  }

  .control-btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.625rem;
  }

  .country-tooltip {
    min-width: 240px;
    max-width: 280px;
    padding: 0.75rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .globe-controls {
    bottom: 0.5rem;
    top: auto;
    right: 50%;
    transform: translateX(50%);
  }

  .country-tooltip {
    min-width: 200px;
    max-width: 240px;
  }

  .flag {
    font-size: 1.5rem;
  }

  .country-name {
    font-size: 1rem;
  }
}
</style>
