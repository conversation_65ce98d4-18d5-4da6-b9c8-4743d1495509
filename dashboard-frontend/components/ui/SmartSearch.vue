<template>
  <div class="smart-search" :class="{ 'search-focused': isFocused, 'search-expanded': isExpanded }">
    <div class="search-container quantum-card-hologram">
      <!-- 搜索图标 -->
      <div class="search-icon">
        <i class="i-carbon-search text-[var(--quantum-fg-muted)]"></i>
      </div>
      
      <!-- 搜索输入框 -->
      <input
        ref="inputRef"
        v-model="searchQuery"
        type="text"
        class="search-input"
        :placeholder="placeholder"
        @focus="onFocus"
        @blur="onBlur"
        @keydown="onKeydown"
        @input="onInput"
      />
      
      <!-- 搜索类型选择器 -->
      <div v-if="showTypeSelector" class="search-type-selector">
        <select v-model="selectedType" class="type-select">
          <option value="">全部</option>
          <option v-for="type in searchTypes" :key="type.value" :value="type.value">
            {{ type.label }}
          </option>
        </select>
      </div>
      
      <!-- 清除按钮 -->
      <button
        v-if="searchQuery"
        @click="clearSearch"
        class="clear-btn"
      >
        <i class="i-carbon-close text-[var(--quantum-fg-muted)]"></i>
      </button>
      
      <!-- 快捷键提示 -->
      <div v-if="showShortcut && !isFocused" class="search-shortcut">
        <kbd class="shortcut-key">{{ shortcut }}</kbd>
      </div>
    </div>
    
    <!-- 搜索建议下拉框 -->
    <div
      v-if="showSuggestions && (suggestions.length > 0 || recentSearches.length > 0)"
      class="suggestions-dropdown quantum-card-hologram"
    >
      <!-- 搜索建议 -->
      <div v-if="suggestions.length > 0" class="suggestions-section">
        <div class="section-header">
          <i class="i-carbon-search text-[var(--quantum-primary)]"></i>
          <span class="section-title">搜索建议</span>
        </div>
        <div class="suggestions-list">
          <div
            v-for="(suggestion, index) in suggestions"
            :key="suggestion.id || index"
            @click="selectSuggestion(suggestion)"
            @mouseenter="highlightIndex = index"
            class="suggestion-item"
            :class="{ 'suggestion-highlighted': highlightIndex === index }"
          >
            <div class="suggestion-icon">
              <i :class="suggestion.icon || 'i-carbon-document'" class="text-[var(--quantum-primary)]"></i>
            </div>
            <div class="suggestion-content">
              <div class="suggestion-title" v-html="highlightMatch(suggestion.title)"></div>
              <div v-if="suggestion.description" class="suggestion-description">
                {{ suggestion.description }}
              </div>
            </div>
            <div v-if="suggestion.type" class="suggestion-type">
              {{ suggestion.type }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 最近搜索 -->
      <div v-if="recentSearches.length > 0 && !searchQuery" class="recent-section">
        <div class="section-header">
          <i class="i-carbon-time text-[var(--quantum-accent)]"></i>
          <span class="section-title">最近搜索</span>
          <button @click="clearRecentSearches" class="clear-recent-btn">
            <i class="i-carbon-trash-can text-[var(--quantum-fg-muted)]"></i>
          </button>
        </div>
        <div class="recent-list">
          <div
            v-for="(recent, index) in recentSearches"
            :key="index"
            @click="selectRecent(recent)"
            class="recent-item"
          >
            <div class="recent-icon">
              <i class="i-carbon-time text-[var(--quantum-fg-muted)]"></i>
            </div>
            <div class="recent-text">{{ recent }}</div>
            <button @click.stop="removeRecent(index)" class="remove-recent-btn">
              <i class="i-carbon-close text-[var(--quantum-fg-muted)]"></i>
            </button>
          </div>
        </div>
      </div>
      
      <!-- 快捷操作 -->
      <div v-if="quickActions.length > 0" class="quick-actions-section">
        <div class="section-header">
          <i class="i-carbon-flash text-[var(--quantum-warning)]"></i>
          <span class="section-title">快捷操作</span>
        </div>
        <div class="quick-actions-list">
          <button
            v-for="action in quickActions"
            :key="action.key"
            @click="executeAction(action)"
            class="quick-action-btn"
          >
            <i :class="action.icon" class="action-icon"></i>
            <span class="action-label">{{ action.label }}</span>
            <kbd v-if="action.shortcut" class="action-shortcut">{{ action.shortcut }}</kbd>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'

interface SearchSuggestion {
  id?: string
  title: string
  description?: string
  type?: string
  icon?: string
  data?: any
}

interface SearchType {
  value: string
  label: string
}

interface QuickAction {
  key: string
  label: string
  icon: string
  shortcut?: string
  handler: () => void
}

interface Props {
  placeholder?: string
  suggestions?: SearchSuggestion[]
  searchTypes?: SearchType[]
  quickActions?: QuickAction[]
  showTypeSelector?: boolean
  showShortcut?: boolean
  shortcut?: string
  debounceDelay?: number
  maxSuggestions?: number
  maxRecentSearches?: number
  autoFocus?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '搜索...',
  suggestions: () => [],
  searchTypes: () => [],
  quickActions: () => [],
  showTypeSelector: false,
  showShortcut: true,
  shortcut: 'Ctrl+K',
  debounceDelay: 300,
  maxSuggestions: 8,
  maxRecentSearches: 5,
  autoFocus: false
})

const emit = defineEmits<{
  search: [query: string, type?: string]
  select: [suggestion: SearchSuggestion]
  focus: []
  blur: []
}>()

// 引用
const inputRef = ref<HTMLInputElement>()

// 状态
const searchQuery = ref('')
const selectedType = ref('')
const isFocused = ref(false)
const isExpanded = ref(false)
const highlightIndex = ref(-1)
const recentSearches = ref<string[]>([])

// 计算属性
const showSuggestions = computed(() => {
  return isFocused.value && (searchQuery.value.length > 0 || recentSearches.value.length > 0)
})

const filteredSuggestions = computed(() => {
  if (!searchQuery.value) return []
  
  return props.suggestions
    .filter(suggestion => {
      const matchesQuery = suggestion.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
                          (suggestion.description && suggestion.description.toLowerCase().includes(searchQuery.value.toLowerCase()))
      const matchesType = !selectedType.value || suggestion.type === selectedType.value
      return matchesQuery && matchesType
    })
    .slice(0, props.maxSuggestions)
})

const suggestions = computed(() => filteredSuggestions.value)

// 防抖搜索
let searchTimer: NodeJS.Timeout | null = null

const debouncedSearch = () => {
  if (searchTimer) clearTimeout(searchTimer)
  searchTimer = setTimeout(() => {
    if (searchQuery.value.trim()) {
      emit('search', searchQuery.value.trim(), selectedType.value)
    }
  }, props.debounceDelay)
}

// 方法
const onFocus = () => {
  isFocused.value = true
  isExpanded.value = true
  emit('focus')
}

const onBlur = () => {
  // 延迟隐藏，允许点击建议项
  setTimeout(() => {
    isFocused.value = false
    isExpanded.value = false
    emit('blur')
  }, 200)
}

const onInput = () => {
  highlightIndex.value = -1
  debouncedSearch()
}

const onKeydown = (event: KeyboardEvent) => {
  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      if (highlightIndex.value < suggestions.value.length - 1) {
        highlightIndex.value++
      }
      break
    case 'ArrowUp':
      event.preventDefault()
      if (highlightIndex.value > 0) {
        highlightIndex.value--
      }
      break
    case 'Enter':
      event.preventDefault()
      if (highlightIndex.value >= 0 && suggestions.value[highlightIndex.value]) {
        selectSuggestion(suggestions.value[highlightIndex.value])
      } else if (searchQuery.value.trim()) {
        performSearch()
      }
      break
    case 'Escape':
      inputRef.value?.blur()
      break
  }
}

const selectSuggestion = (suggestion: SearchSuggestion) => {
  searchQuery.value = suggestion.title
  addToRecentSearches(suggestion.title)
  emit('select', suggestion)
  inputRef.value?.blur()
}

const selectRecent = (recent: string) => {
  searchQuery.value = recent
  performSearch()
}

const performSearch = () => {
  if (searchQuery.value.trim()) {
    addToRecentSearches(searchQuery.value.trim())
    emit('search', searchQuery.value.trim(), selectedType.value)
    inputRef.value?.blur()
  }
}

const clearSearch = () => {
  searchQuery.value = ''
  highlightIndex.value = -1
  inputRef.value?.focus()
}

const addToRecentSearches = (query: string) => {
  const trimmedQuery = query.trim()
  if (!trimmedQuery) return
  
  // 移除重复项
  const filtered = recentSearches.value.filter(item => item !== trimmedQuery)
  
  // 添加到开头
  recentSearches.value = [trimmedQuery, ...filtered].slice(0, props.maxRecentSearches)
  
  // 保存到localStorage
  localStorage.setItem('smart-search-recent', JSON.stringify(recentSearches.value))
}

const clearRecentSearches = () => {
  recentSearches.value = []
  localStorage.removeItem('smart-search-recent')
}

const removeRecent = (index: number) => {
  recentSearches.value.splice(index, 1)
  localStorage.setItem('smart-search-recent', JSON.stringify(recentSearches.value))
}

const executeAction = (action: QuickAction) => {
  action.handler()
  inputRef.value?.blur()
}

const highlightMatch = (text: string) => {
  if (!searchQuery.value) return text
  
  const regex = new RegExp(`(${searchQuery.value})`, 'gi')
  return text.replace(regex, '<mark class="search-highlight">$1</mark>')
}

const focus = () => {
  inputRef.value?.focus()
}

// 全局快捷键
const onGlobalKeydown = (event: KeyboardEvent) => {
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault()
    focus()
  }
}

// 加载最近搜索
const loadRecentSearches = () => {
  try {
    const saved = localStorage.getItem('smart-search-recent')
    if (saved) {
      recentSearches.value = JSON.parse(saved)
    }
  } catch (error) {
    console.warn('Failed to load recent searches:', error)
  }
}

// 监听搜索类型变化
watch(selectedType, () => {
  if (searchQuery.value) {
    debouncedSearch()
  }
})

// 暴露方法
defineExpose({
  focus,
  clear: clearSearch,
  getValue: () => searchQuery.value
})

onMounted(() => {
  loadRecentSearches()
  document.addEventListener('keydown', onGlobalKeydown)
  
  if (props.autoFocus) {
    nextTick(() => {
      focus()
    })
  }
})

onUnmounted(() => {
  if (searchTimer) clearTimeout(searchTimer)
  document.removeEventListener('keydown', onGlobalKeydown)
})
</script>

<style scoped>
.smart-search {
  position: relative;
  width: 100%;
  max-width: 600px;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 0.75rem;
  transition: all var(--quantum-transition-fast);
  border: 1px solid var(--quantum-border-color);
}

.search-focused .search-container {
  border-color: var(--quantum-primary);
  box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.1);
}

.search-icon {
  flex-shrink: 0;
  transition: color var(--quantum-transition-fast);
}

.search-focused .search-icon {
  color: var(--quantum-primary) !important;
}

.search-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: var(--quantum-fg-primary);
  font-size: 1rem;
  placeholder-color: var(--quantum-fg-muted);
}

.search-input::placeholder {
  color: var(--quantum-fg-muted);
}

.search-type-selector {
  flex-shrink: 0;
}

.type-select {
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.375rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  color: var(--quantum-fg-primary);
}

.clear-btn {
  flex-shrink: 0;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all var(--quantum-transition-fast);
  cursor: pointer;
}

.clear-btn:hover {
  background: var(--quantum-bg-elevated);
}

.search-shortcut {
  flex-shrink: 0;
}

.shortcut-key {
  display: inline-block;
  padding: 0.125rem 0.375rem;
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-family: monospace;
  color: var(--quantum-fg-muted);
}

/* 建议下拉框 */
.suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 0.5rem;
  border-radius: 0.75rem;
  border: 1px solid var(--quantum-border-color);
  max-height: 400px;
  overflow-y: auto;
  z-index: 1000;
  animation: dropdownFadeIn 0.2s ease-out;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem 0.5rem;
  border-bottom: 1px solid var(--quantum-border-subtle);
}

.section-title {
  flex: 1;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--quantum-fg-secondary);
}

.clear-recent-btn {
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all var(--quantum-transition-fast);
  cursor: pointer;
}

.clear-recent-btn:hover {
  background: var(--quantum-bg-elevated);
}

/* 建议项 */
.suggestions-list,
.recent-list {
  padding: 0.5rem;
}

.suggestion-item,
.recent-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all var(--quantum-transition-fast);
}

.suggestion-item:hover,
.recent-item:hover,
.suggestion-highlighted {
  background: var(--quantum-bg-elevated);
  transform: translateX(2px);
}

.suggestion-icon,
.recent-icon {
  flex-shrink: 0;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.suggestion-content {
  flex: 1;
  min-width: 0;
}

.suggestion-title {
  font-weight: 500;
  color: var(--quantum-fg-primary);
  margin-bottom: 0.125rem;
}

.suggestion-description {
  font-size: 0.875rem;
  color: var(--quantum-fg-secondary);
  line-height: 1.3;
}

.suggestion-type {
  flex-shrink: 0;
  font-size: 0.75rem;
  color: var(--quantum-fg-muted);
  background: var(--quantum-bg-elevated);
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
}

.recent-text {
  flex: 1;
  color: var(--quantum-fg-primary);
}

.remove-recent-btn {
  flex-shrink: 0;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all var(--quantum-transition-fast);
  cursor: pointer;
  opacity: 0;
}

.recent-item:hover .remove-recent-btn {
  opacity: 1;
}

.remove-recent-btn:hover {
  background: var(--quantum-bg-hover);
}

/* 快捷操作 */
.quick-actions-list {
  padding: 0.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all var(--quantum-transition-fast);
  text-align: left;
}

.quick-action-btn:hover {
  background: var(--quantum-bg-hover);
  border-color: var(--quantum-primary);
  transform: translateY(-1px);
}

.action-icon {
  flex-shrink: 0;
  color: var(--quantum-primary);
}

.action-label {
  flex: 1;
  font-weight: 500;
  color: var(--quantum-fg-primary);
}

.action-shortcut {
  flex-shrink: 0;
  font-size: 0.75rem;
  font-family: monospace;
  color: var(--quantum-fg-muted);
  background: var(--quantum-bg-surface);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

/* 搜索高亮 */
:deep(.search-highlight) {
  background: var(--quantum-primary);
  color: white;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-weight: 600;
}

/* 滚动条 */
.suggestions-dropdown::-webkit-scrollbar {
  width: 6px;
}

.suggestions-dropdown::-webkit-scrollbar-track {
  background: var(--quantum-bg-elevated);
  border-radius: 3px;
}

.suggestions-dropdown::-webkit-scrollbar-thumb {
  background: var(--quantum-border-color);
  border-radius: 3px;
}

/* 动画 */
@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式 */
@media (max-width: 768px) {
  .search-container {
    padding: 0.5rem 0.75rem;
  }
  
  .search-input {
    font-size: 0.875rem;
  }
  
  .quick-actions-list {
    grid-template-columns: 1fr;
  }
  
  .suggestions-dropdown {
    max-height: 300px;
  }
}
</style>
