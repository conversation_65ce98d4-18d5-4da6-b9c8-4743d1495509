<template>
  <div 
    ref="containerRef"
    class="virtual-list-container"
    :style="{ height: containerHeight }"
    @scroll="onScroll"
  >
    <!-- 虚拟滚动区域 -->
    <div 
      class="virtual-scroll-area"
      :style="{ height: `${totalHeight}px` }"
    >
      <!-- 可见项目容器 -->
      <div 
        class="visible-items"
        :style="{ transform: `translateY(${offsetY}px)` }"
      >
        <div
          v-for="(item, index) in visibleItems"
          :key="getItemKey(item, startIndex + index)"
          class="virtual-item"
          :style="{ height: `${itemHeight}px` }"
        >
          <slot 
            :item="item" 
            :index="startIndex + index"
            :isVisible="true"
          >
            <div class="default-item">
              {{ item }}
            </div>
          </slot>
        </div>
      </div>
    </div>

    <!-- 加载更多指示器 -->
    <div v-if="loading" class="loading-indicator">
      <div class="loading-content quantum-card-hologram p-4 rounded-lg">
        <div class="loading-spinner">
          <i class="i-carbon-circle-dash animate-spin text-[var(--quantum-primary)] text-2xl"></i>
        </div>
        <p class="loading-text text-sm text-[var(--quantum-fg-secondary)] mt-2">加载中...</p>
      </div>
    </div>

    <!-- 无更多数据指示器 -->
    <div v-if="!hasMore && items.length > 0" class="no-more-indicator">
      <div class="no-more-content text-center p-4">
        <i class="i-carbon-checkmark-filled text-[var(--quantum-success)] text-xl mb-2"></i>
        <p class="no-more-text text-sm text-[var(--quantum-fg-muted)]">已加载全部数据</p>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="items.length === 0 && !loading" class="empty-state">
      <div class="empty-content quantum-card-hologram p-8 rounded-xl text-center">
        <div class="empty-icon quantum-energy-ring w-16 h-16 mx-auto mb-4 flex items-center justify-center">
          <i class="i-carbon-no-image text-3xl text-[var(--quantum-fg-muted)]"></i>
        </div>
        <h3 class="empty-title text-lg font-semibold quantum-text-matrix mb-2">暂无数据</h3>
        <p class="empty-description text-sm text-[var(--quantum-fg-secondary)]">
          {{ emptyText || '当前列表为空，请稍后再试' }}
        </p>
        <slot name="empty-action"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'

interface Props {
  items: any[]
  itemHeight: number
  containerHeight?: string
  overscan?: number
  loading?: boolean
  hasMore?: boolean
  emptyText?: string
  keyField?: string
  loadMoreThreshold?: number
}

const props = withDefaults(defineProps<Props>(), {
  containerHeight: '400px',
  overscan: 5,
  loading: false,
  hasMore: true,
  emptyText: '',
  keyField: 'id',
  loadMoreThreshold: 100
})

const emit = defineEmits<{
  loadMore: []
  scroll: [{ scrollTop: number; scrollLeft: number }]
}>()

// 引用
const containerRef = ref<HTMLElement>()

// 状态
const scrollTop = ref(0)
const containerClientHeight = ref(0)

// 计算属性
const totalHeight = computed(() => props.items.length * props.itemHeight)

const visibleCount = computed(() => {
  if (containerClientHeight.value === 0) return 0
  return Math.ceil(containerClientHeight.value / props.itemHeight) + props.overscan * 2
})

const startIndex = computed(() => {
  const index = Math.floor(scrollTop.value / props.itemHeight) - props.overscan
  return Math.max(0, index)
})

const endIndex = computed(() => {
  const index = startIndex.value + visibleCount.value
  return Math.min(props.items.length, index)
})

const visibleItems = computed(() => {
  return props.items.slice(startIndex.value, endIndex.value)
})

const offsetY = computed(() => {
  return startIndex.value * props.itemHeight
})

// 方法
const getItemKey = (item: any, index: number) => {
  if (props.keyField && item[props.keyField] !== undefined) {
    return item[props.keyField]
  }
  return index
}

const onScroll = (event: Event) => {
  const target = event.target as HTMLElement
  scrollTop.value = target.scrollTop
  
  emit('scroll', {
    scrollTop: target.scrollTop,
    scrollLeft: target.scrollLeft
  })

  // 检查是否需要加载更多
  if (props.hasMore && !props.loading) {
    const scrollBottom = target.scrollHeight - target.scrollTop - target.clientHeight
    if (scrollBottom <= props.loadMoreThreshold) {
      emit('loadMore')
    }
  }
}

const scrollTo = (index: number) => {
  if (!containerRef.value) return
  
  const targetScrollTop = index * props.itemHeight
  containerRef.value.scrollTop = targetScrollTop
}

const scrollToTop = () => {
  scrollTo(0)
}

const scrollToBottom = () => {
  if (!containerRef.value) return
  containerRef.value.scrollTop = totalHeight.value
}

// 更新容器高度
const updateContainerHeight = () => {
  if (containerRef.value) {
    containerClientHeight.value = containerRef.value.clientHeight
  }
}

// 防抖的滚动处理
let scrollTimer: NodeJS.Timeout | null = null
const debouncedScroll = (event: Event) => {
  if (scrollTimer) clearTimeout(scrollTimer)
  scrollTimer = setTimeout(() => {
    onScroll(event)
  }, 16) // 约60fps
}

// 监听器
const resizeObserver = ref<ResizeObserver>()

onMounted(() => {
  nextTick(() => {
    updateContainerHeight()
    
    // 设置ResizeObserver
    if (containerRef.value && typeof ResizeObserver !== 'undefined') {
      resizeObserver.value = new ResizeObserver(() => {
        updateContainerHeight()
      })
      resizeObserver.value.observe(containerRef.value)
    }
  })
})

onUnmounted(() => {
  if (scrollTimer) clearTimeout(scrollTimer)
  resizeObserver.value?.disconnect()
})

// 监听items变化，重置滚动位置
watch(() => props.items.length, (newLength, oldLength) => {
  if (newLength === 0 || (oldLength > 0 && newLength < oldLength)) {
    scrollToTop()
  }
})

// 暴露方法
defineExpose({
  scrollTo,
  scrollToTop,
  scrollToBottom,
  getScrollTop: () => scrollTop.value
})
</script>

<style scoped>
.virtual-list-container {
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  background: var(--quantum-bg-surface);
  border-radius: 0.75rem;
  border: 1px solid var(--quantum-border-color);
}

.virtual-scroll-area {
  position: relative;
  width: 100%;
}

.visible-items {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.virtual-item {
  position: relative;
  width: 100%;
  border-bottom: 1px solid var(--quantum-border-subtle);
}

.virtual-item:last-child {
  border-bottom: none;
}

.default-item {
  padding: 1rem;
  display: flex;
  align-items: center;
  background: var(--quantum-bg-surface);
  transition: background-color var(--quantum-transition-fast);
}

.default-item:hover {
  background: var(--quantum-bg-elevated);
}

/* 加载指示器 */
.loading-indicator {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1rem;
  background: var(--quantum-bg-surface);
  border-top: 1px solid var(--quantum-border-color);
  z-index: 10;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 4rem;
}

.loading-spinner {
  animation: pulse 2s ease-in-out infinite;
}

/* 无更多数据指示器 */
.no-more-indicator {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0.5rem 1rem;
  background: var(--quantum-bg-surface);
  border-top: 1px solid var(--quantum-border-color);
}

.no-more-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 空状态 */
.empty-state {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.empty-content {
  max-width: 24rem;
  width: 100%;
}

/* 滚动条样式 */
.virtual-list-container::-webkit-scrollbar {
  width: 6px;
}

.virtual-list-container::-webkit-scrollbar-track {
  background: var(--quantum-bg-elevated);
  border-radius: 3px;
}

.virtual-list-container::-webkit-scrollbar-thumb {
  background: var(--quantum-border-color);
  border-radius: 3px;
  transition: background-color var(--quantum-transition-fast);
}

.virtual-list-container::-webkit-scrollbar-thumb:hover {
  background: var(--quantum-primary);
}

/* 动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式优化 */
@media (max-width: 768px) {
  .virtual-list-container {
    border-radius: 0.5rem;
  }
  
  .loading-content {
    min-height: 3rem;
  }
  
  .empty-content {
    padding: 1.5rem;
  }
  
  .empty-icon {
    width: 3rem;
    height: 3rem;
  }
}

/* 性能优化 */
.virtual-item {
  contain: layout style paint;
  will-change: transform;
}

.visible-items {
  contain: layout style paint;
  will-change: transform;
}
</style>
