<template>
  <div class="quantum-earth-background">
    <!-- 🌍 地球轮廓 -->
    <div class="earth-sphere" :style="earthStyle">
      <!-- 大陆轮廓 -->
      <div class="continents">
        <div class="continent north-america"></div>
        <div class="continent south-america"></div>
        <div class="continent europe"></div>
        <div class="continent africa"></div>
        <div class="continent asia"></div>
        <div class="continent australia"></div>
      </div>
      
      <!-- 经纬线网格 -->
      <div class="grid-lines">
        <div v-for="i in 12" :key="`lat-${i}`" class="latitude-line" :style="getLatitudeStyle(i)"></div>
        <div v-for="i in 24" :key="`lng-${i}`" class="longitude-line" :style="getLongitudeStyle(i)"></div>
      </div>
      
      <!-- 量子能量场 -->
      <div class="quantum-field">
        <div v-for="i in 8" :key="`field-${i}`" class="energy-ring" :style="getEnergyRingStyle(i)"></div>
      </div>
    </div>
    
    <!-- 🌌 星空背景 -->
    <div class="space-background">
      <div v-for="i in 100" :key="`star-${i}`" class="star" :style="getStarStyle(i)"></div>
    </div>
    
    <!-- ⚡ 量子粒子 -->
    <div class="quantum-particles">
      <div v-for="i in 20" :key="`particle-${i}`" class="quantum-particle" :style="getParticleStyle(i)"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// Props
interface Props {
  rotationSpeed?: number
  showGrid?: boolean
  showParticles?: boolean
  theme?: 'light' | 'dark'
}

const props = withDefaults(defineProps<Props>(), {
  rotationSpeed: 1,
  showGrid: true,
  showParticles: true,
  theme: 'dark'
})

// 状态
const rotation = ref(0)

// 计算属性
const earthStyle = computed(() => ({
  transform: `rotateY(${rotation.value}deg)`,
  '--rotation-speed': `${props.rotationSpeed}s`
}))

// 方法
const getLatitudeStyle = (index: number) => {
  const angle = (index - 1) * 15 - 90 // -90 to 90 degrees
  return {
    transform: `rotateX(${angle}deg)`,
    opacity: Math.abs(angle) < 60 ? 0.6 : 0.3
  }
}

const getLongitudeStyle = (index: number) => {
  const angle = (index - 1) * 15 // 0 to 360 degrees
  return {
    transform: `rotateY(${angle}deg)`,
    opacity: 0.4
  }
}

const getEnergyRingStyle = (index: number) => {
  const scale = 1 + (index * 0.1)
  const delay = index * 0.5
  return {
    transform: `scale(${scale})`,
    animationDelay: `${delay}s`,
    opacity: 1 - (index * 0.1)
  }
}

const getStarStyle = (index: number) => {
  const x = Math.random() * 100
  const y = Math.random() * 100
  const size = Math.random() * 3 + 1
  const delay = Math.random() * 5
  
  return {
    left: `${x}%`,
    top: `${y}%`,
    width: `${size}px`,
    height: `${size}px`,
    animationDelay: `${delay}s`
  }
}

const getParticleStyle = (index: number) => {
  const x = Math.random() * 100
  const y = Math.random() * 100
  const size = Math.random() * 4 + 2
  const delay = Math.random() * 3
  const duration = Math.random() * 2 + 3
  
  return {
    left: `${x}%`,
    top: `${y}%`,
    width: `${size}px`,
    height: `${size}px`,
    animationDelay: `${delay}s`,
    animationDuration: `${duration}s`
  }
}

// 动画循环
let animationFrame: number

const animate = () => {
  rotation.value += props.rotationSpeed * 0.1
  if (rotation.value >= 360) {
    rotation.value = 0
  }
  animationFrame = requestAnimationFrame(animate)
}

// 生命周期
onMounted(() => {
  animate()
})

onUnmounted(() => {
  if (animationFrame) {
    cancelAnimationFrame(animationFrame)
  }
})
</script>

<style scoped>
.quantum-earth-background {
  position: absolute;
  inset: 0;
  overflow: hidden;
  perspective: 1000px;
}

.earth-sphere {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 300px;
  height: 300px;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, 
    rgba(0, 100, 200, 0.3) 0%,
    rgba(0, 50, 150, 0.5) 50%,
    rgba(0, 20, 80, 0.8) 100%
  );
  border: 2px solid rgba(0, 212, 255, 0.3);
  transform-style: preserve-3d;
  animation: quantumEarthRotate 60s linear infinite;
}

.continents {
  position: absolute;
  inset: 0;
  border-radius: 50%;
  overflow: hidden;
}

.continent {
  position: absolute;
  background: rgba(0, 255, 136, 0.2);
  border: 1px solid rgba(0, 255, 136, 0.4);
  border-radius: 50%;
}

.north-america {
  top: 20%;
  left: 15%;
  width: 25%;
  height: 30%;
  border-radius: 60% 40% 50% 70%;
}

.south-america {
  top: 45%;
  left: 25%;
  width: 15%;
  height: 35%;
  border-radius: 70% 30% 60% 40%;
}

.europe {
  top: 15%;
  left: 45%;
  width: 15%;
  height: 20%;
  border-radius: 50%;
}

.africa {
  top: 25%;
  left: 50%;
  width: 20%;
  height: 40%;
  border-radius: 60% 40% 50% 50%;
}

.asia {
  top: 10%;
  left: 60%;
  width: 35%;
  height: 45%;
  border-radius: 40% 60% 50% 50%;
}

.australia {
  top: 60%;
  left: 75%;
  width: 15%;
  height: 15%;
  border-radius: 50%;
}

.grid-lines {
  position: absolute;
  inset: 0;
  border-radius: 50%;
}

.latitude-line,
.longitude-line {
  position: absolute;
  border: 1px solid rgba(0, 212, 255, 0.2);
}

.latitude-line {
  width: 100%;
  height: 0;
  top: 50%;
  left: 0;
  border-radius: 50%;
  transform-origin: center;
}

.longitude-line {
  width: 0;
  height: 100%;
  top: 0;
  left: 50%;
  border-radius: 50%;
  transform-origin: center;
}

.quantum-field {
  position: absolute;
  inset: -20%;
  border-radius: 50%;
}

.energy-ring {
  position: absolute;
  inset: 0;
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 50%;
  animation: quantumEnergyPulse 4s ease-in-out infinite;
}

.space-background {
  position: absolute;
  inset: 0;
}

.star {
  position: absolute;
  background: white;
  border-radius: 50%;
  animation: quantumStarTwinkle 3s ease-in-out infinite;
}

.quantum-particles {
  position: absolute;
  inset: 0;
}

.quantum-particle {
  position: absolute;
  background: var(--quantum-primary);
  border-radius: 50%;
  box-shadow: 0 0 10px var(--quantum-primary);
  animation: quantumParticleFloat 5s ease-in-out infinite;
}

@keyframes quantumEarthRotate {
  from {
    transform: translate(-50%, -50%) rotateY(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotateY(360deg);
  }
}

@keyframes quantumEnergyPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

@keyframes quantumStarTwinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes quantumParticleFloat {
  0%, 100% {
    opacity: 0.5;
    transform: translateY(0px) scale(1);
  }
  33% {
    opacity: 1;
    transform: translateY(-20px) scale(1.1);
  }
  66% {
    opacity: 0.7;
    transform: translateY(10px) scale(0.9);
  }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .earth-sphere {
    width: 200px;
    height: 200px;
  }
}

@media (max-width: 480px) {
  .earth-sphere {
    width: 150px;
    height: 150px;
  }
}
</style>
