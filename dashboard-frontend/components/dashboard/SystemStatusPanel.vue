<template>
  <div class="system-status-panel cyber-card-hologram">
    <!-- 🎯 系统总览 -->
    <div class="status-header">
      <div class="header-left">
        <h3 class="status-title cyber-text-matrix">SYSTEM STATUS OVERVIEW</h3>
        <div class="system-uptime">
          <span class="cyber-hud-element">UPTIME: {{ systemUptime }}</span>
          <span class="cyber-hud-element ml-4">LOAD: {{ systemLoad }}%</span>
        </div>
      </div>
      <div class="header-right">
        <div class="overall-status" :class="overallStatus">
          <div class="status-indicator"></div>
          <span class="status-text">{{ overallStatus.toUpperCase() }}</span>
        </div>
      </div>
    </div>

    <!-- 📊 核心指标网格 -->
    <div class="core-metrics-grid">
      <div v-for="metric in coreMetrics" :key="metric.id" 
           class="metric-card" :class="metric.status">
        <div class="metric-header">
          <div class="metric-icon">
            <i :class="metric.icon" class="cyber-text-glow"></i>
          </div>
          <div class="metric-info">
            <div class="metric-name">{{ metric.name }}</div>
            <div class="metric-category">{{ metric.category }}</div>
          </div>
        </div>
        <div class="metric-value-section">
          <div class="metric-value cyber-text-glow">{{ metric.value }}</div>
          <div class="metric-unit">{{ metric.unit }}</div>
        </div>
        <div class="metric-chart">
          <div class="sparkline">
            <div v-for="(point, index) in metric.history" :key="index"
                 class="sparkline-point"
                 :style="{ height: `${point}%`, animationDelay: `${index * 0.05}s` }">
            </div>
          </div>
        </div>
        <div class="metric-trend" :class="metric.trend">
          <i :class="getTrendIcon(metric.trend)"></i>
          <span>{{ Math.abs(metric.change) }}%</span>
        </div>
      </div>
    </div>

    <!-- 🌐 服务状态 -->
    <div class="services-status">
      <h4 class="services-title cyber-text-matrix">CRITICAL SERVICES</h4>
      <div class="services-grid">
        <div v-for="service in criticalServices" :key="service.id"
             class="service-item" :class="service.status">
          <div class="service-info">
            <div class="service-name">{{ service.name }}</div>
            <div class="service-endpoint">{{ service.endpoint }}</div>
          </div>
          <div class="service-metrics">
            <div class="service-metric">
              <span class="metric-label">RESPONSE:</span>
              <span class="metric-value">{{ service.responseTime }}ms</span>
            </div>
            <div class="service-metric">
              <span class="metric-label">UPTIME:</span>
              <span class="metric-value">{{ service.uptime }}%</span>
            </div>
          </div>
          <div class="service-status-indicator">
            <div class="status-dot" :class="service.status"></div>
            <span class="status-label">{{ service.status.toUpperCase() }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- ⚠️ 系统警报 -->
    <div class="system-alerts" v-if="systemAlerts.length > 0">
      <h4 class="alerts-title cyber-text-matrix">SYSTEM ALERTS</h4>
      <div class="alerts-list">
        <div v-for="alert in systemAlerts" :key="alert.id"
             class="alert-item" :class="alert.severity">
          <div class="alert-icon">
            <i :class="getAlertIcon(alert.severity)"></i>
          </div>
          <div class="alert-content">
            <div class="alert-message">{{ alert.message }}</div>
            <div class="alert-details">
              <span class="alert-time">{{ alert.timestamp }}</span>
              <span class="alert-source">{{ alert.source }}</span>
            </div>
          </div>
          <div class="alert-actions">
            <button @click="acknowledgeAlert(alert.id)" class="alert-btn">
              <i class="i-carbon-checkmark"></i>
            </button>
            <button @click="dismissAlert(alert.id)" class="alert-btn">
              <i class="i-carbon-close"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 📈 快速统计 -->
    <div class="quick-stats">
      <div class="stats-row">
        <div v-for="stat in quickStats" :key="stat.label" class="stat-item">
          <div class="stat-value cyber-text-glow">{{ stat.value }}</div>
          <div class="stat-label">{{ stat.label }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 系统运行时间
const systemUptime = ref('99d 12h 34m')
const systemLoad = ref(67)

// 核心指标
const coreMetrics = ref([
  {
    id: 1,
    name: 'CPU Usage',
    category: 'COMPUTE',
    value: 67,
    unit: '%',
    status: 'normal',
    trend: 'up',
    change: 5.2,
    icon: 'i-carbon-chip',
    history: [45, 52, 48, 61, 67, 59, 73, 68, 65, 67]
  },
  {
    id: 2,
    name: 'Memory',
    category: 'STORAGE',
    value: 8.2,
    unit: 'GB',
    status: 'warning',
    trend: 'up',
    change: 12.8,
    icon: 'i-carbon-data-base',
    history: [78, 80, 85, 82, 79, 84, 82, 81, 83, 82]
  },
  {
    id: 3,
    name: 'Network I/O',
    category: 'NETWORK',
    value: 125,
    unit: 'Mbps',
    status: 'normal',
    trend: 'down',
    change: -3.4,
    icon: 'i-carbon-network-3',
    history: [25, 30, 28, 35, 34, 32, 38, 36, 33, 34]
  },
  {
    id: 4,
    name: 'Disk Usage',
    category: 'STORAGE',
    value: 245,
    unit: 'GB',
    status: 'normal',
    trend: 'stable',
    change: 0.8,
    icon: 'i-carbon-data-volume',
    history: [52, 54, 58, 56, 53, 59, 56, 57, 55, 56]
  }
])

// 关键服务
const criticalServices = ref([
  {
    id: 1,
    name: 'API Gateway',
    endpoint: 'api.system.com',
    status: 'healthy',
    responseTime: 45,
    uptime: 99.9
  },
  {
    id: 2,
    name: 'Database Cluster',
    endpoint: 'db.system.com',
    status: 'healthy',
    responseTime: 12,
    uptime: 99.8
  },
  {
    id: 3,
    name: 'Cache Layer',
    endpoint: 'cache.system.com',
    status: 'warning',
    responseTime: 156,
    uptime: 98.5
  },
  {
    id: 4,
    name: 'Message Queue',
    endpoint: 'mq.system.com',
    status: 'healthy',
    responseTime: 23,
    uptime: 99.7
  }
])

// 系统警报
const systemAlerts = ref([
  {
    id: 1,
    severity: 'warning',
    message: 'High memory usage detected on node-03',
    timestamp: '2 min ago',
    source: 'MONITORING'
  },
  {
    id: 2,
    severity: 'info',
    message: 'Scheduled maintenance completed successfully',
    timestamp: '15 min ago',
    source: 'SYSTEM'
  }
])

// 快速统计
const quickStats = ref([
  { label: 'ACTIVE USERS', value: '2,847' },
  { label: 'REQUESTS/SEC', value: '1,234' },
  { label: 'ERROR RATE', value: '0.02%' },
  { label: 'RESPONSE TIME', value: '45ms' }
])

// 计算总体状态
const overallStatus = computed(() => {
  const warningCount = coreMetrics.value.filter(m => m.status === 'warning').length
  const errorCount = coreMetrics.value.filter(m => m.status === 'error').length
  
  if (errorCount > 0) return 'critical'
  if (warningCount > 1) return 'warning'
  return 'healthy'
})

// 工具函数
const getTrendIcon = (trend: string) => {
  const icons = {
    up: 'i-carbon-trending-up',
    down: 'i-carbon-trending-down',
    stable: 'i-carbon-trending-flat'
  }
  return icons[trend] || 'i-carbon-trending-flat'
}

const getAlertIcon = (severity: string) => {
  const icons = {
    critical: 'i-carbon-warning-filled',
    warning: 'i-carbon-warning',
    info: 'i-carbon-information'
  }
  return icons[severity] || 'i-carbon-information'
}

// 警报操作
const acknowledgeAlert = (id: number) => {
  console.log('Acknowledged alert:', id)
}

const dismissAlert = (id: number) => {
  const index = systemAlerts.value.findIndex(alert => alert.id === id)
  if (index > -1) {
    systemAlerts.value.splice(index, 1)
  }
}

// 数据更新
const updateData = () => {
  // 更新核心指标
  coreMetrics.value.forEach(metric => {
    const variation = (Math.random() - 0.5) * 10
    const newValue = Math.max(0, metric.value + variation)
    
    // 更新历史数据
    metric.history.shift()
    metric.history.push(Math.round((newValue / (metric.unit === '%' ? 1 : 10)) * 100))
    
    // 更新当前值
    if (metric.unit === '%') {
      metric.value = Math.min(100, Math.round(newValue))
    } else {
      metric.value = Number(newValue.toFixed(1))
    }
    
    // 更新状态
    if (metric.unit === '%' && metric.value > 80) {
      metric.status = 'warning'
    } else if (metric.unit === '%' && metric.value > 95) {
      metric.status = 'error'
    } else {
      metric.status = 'normal'
    }
  })

  // 更新系统负载
  systemLoad.value = Math.max(0, Math.min(100, systemLoad.value + (Math.random() - 0.5) * 10))
}

// 定时更新
let updateInterval: NodeJS.Timeout

onMounted(() => {
  updateInterval = setInterval(updateData, 3000)
})

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
  }
})
</script>

<style scoped>
.system-status-panel {
  padding: 2rem;
  min-height: 600px;
}

/* 🎯 状态头部 */
.status-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.status-title {
  font-size: 1.5rem;
  font-weight: 700;
  font-family: 'Courier New', monospace;
  margin-bottom: 0.5rem;
}

.system-uptime {
  display: flex;
  gap: 1rem;
}

.overall-status {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.overall-status.healthy {
  background: rgba(0, 255, 136, 0.1);
  color: var(--quantum-accent);
  border: 1px solid rgba(0, 255, 136, 0.3);
}

.overall-status.warning {
  background: rgba(255, 170, 0, 0.1);
  color: var(--quantum-warning);
  border: 1px solid rgba(255, 170, 0, 0.3);
}

.overall-status.critical {
  background: rgba(255, 71, 87, 0.1);
  color: var(--quantum-error);
  border: 1px solid rgba(255, 71, 87, 0.3);
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: currentColor;
  animation: pulse 2s ease-in-out infinite;
}

/* 📊 核心指标网格 */
.core-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metric-card {
  background: rgba(0, 212, 255, 0.05);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--glow-cyber-primary);
}

.metric-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.metric-icon {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 212, 255, 0.1);
  border-radius: 8px;
  font-size: 1.25rem;
}

.metric-name {
  font-weight: 600;
  color: var(--quantum-fg-primary);
}

.metric-category {
  font-size: 0.75rem;
  color: var(--quantum-fg-muted);
  font-family: 'Courier New', monospace;
}

.metric-value-section {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  font-family: 'Courier New', monospace;
}

.metric-unit {
  font-size: 0.875rem;
  color: var(--quantum-fg-secondary);
}

.sparkline {
  display: flex;
  align-items: end;
  gap: 2px;
  height: 40px;
  margin-bottom: 1rem;
}

.sparkline-point {
  flex: 1;
  background: linear-gradient(135deg, var(--quantum-primary), var(--quantum-secondary));
  border-radius: 1px;
  animation: sparklineGrow 0.5s ease-out;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.metric-trend.up {
  color: var(--quantum-accent);
}

.metric-trend.down {
  color: var(--quantum-error);
}

.metric-trend.stable {
  color: var(--quantum-fg-muted);
}

/* 🌐 服务状态 */
.services-status {
  margin-bottom: 2rem;
}

.services-title {
  font-size: 1.125rem;
  font-weight: 700;
  font-family: 'Courier New', monospace;
  margin-bottom: 1rem;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.service-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: rgba(0, 212, 255, 0.03);
  border: 1px solid rgba(0, 212, 255, 0.1);
  border-radius: 6px;
}

.service-name {
  font-weight: 600;
  color: var(--quantum-fg-primary);
}

.service-endpoint {
  font-size: 0.75rem;
  color: var(--quantum-fg-muted);
  font-family: 'Courier New', monospace;
}

.service-metrics {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.service-metric {
  display: flex;
  gap: 0.5rem;
  font-size: 0.75rem;
  font-family: 'Courier New', monospace;
}

.service-status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  font-family: 'Courier New', monospace;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.status-dot.healthy {
  background: var(--quantum-accent);
}

.status-dot.warning {
  background: var(--quantum-warning);
}

.status-dot.error {
  background: var(--quantum-error);
}

/* ⚠️ 系统警报 */
.system-alerts {
  margin-bottom: 2rem;
}

.alerts-title {
  font-size: 1.125rem;
  font-weight: 700;
  font-family: 'Courier New', monospace;
  margin-bottom: 1rem;
}

.alert-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  margin-bottom: 0.5rem;
  border-radius: 6px;
  animation: slideIn 0.3s ease-out;
}

.alert-item.warning {
  background: rgba(255, 170, 0, 0.1);
  border: 1px solid rgba(255, 170, 0, 0.3);
}

.alert-item.info {
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
}

.alert-icon {
  font-size: 1.25rem;
  color: currentColor;
}

.alert-content {
  flex: 1;
}

.alert-message {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.alert-details {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: var(--quantum-fg-muted);
  font-family: 'Courier New', monospace;
}

.alert-actions {
  display: flex;
  gap: 0.5rem;
}

.alert-btn {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 4px;
  color: var(--quantum-fg-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.alert-btn:hover {
  background: rgba(0, 212, 255, 0.1);
  color: var(--quantum-primary);
}

/* 📈 快速统计 */
.quick-stats {
  border-top: 1px solid rgba(0, 212, 255, 0.2);
  padding-top: 1.5rem;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: rgba(0, 212, 255, 0.03);
  border-radius: 6px;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  font-family: 'Courier New', monospace;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--quantum-fg-secondary);
  font-family: 'Courier New', monospace;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* 🎬 动画 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes sparklineGrow {
  from { height: 0; }
  to { height: var(--final-height, 50%); }
}

@keyframes slideIn {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

/* 📱 响应式 */
@media (max-width: 768px) {
  .system-status-panel {
    padding: 1rem;
  }
  
  .status-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .core-metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .services-grid {
    grid-template-columns: 1fr;
  }
  
  .service-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}
</style>
