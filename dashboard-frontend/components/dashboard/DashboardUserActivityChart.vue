<template>
  <div class="user-activity-chart-container">
    <div class="chart-placeholder">
      <div class="placeholder-content">
        <i class="i-carbon-user-activity text-4xl text-[var(--quantum-fg-muted)] mb-4"></i>
        <h4 class="text-lg font-semibold text-[var(--quantum-fg-primary)] mb-2">
          用户活动图表
        </h4>
        <p class="text-sm text-[var(--quantum-fg-secondary)] mb-4">
          实时用户活动统计
        </p>
        
        <!-- 活动统计 -->
        <div class="activity-stats grid grid-cols-2 gap-4 w-full max-w-md mb-6">
          <div class="stat-item text-center">
            <div class="stat-value text-xl font-bold text-blue-500">
              {{ activityData.activeUsers }}
            </div>
            <div class="stat-label text-xs text-[var(--quantum-fg-secondary)]">
              活跃用户
            </div>
          </div>
          <div class="stat-item text-center">
            <div class="stat-value text-xl font-bold text-green-500">
              {{ activityData.newUsers }}
            </div>
            <div class="stat-label text-xs text-[var(--quantum-fg-secondary)]">
              新增用户
            </div>
          </div>
          <div class="stat-item text-center">
            <div class="stat-value text-xl font-bold text-purple-500">
              {{ activityData.sessions }}
            </div>
            <div class="stat-label text-xs text-[var(--quantum-fg-secondary)]">
              会话数
            </div>
          </div>
          <div class="stat-item text-center">
            <div class="stat-value text-xl font-bold text-orange-500">
              {{ activityData.avgDuration }}m
            </div>
            <div class="stat-label text-xs text-[var(--quantum-fg-secondary)]">
              平均时长
            </div>
          </div>
        </div>

        <!-- 活动趋势 -->
        <div class="activity-trend mb-4">
          <div class="trend-title text-sm font-medium text-[var(--quantum-fg-primary)] mb-2">
            24小时活动趋势
          </div>
          <div class="trend-bars flex items-end justify-center gap-1 h-16">
            <div v-for="(value, index) in trendData" :key="index" 
                 class="trend-bar bg-[var(--quantum-primary)] rounded-t"
                 :style="{ height: `${value}%`, width: '8px' }">
            </div>
          </div>
        </div>

        <div class="chart-note text-xs text-[var(--quantum-fg-muted)]">
          * 完整图表组件将在后续版本中实现
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 响应式数据
const activityData = ref({
  activeUsers: 1234,
  newUsers: 89,
  sessions: 2567,
  avgDuration: 15
})

// 趋势数据（24小时，每小时一个数据点）
const trendData = ref<number[]>([])

// 生成模拟趋势数据
const generateTrendData = () => {
  const data = []
  for (let i = 0; i < 24; i++) {
    // 模拟一天中的活动模式：白天高，夜晚低
    const hour = i
    let baseValue = 30
    
    if (hour >= 6 && hour <= 22) {
      // 白天时间，活动较高
      baseValue = 50 + Math.sin((hour - 6) / 16 * Math.PI) * 30
    } else {
      // 夜晚时间，活动较低
      baseValue = 20 + Math.random() * 20
    }
    
    data.push(Math.max(10, Math.min(90, baseValue + (Math.random() - 0.5) * 20)))
  }
  return data
}

// 模拟数据更新
const updateActivityData = () => {
  activityData.value = {
    activeUsers: Math.floor(Math.random() * 500) + 1000, // 1000-1500
    newUsers: Math.floor(Math.random() * 50) + 50, // 50-100
    sessions: Math.floor(Math.random() * 1000) + 2000, // 2000-3000
    avgDuration: Math.floor(Math.random() * 10) + 10 // 10-20分钟
  }
  
  trendData.value = generateTrendData()
}

// 定时更新数据
let updateInterval: NodeJS.Timeout

onMounted(() => {
  updateActivityData()
  
  // 每60秒更新一次数据
  updateInterval = setInterval(updateActivityData, 60000)
})

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
  }
})
</script>

<style scoped>
.user-activity-chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--quantum-bg-elevated);
  border: 2px dashed var(--quantum-border-color);
  border-radius: 0.5rem;
  transition: all var(--dashboard-transition-normal);
}

.chart-placeholder:hover {
  border-color: var(--quantum-primary);
  background: rgba(0, 212, 255, 0.02);
}

.placeholder-content {
  text-align: center;
  padding: 2rem;
  width: 100%;
}

.activity-stats {
  margin: 1rem auto;
}

.stat-item {
  padding: 0.75rem;
  background: var(--quantum-bg-surface);
  border-radius: 0.5rem;
  border: 1px solid var(--quantum-border-color);
}

.stat-value {
  margin-bottom: 0.25rem;
}

.activity-trend {
  background: var(--quantum-bg-surface);
  border-radius: 0.5rem;
  border: 1px solid var(--quantum-border-color);
  padding: 1rem;
  margin: 0 auto;
  max-width: 20rem;
}

.trend-bars {
  background: var(--quantum-bg-elevated);
  border-radius: 0.25rem;
  padding: 0.5rem;
}

.trend-bar {
  opacity: 0.8;
  transition: opacity var(--dashboard-transition-fast);
}

.trend-bar:hover {
  opacity: 1;
}

.chart-note {
  font-style: italic;
  opacity: 0.7;
}

/* 📱 响应式设计 */
@media (max-width: 640px) {
  .placeholder-content {
    padding: 1rem;
  }
  
  .activity-stats {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .stat-value {
    font-size: 1rem;
  }
  
  .activity-trend {
    max-width: 16rem;
  }
  
  .trend-bars {
    height: 3rem;
  }
}
</style>
