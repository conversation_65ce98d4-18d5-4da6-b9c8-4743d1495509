<template>
  <div class="notifications-container">
    <!-- 通知列表 -->
    <TransitionGroup name="notification" tag="div" class="notifications-list">
      <div
        v-for="notification in notifications"
        :key="notification.id"
        class="notification-item"
        :class="[
          `notification-${notification.type}`,
          { 'notification-dismissible': notification.dismissible }
        ]"
      >
        <!-- 图标 -->
        <div class="notification-icon">
          <i :class="getNotificationIcon(notification.type)"></i>
        </div>

        <!-- 内容 -->
        <div class="notification-content">
          <div v-if="notification.title" class="notification-title">
            {{ notification.title }}
          </div>
          <div class="notification-message">
            {{ notification.message }}
          </div>
          <div v-if="notification.actions" class="notification-actions">
            <button
              v-for="action in notification.actions"
              :key="action.label"
              @click="handleAction(notification, action)"
              class="notification-action-btn"
              :class="action.type || 'primary'"
            >
              {{ action.label }}
            </button>
          </div>
        </div>

        <!-- 关闭按钮 -->
        <button
          v-if="notification.dismissible !== false"
          @click="dismissNotification(notification.id)"
          class="notification-close"
        >
          <i class="i-carbon-close"></i>
        </button>

        <!-- 进度条 -->
        <div
          v-if="notification.duration"
          class="notification-progress"
          :style="{ animationDuration: `${notification.duration}ms` }"
        ></div>
      </div>
    </TransitionGroup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 通知类型定义
interface NotificationAction {
  label: string
  type?: 'primary' | 'secondary' | 'danger'
  handler: () => void
}

interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title?: string
  message: string
  duration?: number
  dismissible?: boolean
  actions?: NotificationAction[]
}

// 通知列表
const notifications = ref<Notification[]>([])

// 获取通知图标
const getNotificationIcon = (type: string) => {
  const icons = {
    success: 'i-carbon-checkmark-filled',
    error: 'i-carbon-error-filled',
    warning: 'i-carbon-warning-filled',
    info: 'i-carbon-information-filled'
  }
  return icons[type] || 'i-carbon-information-filled'
}

// 添加通知
const addNotification = (notification: Omit<Notification, 'id'>) => {
  const id = `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  const newNotification: Notification = {
    id,
    dismissible: true,
    duration: 5000,
    ...notification
  }

  notifications.value.push(newNotification)

  // 自动移除
  if (newNotification.duration && newNotification.duration > 0) {
    setTimeout(() => {
      dismissNotification(id)
    }, newNotification.duration)
  }

  return id
}

// 移除通知
const dismissNotification = (id: string) => {
  const index = notifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
}

// 清空所有通知
const clearAllNotifications = () => {
  notifications.value = []
}

// 处理操作按钮
const handleAction = (notification: Notification, action: NotificationAction) => {
  action.handler()
  if (notification.dismissible !== false) {
    dismissNotification(notification.id)
  }
}

// 全局通知方法
const showSuccess = (message: string, options?: Partial<Notification>) => {
  return addNotification({
    type: 'success',
    message,
    ...options
  })
}

const showError = (message: string, options?: Partial<Notification>) => {
  return addNotification({
    type: 'error',
    message,
    duration: 0, // 错误通知不自动消失
    ...options
  })
}

const showWarning = (message: string, options?: Partial<Notification>) => {
  return addNotification({
    type: 'warning',
    message,
    ...options
  })
}

const showInfo = (message: string, options?: Partial<Notification>) => {
  return addNotification({
    type: 'info',
    message,
    ...options
  })
}

// 监听全局通知事件
const handleGlobalNotification = (event: CustomEvent) => {
  const { type, message, options } = event.detail
  addNotification({
    type,
    message,
    ...options
  })
}

// 生命周期
onMounted(() => {
  // 监听全局通知事件
  window.addEventListener('show-notification', handleGlobalNotification as EventListener)
  
  // 示例通知（开发时使用）
  if (process.dev) {
    setTimeout(() => {
      showInfo('Dashboard 已加载完成', {
        title: '系统通知',
        duration: 3000
      })
    }, 1000)
  }
})

onUnmounted(() => {
  window.removeEventListener('show-notification', handleGlobalNotification as EventListener)
})

// 提供给全局使用
provide('notifications', {
  showSuccess,
  showError,
  showWarning,
  showInfo,
  addNotification,
  dismissNotification,
  clearAllNotifications
})

// 导出方法
defineExpose({
  showSuccess,
  showError,
  showWarning,
  showInfo,
  addNotification,
  dismissNotification,
  clearAllNotifications
})
</script>

<style scoped>
/* 🔔 通知容器 */
.notifications-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: var(--dashboard-z-toast);
  pointer-events: none;
}

.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-width: 24rem;
}

/* 🎯 通知项 */
.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background: var(--quantum-bg-surface);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.75rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  pointer-events: auto;
  position: relative;
  overflow: hidden;
  min-width: 20rem;
}

/* 通知类型样式 */
.notification-success {
  border-left: 4px solid var(--quantum-success);
}

.notification-error {
  border-left: 4px solid var(--quantum-error);
}

.notification-warning {
  border-left: 4px solid var(--quantum-warning);
}

.notification-info {
  border-left: 4px solid var(--quantum-info);
}

/* 🎨 通知图标 */
.notification-icon {
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.notification-success .notification-icon {
  background: rgba(46, 213, 115, 0.1);
  color: var(--quantum-success);
}

.notification-error .notification-icon {
  background: rgba(var(--quantum-error-rgb), 0.1);
  color: var(--quantum-error);
}

.notification-warning .notification-icon {
  background: rgba(var(--quantum-warning-rgb), 0.1);
  color: var(--quantum-warning);
}

.notification-info .notification-icon {
  background: rgba(var(--quantum-info-rgb), 0.1);
  color: var(--quantum-info);
}

/* 📝 通知内容 */
.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--quantum-fg-primary);
  margin-bottom: 0.25rem;
}

.notification-message {
  font-size: 0.875rem;
  color: var(--quantum-fg-secondary);
  line-height: 1.4;
}

/* 🎛️ 通知操作 */
.notification-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.notification-action-btn {
  padding: 0.375rem 0.75rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--dashboard-transition-fast);
}

.notification-action-btn.primary {
  background: var(--quantum-primary);
  color: white;
}

.notification-action-btn.primary:hover {
  background: var(--quantum-bg-elevated);
}

.notification-action-btn.secondary {
  background: var(--quantum-bg-elevated);
  color: var(--quantum-fg-secondary);
  border: 1px solid var(--quantum-border-color);
}

.notification-action-btn.secondary:hover {
  background: var(--quantum-bg-surface);
  color: var(--quantum-fg-primary);
}

.notification-action-btn.danger {
  background: var(--quantum-error);
  color: white;
}

.notification-action-btn.danger:hover {
  background: #dc2626;
}

/* ❌ 关闭按钮 */
.notification-close {
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  border-radius: 0.375rem;
  color: var(--quantum-fg-muted);
  cursor: pointer;
  transition: all var(--dashboard-transition-fast);
  flex-shrink: 0;
}

.notification-close:hover {
  background: var(--quantum-bg-elevated);
  color: var(--quantum-fg-primary);
}

/* ⏱️ 进度条 */
.notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background: var(--quantum-primary);
  animation: notificationProgress linear forwards;
}

@keyframes notificationProgress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* 🎬 过渡动画 */
.notification-enter-active {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.notification-leave-active {
  transition: all 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%) scale(0.9);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%) scale(0.9);
}

.notification-move {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 📱 响应式 */
@media (max-width: 768px) {
  .notifications-container {
    top: 0.5rem;
    right: 0.5rem;
    left: 0.5rem;
  }
  
  .notifications-list {
    max-width: none;
  }
  
  .notification-item {
    min-width: auto;
    padding: 0.75rem;
  }
  
  .notification-title {
    font-size: 0.8125rem;
  }
  
  .notification-message {
    font-size: 0.8125rem;
  }
}

/* 🎭 性能优化 */
@media (prefers-reduced-motion: reduce) {
  .notification-enter-active,
  .notification-leave-active,
  .notification-move {
    transition: none !important;
  }
  
  .notification-progress {
    animation: none !important;
  }
}
</style>
