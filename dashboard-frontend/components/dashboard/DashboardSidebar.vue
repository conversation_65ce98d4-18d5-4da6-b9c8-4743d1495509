<template>
  <div class="dashboard-sidebar-container" :class="{ collapsed }">
    <!-- 🎯 Logo 区域 -->
    <div class="sidebar-logo">
      <NuxtLink to="/" class="logo-link">
        <div class="logo-icon">
          <i class="i-carbon-3d-cursor text-2xl quantum-text-gradient"></i>
        </div>
        <div v-if="!collapsed" class="logo-text">
          <span class="logo-title quantum-text-gradient">AR-System</span>
          <span class="logo-subtitle">Dashboard</span>
        </div>
      </NuxtLink>

      <!-- 侧边栏内不需要切换按钮，统一在Header中管理 -->
    </div>

    <!-- 🧭 导航菜单 -->
    <nav class="sidebar-nav">
      <div class="nav-section">
        <div v-if="!collapsed" class="nav-section-title">概览</div>

        <!-- 系统概览 -->
        <NuxtLink
          to="/"
          class="nav-item"
          :class="{ active: route.path === '/' }"
          :title="collapsed ? '系统概览' : ''"
        >
          <i class="nav-icon i-carbon-dashboard"></i>
          <span v-if="!collapsed" class="nav-text">系统概览</span>
        </NuxtLink>

        <!-- 数据分析 -->
        <NuxtLink
          to="/analytics"
          class="nav-item"
          :class="{ active: route.path.startsWith('/analytics') }"
          :title="collapsed ? '数据分析' : ''"
        >
          <i class="nav-icon i-carbon-analytics"></i>
          <span v-if="!collapsed" class="nav-text">数据分析</span>
        </NuxtLink>

        <!-- 数据分析子页面 -->
        <div v-if="!collapsed && route.path.startsWith('/analytics')" class="nav-sub-items">
          <NuxtLink
            to="/analytics"
            class="nav-sub-item"
            :class="{ active: route.path === '/analytics' }"
          >
            <span class="nav-text">数据概览</span>
          </NuxtLink>
          <NuxtLink
            to="/analytics/realtime"
            class="nav-sub-item"
            :class="{ active: route.path === '/analytics/realtime' }"
          >
            <span class="nav-text">实时监控</span>
          </NuxtLink>
          <NuxtLink
            to="/analytics/reports"
            class="nav-sub-item"
            :class="{ active: route.path === '/analytics/reports' }"
          >
            <span class="nav-text">报表中心</span>
          </NuxtLink>
        </div>


      </div>

      <div class="nav-section">
        <div v-if="!collapsed" class="nav-section-title">管理</div>

        <!-- 设备管理 -->
        <NuxtLink
          to="/devices"
          class="nav-item"
          :class="{ active: route.path.startsWith('/devices') }"
          :title="collapsed ? '设备管理' : ''"
        >
          <i class="nav-icon i-carbon-devices"></i>
          <span v-if="!collapsed" class="nav-text">设备管理</span>
          <span v-if="!collapsed && deviceStats.online > 0" class="nav-badge">
            {{ deviceStats.online }}
          </span>
        </NuxtLink>

        <!-- 应用管理 -->
        <NuxtLink
          to="/applications"
          class="nav-item"
          :class="{ active: route.path.startsWith('/applications') }"
          :title="collapsed ? '应用管理' : ''"
        >
          <i class="nav-icon i-carbon-application"></i>
          <span v-if="!collapsed" class="nav-text">应用管理</span>
          <span v-if="!collapsed" class="nav-badge">156</span>
        </NuxtLink>

        <!-- 应用管理子页面 -->
        <div v-if="!collapsed && route.path.startsWith('/applications')" class="nav-sub-items">
          <NuxtLink
            to="/applications"
            class="nav-sub-item"
            :class="{ active: route.path === '/applications' }"
          >
            <span class="nav-text">应用列表</span>
          </NuxtLink>
          <NuxtLink
            to="/applications/store"
            class="nav-sub-item"
            :class="{ active: route.path === '/applications/store' }"
          >
            <span class="nav-text">应用商店</span>
          </NuxtLink>
          <NuxtLink
            to="/applications/versions"
            class="nav-sub-item"
            :class="{ active: route.path === '/applications/versions' }"
          >
            <span class="nav-text">版本管理</span>
          </NuxtLink>
        </div>

        <!-- 用户管理 -->
        <NuxtLink
          to="/users"
          class="nav-item"
          :class="{ active: route.path.startsWith('/users') }"
          :title="collapsed ? '用户管理' : ''"
        >
          <i class="nav-icon i-carbon-user-multiple"></i>
          <span v-if="!collapsed" class="nav-text">用户管理</span>
          <span v-if="!collapsed" class="nav-badge">2.4K</span>
        </NuxtLink>

        <!-- 用户管理子页面 -->
        <div v-if="!collapsed && route.path.startsWith('/users')" class="nav-sub-items">
          <NuxtLink
            to="/users"
            class="nav-sub-item"
            :class="{ active: route.path === '/users' }"
          >
            <span class="nav-text">用户列表</span>
          </NuxtLink>
          <NuxtLink
            to="/users/roles"
            class="nav-sub-item"
            :class="{ active: route.path === '/users/roles' }"
          >
            <span class="nav-text">角色管理</span>
          </NuxtLink>
          <NuxtLink
            to="/users/permissions"
            class="nav-sub-item"
            :class="{ active: route.path === '/users/permissions' }"
          >
            <span class="nav-text">权限管理</span>
          </NuxtLink>
        </div>
      </div>

      <div class="nav-section">
        <div v-if="!collapsed" class="nav-section-title">社区与电商</div>

        <!-- 社区管理 -->
        <div class="nav-group">
          <NuxtLink
            to="/community"
            class="nav-item"
            :class="{ active: route.path === '/community' }"
            :title="collapsed ? '社区管理' : ''"
          >
            <i class="nav-icon i-carbon-user-speaker"></i>
            <span v-if="!collapsed" class="nav-text">社区管理</span>
            <span v-if="!collapsed" class="nav-badge">24.5K</span>
          </NuxtLink>

          <!-- 社区子页面 -->
          <div v-if="!collapsed && route.path.startsWith('/community')" class="nav-sub-items">
            <NuxtLink
              to="/community/feed"
              class="nav-sub-item"
              :class="{ active: route.path === '/community/feed' }"
            >
              <span class="nav-text">社区动态</span>
            </NuxtLink>
            <NuxtLink
              to="/community/projects"
              class="nav-sub-item"
              :class="{ active: route.path === '/community/projects' }"
            >
              <span class="nav-text">协作项目</span>
            </NuxtLink>
            <NuxtLink
              to="/community/knowledge"
              class="nav-sub-item"
              :class="{ active: route.path === '/community/knowledge' }"
            >
              <span class="nav-text">知识库</span>
            </NuxtLink>
          </div>
        </div>

        <!-- 电商管理 -->
        <div class="nav-group">
          <NuxtLink
            to="/ecommerce"
            class="nav-item"
            :class="{ active: route.path === '/ecommerce' }"
            :title="collapsed ? '电商管理' : ''"
          >
            <i class="nav-icon i-carbon-shopping-cart"></i>
            <span v-if="!collapsed" class="nav-text">电商管理</span>
            <span v-if="!collapsed" class="nav-badge success">$2.4M</span>
          </NuxtLink>

          <!-- 电商子页面 -->
          <div v-if="!collapsed && route.path.startsWith('/ecommerce')" class="nav-sub-items">
            <NuxtLink
              to="/ecommerce/products"
              class="nav-sub-item"
              :class="{ active: route.path === '/ecommerce/products' }"
            >
              <span class="nav-text">商品管理</span>
            </NuxtLink>
            <NuxtLink
              to="/ecommerce/orders"
              class="nav-sub-item"
              :class="{ active: route.path === '/ecommerce/orders' }"
            >
              <span class="nav-text">订单管理</span>
            </NuxtLink>
          </div>
        </div>
      </div>

      <div class="nav-section">
        <div v-if="!collapsed" class="nav-section-title">开发与系统</div>

        <!-- 开发者工具 -->
        <div class="nav-group">
          <NuxtLink
            to="/developer-tools"
            class="nav-item"
            :class="{ active: route.path === '/developer-tools' }"
            :title="collapsed ? '开发者工具' : ''"
          >
            <i class="nav-icon i-carbon-code"></i>
            <span v-if="!collapsed" class="nav-text">开发者工具</span>
          </NuxtLink>

          <!-- 开发者工具子页面 -->
          <div v-if="!collapsed && route.path.startsWith('/developer-tools')" class="nav-sub-items">
            <NuxtLink
              to="/developer-tools/console"
              class="nav-sub-item"
              :class="{ active: route.path === '/developer-tools/console' }"
            >
              <span class="nav-text">量子控制台</span>
            </NuxtLink>
            <NuxtLink
              to="/developer-tools/api-tester"
              class="nav-sub-item"
              :class="{ active: route.path === '/developer-tools/api-tester' }"
            >
              <span class="nav-text">API测试器</span>
            </NuxtLink>
            <NuxtLink
              to="/developer-tools/performance"
              class="nav-sub-item"
              :class="{ active: route.path === '/developer-tools/performance' }"
            >
              <span class="nav-text">性能分析器</span>
            </NuxtLink>
          </div>
        </div>

        <!-- 通知中心 -->
        <NuxtLink
          to="/notifications"
          class="nav-item"
          :class="{ active: route.path.startsWith('/notifications') }"
          :title="collapsed ? '通知中心' : ''"
        >
          <i class="nav-icon i-carbon-notification"></i>
          <span v-if="!collapsed" class="nav-text">通知中心</span>
          <span v-if="!collapsed && notificationStats.unread > 0" class="nav-badge warning">
            {{ notificationStats.unread }}
          </span>
        </NuxtLink>

        <!-- 系统设置 -->
        <NuxtLink
          to="/settings"
          class="nav-item"
          :class="{ active: route.path.startsWith('/settings') }"
          :title="collapsed ? '系统设置' : ''"
        >
          <i class="nav-icon i-carbon-settings"></i>
          <span v-if="!collapsed" class="nav-text">系统设置</span>
        </NuxtLink>
      </div>
    </nav>

    <!-- 🎛️ 底部控制区 -->
    <div class="sidebar-footer">
      <!-- 用户信息 -->
      <div v-if="!collapsed" class="user-info">
        <div class="user-avatar">
          <img src="/avatar-placeholder.svg" alt="用户头像" class="avatar-image">
          <div class="status-indicator online"></div>
        </div>
        <div class="user-details">
          <div class="user-name">管理员</div>
          <div class="user-role">系统管理员</div>
        </div>
      </div>

      <!-- 底部折叠按钮 -->
      <button
        @click="$emit('toggle')"
        class="bottom-collapse-btn"
        :class="{ collapsed }"
        :title="collapsed ? '展开侧边栏 (Ctrl+B)' : '折叠侧边栏 (Ctrl+B)'"
      >
        <i class="i-carbon-chevron-left" :class="{ 'rotate-180': collapsed }"></i>
        <span v-if="!collapsed" class="collapse-text">折叠</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// Props
interface Props {
  collapsed: boolean
}

defineProps<Props>()

// Emits
defineEmits<{
  toggle: []
}>()

// 路由
const route = useRoute()

// 模拟数据
const deviceStats = ref({
  online: 18,
  total: 24
})

const notificationStats = ref({
  unread: 12,
  total: 156
})
</script>

<style scoped>
/* 🎛️ 侧边栏容器 */
.dashboard-sidebar-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--quantum-bg-surface);
  border-right: 1px solid var(--quantum-border-color);
  transition: all var(--transition-normal);
  width: var(--sidebar-width);
}

.dashboard-sidebar-container.collapsed {
  width: var(--sidebar-collapsed-width);
}

.dashboard-sidebar-container.collapsed .nav-item {
  justify-content: center;
  padding: 0.75rem;
}

.dashboard-sidebar-container.collapsed .nav-icon {
  font-size: 1.25rem;
  width: 1.25rem;
  height: 1.25rem;
}

/* 收起状态下的工具提示优化 */
.dashboard-sidebar-container.collapsed .nav-item {
  position: relative;
}

.dashboard-sidebar-container.collapsed .nav-item:hover {
  background: var(--quantum-bg-elevated);
  transform: translateY(-1px);
}

.dashboard-sidebar-container.collapsed .nav-item.active {
  background: linear-gradient(135deg, var(--quantum-primary), var(--quantum-secondary));
  color: white;
}

.dashboard-sidebar-container.collapsed .nav-item.active .nav-icon {
  color: white;
}

/* 🎯 Logo 区域 */
.sidebar-logo {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid var(--quantum-border-color);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-decoration: none;
  transition: all var(--transition-fast);
  flex: 1;
}

.logo-link:hover {
  transform: translateY(-1px);
}

.logo-icon {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--quantum-bg-elevated);
  border-radius: 0.75rem;
  border: 1px solid var(--quantum-border-color);
}

.logo-text {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.logo-title {
  font-size: 1.125rem;
  font-weight: 700;
  line-height: 1.2;
}

.logo-subtitle {
  font-size: 0.75rem;
  color: var(--quantum-fg-secondary);
  font-weight: 500;
}

/* 侧边栏切换按钮已移至Header组件统一管理 */

/* 🧭 导航菜单 */
.sidebar-nav {
  flex: 1;
  padding: 1rem 0;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: 1.5rem;
}

.nav-section:last-child {
  margin-bottom: 0;
}

.nav-section-title {
  padding: 0 1rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--quantum-fg-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  margin: 0.25rem 0.5rem; /* 添加垂直间距 */
  border-radius: 0.75rem;
  color: var(--quantum-fg-secondary);
  text-decoration: none;
  transition: all var(--dashboard-transition-fast) var(--dashboard-ease-default);
  position: relative;
}

.nav-item:hover {
  background: var(--quantum-bg-elevated);
  color: var(--quantum-fg-primary);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-item.active {
  background: rgba(0, 212, 255, 0.1);
  color: var(--quantum-primary);
  border: 1px solid var(--quantum-border-glow);
}

.nav-item.active::before {
  content: '';
  position: absolute;
  left: -0.5rem;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 1.5rem;
  background: var(--quantum-primary);
  border-radius: 0 2px 2px 0;
}



.nav-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-text {
  font-size: 0.875rem;
  font-weight: 500;
  flex: 1;
}

.nav-badge {
  background: var(--quantum-primary);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 9999px;
  min-width: 1.25rem;
  text-align: center;
}

.nav-badge.warning {
  background: var(--quantum-warning);
}

.nav-badge.success {
  background: var(--quantum-success);
}

/* 子导航项容器 */
.nav-sub-items {
  margin-top: 0.5rem;
  margin-left: 1rem;
  border-left: 2px solid var(--quantum-border-color);
  padding-left: 0.5rem;
}

.nav-sub-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  margin: 0.25rem 0;
  border-radius: 0.5rem;
  color: var(--quantum-fg-muted);
  text-decoration: none;
  transition: all var(--transition-fast);
  position: relative;
  font-size: 0.875rem;
}

.nav-sub-item::before {
  content: '';
  position: absolute;
  left: -0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background: var(--quantum-fg-muted);
  border-radius: 50%;
  transition: all var(--transition-fast);
}

.nav-sub-item:hover {
  background: var(--quantum-bg-elevated);
  color: var(--quantum-fg-secondary);
  transform: translateX(2px);
}

.nav-sub-item:hover::before {
  background: var(--quantum-primary);
  transform: translateY(-50%) scale(1.2);
}

.nav-sub-item.active {
  background: rgba(0, 212, 255, 0.1);
  color: var(--quantum-primary);
  font-weight: 500;
}

.nav-sub-item.active::before {
  background: var(--quantum-primary);
  transform: translateY(-50%) scale(1.3);
  box-shadow: 0 0 8px rgba(0, 212, 255, 0.4);
}



/* 🎛️ 底部控制区 */
.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid var(--quantum-border-color);
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* 🔄 底部切换按钮 */
.bottom-collapse-btn {
  width: 100%;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.75rem;
  color: var(--quantum-fg-secondary);
  cursor: pointer;
  transition: all var(--dashboard-transition-fast) var(--dashboard-ease-default);
  font-size: 0.875rem;
  font-weight: 500;
  margin-top: 0.5rem;
}

.bottom-collapse-btn.collapsed {
  width: 2.5rem;
  height: 2.5rem;
  align-self: center;
}

.bottom-collapse-btn:hover {
  background: var(--quantum-primary);
  color: white;
  border-color: var(--quantum-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
}

.bottom-collapse-btn i {
  font-size: 1rem;
  transition: transform var(--dashboard-transition-normal) var(--dashboard-ease-default);
}

.collapse-text {
  transition: opacity var(--dashboard-transition-fast) var(--dashboard-ease-default);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--quantum-bg-elevated);
  border-radius: 0.75rem;
  border: 1px solid var(--quantum-border-color);
}

.user-avatar {
  position: relative;
  width: 2rem;
  height: 2rem;
  flex-shrink: 0;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  border: 2px solid var(--quantum-bg-surface);
}

.status-indicator.online {
  background: var(--quantum-success);
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--quantum-fg-primary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-role {
  font-size: 0.75rem;
  color: var(--quantum-fg-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 📱 全面响应式适配 */

/* 大屏幕 (1440px+) */
@media (min-width: 1440px) {
  .dashboard-sidebar-container {
    width: 18rem;
  }

  .sidebar-logo {
    padding: 2rem 1.5rem;
  }

  .nav-item {
    padding: 1rem 1.5rem;
    font-size: var(--text-base);
  }
}

/* 桌面端 (1025px - 1439px) */
@media (max-width: 1439px) and (min-width: 1025px) {
  /* 切换按钮样式已移至Header组件 */
}

/* 平板和移动端 (1024px以下) */
@media (max-width: 1024px) {
  .dashboard-sidebar-container {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform var(--transition-normal);
    width: var(--sidebar-width);
    background: var(--quantum-bg-surface);
    backdrop-filter: blur(30px);
    box-shadow: var(--quantum-shadow-elevated);
  }

  .dashboard-sidebar-container:not(.collapsed) {
    transform: translateX(0);
  }

  /* 切换按钮已移至Header组件 */
}

/* 平板竖屏 (768px - 1024px) */
@media (max-width: 1024px) and (min-width: 768px) {
  .dashboard-sidebar-container {
    width: 16rem;
  }

  .nav-item {
    padding: 1rem;
    font-size: var(--text-sm);
  }
}

/* 手机横屏 (640px - 767px) */
@media (max-width: 767px) and (min-width: 640px) {
  .dashboard-sidebar-container {
    width: 15rem;
  }

  .sidebar-logo {
    padding: 1rem;
  }

  .logo-text {
    font-size: var(--text-lg);
  }

  .nav-item {
    padding: 0.75rem 1rem;
    font-size: var(--text-sm);
  }

  .nav-icon {
    font-size: 1.125rem;
    width: 1.125rem;
    height: 1.125rem;
  }
}

/* 手机竖屏 (480px - 639px) */
@media (max-width: 639px) and (min-width: 480px) {
  .dashboard-sidebar-container {
    width: 14.5rem;
  }

  .sidebar-logo {
    padding: 1rem 0.75rem;
  }

  .logo-text {
    font-size: var(--text-base);
  }

  .nav-item {
    padding: 0.75rem;
    font-size: var(--text-sm);
    min-height: 2.75rem;
  }
}

/* 小手机 (320px - 479px) */
@media (max-width: 479px) {
  .dashboard-sidebar-container {
    width: 14rem;
    max-width: 85vw;
  }

  .sidebar-logo {
    padding: 0.75rem;
  }

  .logo-icon {
    width: 2rem;
    height: 2rem;
  }

  .logo-text {
    font-size: var(--text-sm);
  }

  .nav-item {
    padding: 0.75rem;
    font-size: var(--text-sm);
    min-height: 2.75rem;
  }

  .nav-icon {
    font-size: 1rem;
    width: 1rem;
    height: 1rem;
    min-width: 1rem;
  }

  .nav-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

/* 🎭 性能优化 */
@media (prefers-reduced-motion: reduce) {
  .dashboard-sidebar-container,
  .logo-link,
  .nav-item,
  .collapse-btn,
  .collapse-btn i {
    transition: none !important;
  }
}
</style>
