<template>
  <div class="performance-chart-container">
    <div class="chart-placeholder">
      <div class="placeholder-content">
        <i class="i-carbon-analytics text-4xl text-[var(--quantum-fg-muted)] mb-4"></i>
        <h4 class="text-lg font-semibold text-[var(--quantum-fg-primary)] mb-2">
          系统性能图表
        </h4>
        <p class="text-sm text-[var(--quantum-fg-secondary)] mb-4">
          时间范围: {{ timeRangeText }}
        </p>
        <div class="performance-metrics grid grid-cols-3 gap-4 w-full max-w-md">
          <div class="metric-item text-center">
            <div class="metric-value text-xl font-bold text-blue-500">
              {{ performanceData.cpu }}%
            </div>
            <div class="metric-label text-xs text-[var(--quantum-fg-secondary)]">
              CPU使用率
            </div>
          </div>
          <div class="metric-item text-center">
            <div class="metric-value text-xl font-bold text-green-500">
              {{ performanceData.memory }}%
            </div>
            <div class="metric-label text-xs text-[var(--quantum-fg-secondary)]">
              内存使用率
            </div>
          </div>
          <div class="metric-item text-center">
            <div class="metric-value text-xl font-bold text-purple-500">
              {{ performanceData.network }}MB/s
            </div>
            <div class="metric-label text-xs text-[var(--quantum-fg-secondary)]">
              网络流量
            </div>
          </div>
        </div>
        <div class="chart-note text-xs text-[var(--quantum-fg-muted)] mt-4">
          * 图表组件将在后续版本中实现
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'

// Props
interface Props {
  timeRange: string
}

const props = defineProps<Props>()

// 响应式数据
const performanceData = ref({
  cpu: 45,
  memory: 67,
  network: 12.5
})

// 计算属性
const timeRangeText = computed(() => {
  const rangeMap = {
    '1h': '最近1小时',
    '24h': '最近24小时',
    '7d': '最近7天'
  }
  return rangeMap[props.timeRange] || props.timeRange
})

// 模拟数据更新
const updatePerformanceData = () => {
  performanceData.value = {
    cpu: Math.floor(Math.random() * 40) + 30, // 30-70%
    memory: Math.floor(Math.random() * 30) + 50, // 50-80%
    network: Math.floor(Math.random() * 20) + 5 // 5-25 MB/s
  }
}

// 监听时间范围变化
watch(() => props.timeRange, () => {
  updatePerformanceData()
})

// 定时更新数据
let updateInterval: NodeJS.Timeout

onMounted(() => {
  updatePerformanceData()
  
  // 每30秒更新一次数据
  updateInterval = setInterval(updatePerformanceData, 30000)
})

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
  }
})
</script>

<style scoped>
.performance-chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--quantum-bg-elevated);
  border: 2px dashed var(--quantum-border-color);
  border-radius: 0.5rem;
  transition: all var(--dashboard-transition-normal);
}

.chart-placeholder:hover {
  border-color: var(--quantum-primary);
  background: rgba(0, 212, 255, 0.02);
}

.placeholder-content {
  text-align: center;
  padding: 2rem;
}

.performance-metrics {
  margin: 1rem auto;
}

.metric-item {
  padding: 0.75rem;
  background: var(--quantum-bg-surface);
  border-radius: 0.5rem;
  border: 1px solid var(--quantum-border-color);
}

.metric-value {
  margin-bottom: 0.25rem;
}

.chart-note {
  font-style: italic;
  opacity: 0.7;
}

/* 📱 响应式设计 */
@media (max-width: 640px) {
  .placeholder-content {
    padding: 1rem;
  }
  
  .performance-metrics {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .metric-value {
    font-size: 1rem;
  }
}
</style>
