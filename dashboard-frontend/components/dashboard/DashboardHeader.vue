<template>
  <div class="dashboard-header-container">
    <!-- 🍔 移动端菜单按钮 -->
    <button
      @click="$emit('toggleSidebar')"
      class="mobile-menu-btn"
      :class="{ active: !sidebarCollapsed }"
      :title="sidebarCollapsed ? '显示菜单' : '隐藏菜单'"
    >
      <i :class="sidebarCollapsed ? 'i-carbon-menu' : 'i-carbon-close'"></i>
    </button>

    <!-- 🔄 桌面端侧边栏切换按钮 -->
    <button
      @click="$emit('toggleSidebar')"
      class="desktop-sidebar-toggle"
      :title="sidebarCollapsed ? '展开侧边栏 (Ctrl+B)' : '折叠侧边栏 (Ctrl+B)'"
    >
      <i class="i-carbon-side-panel-close" :class="{ 'rotate-180': sidebarCollapsed }"></i>
    </button>

    <!-- 🔍 搜索区域 -->
    <div class="header-search">
      <div class="search-container">
        <i class="search-icon i-carbon-search"></i>
        <input 
          v-model="searchQuery"
          type="text" 
          placeholder="搜索设备、用户、订单..."
          class="search-input"
          @focus="showSearchResults = true"
          @blur="hideSearchResults"
          @keydown.enter="performSearch"
        >
        <kbd class="search-shortcut">⌘K</kbd>
      </div>
      
      <!-- 搜索结果下拉 -->
      <div v-if="showSearchResults && searchQuery" class="search-results">
        <div class="search-section">
          <div class="search-section-title">设备</div>
          <div v-for="device in filteredDevices" :key="device.id" class="search-item">
            <i class="i-carbon-devices text-blue-500"></i>
            <span>{{ device.name }}</span>
          </div>
        </div>
        <div class="search-section">
          <div class="search-section-title">用户</div>
          <div v-for="user in filteredUsers" :key="user.id" class="search-item">
            <i class="i-carbon-user text-green-500"></i>
            <span>{{ user.name }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 🎛️ 控制区域 -->
    <div class="header-controls">
      <!-- 通知 -->
      <div class="control-item notification-btn" @click="toggleNotifications">
        <i class="i-carbon-notification"></i>
        <span v-if="notificationCount > 0" class="notification-badge">
          {{ notificationCount > 99 ? '99+' : notificationCount }}
        </span>
      </div>

      <!-- 主题切换 -->
      <div class="control-item theme-toggle" @click="toggleTheme">
        <i :class="isDark ? 'i-carbon-sun' : 'i-carbon-moon'"></i>
      </div>

      <!-- 全屏切换 -->
      <div class="control-item fullscreen-btn" @click="toggleFullscreen">
        <i :class="isFullscreen ? 'i-carbon-minimize' : 'i-carbon-maximize'"></i>
      </div>

      <!-- 系统状态 -->
      <div class="control-item system-status" :class="systemStatus.status">
        <i class="status-icon i-carbon-circle-filled"></i>
        <span class="status-text">{{ systemStatus.text }}</span>
      </div>

      <!-- 用户菜单 -->
      <div class="control-item user-menu" @click="toggleUserMenu">
        <div class="user-avatar">
          <img :src="currentUser?.avatar || '/avatar-placeholder.svg'" alt="用户头像" class="avatar-image">
          <div class="status-indicator online"></div>
        </div>
        <div class="user-info">
          <div class="user-name">{{ currentUser?.full_name || currentUser?.username || '管理员' }}</div>
          <div class="user-role">{{ currentUser?.is_superuser ? '超级管理员' : '系统管理员' }}</div>
        </div>
        <i class="i-carbon-chevron-down dropdown-icon"></i>
      </div>
    </div>

    <!-- 🔔 通知面板 -->
    <div v-if="showNotifications" class="notifications-panel">
      <div class="panel-header">
        <h3>通知中心</h3>
        <button @click="markAllAsRead" class="mark-all-read">全部已读</button>
      </div>
      <div class="notifications-list">
        <div v-for="notification in notifications" :key="notification.id" 
             class="notification-item" :class="{ unread: !notification.read }">
          <div class="notification-icon" :class="notification.type">
            <i :class="getNotificationIcon(notification.type)"></i>
          </div>
          <div class="notification-content">
            <div class="notification-title">{{ notification.title }}</div>
            <div class="notification-message">{{ notification.message }}</div>
            <div class="notification-time">{{ formatTime(notification.time) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 👤 用户菜单下拉 -->
    <div v-if="showUserMenu" class="user-menu-dropdown">
      <div class="menu-section">
        <NuxtLink to="/profile" class="menu-item">
          <i class="i-carbon-user"></i>
          <span>个人资料</span>
        </NuxtLink>
        <NuxtLink to="/settings" class="menu-item">
          <i class="i-carbon-settings"></i>
          <span>系统设置</span>
        </NuxtLink>
        <div class="menu-item" @click="showShortcuts = true">
          <i class="i-carbon-keyboard"></i>
          <span>快捷键</span>
        </div>
      </div>
      <div class="menu-divider"></div>
      <div class="menu-section">
        <div class="menu-item danger" @click="handleLogout">
          <i class="i-carbon-logout"></i>
          <span>退出登录</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useGlobalTheme } from '~/composables/useTheme'

// Props
interface Props {
  sidebarCollapsed: boolean
}

defineProps<Props>()

// Emits
defineEmits<{
  toggleSidebar: []
}>()

// 认证相关
const authStore = useAuthStore()
const currentUser = computed(() => authStore.currentUser)

// 主题相关
const { isDark, toggleTheme } = useGlobalTheme()

// 搜索相关
const searchQuery = ref('')
const showSearchResults = ref(false)

// 通知相关
const showNotifications = ref(false)
const notificationCount = ref(5)
const notifications = ref([
  {
    id: 1,
    type: 'info',
    title: '系统更新',
    message: '系统已更新到最新版本 v2.1.0',
    time: new Date(Date.now() - 5 * 60 * 1000),
    read: false
  },
  {
    id: 2,
    type: 'warning',
    title: '设备离线',
    message: 'AR设备 #001 已离线超过 10 分钟',
    time: new Date(Date.now() - 15 * 60 * 1000),
    read: false
  },
  {
    id: 3,
    type: 'success',
    title: '订单完成',
    message: '订单 #QS202401150001 已成功完成',
    time: new Date(Date.now() - 30 * 60 * 1000),
    read: true
  }
])

// 用户菜单
const showUserMenu = ref(false)

// 全屏状态
const isFullscreen = ref(false)

// 系统状态
const systemStatus = ref({
  status: 'online',
  text: '系统正常'
})

// 模拟数据
const devices = ref([
  { id: 1, name: 'AR设备 #001' },
  { id: 2, name: 'AR设备 #002' },
  { id: 3, name: 'VR设备 #001' }
])

const users = ref([
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' }
])

// 计算属性
const filteredDevices = computed(() => {
  if (!searchQuery.value) return []
  return devices.value.filter(device => 
    device.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  ).slice(0, 3)
})

const filteredUsers = computed(() => {
  if (!searchQuery.value) return []
  return users.value.filter(user => 
    user.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  ).slice(0, 3)
})

// 方法
const toggleNotifications = () => {
  showNotifications.value = !showNotifications.value
  showUserMenu.value = false
}

const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
  showNotifications.value = false
}

const hideSearchResults = () => {
  setTimeout(() => {
    showSearchResults.value = false
  }, 200)
}

const performSearch = () => {
  console.log('搜索:', searchQuery.value)
  showSearchResults.value = false
}

const markAllAsRead = () => {
  notifications.value.forEach(n => n.read = true)
  notificationCount.value = 0
}

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

const handleLogout = async () => {
  try {
    authStore.logout()
    console.log('退出登录成功')
    await navigateTo('/login')
  } catch (error) {
    console.error('退出登录失败:', error)
  }
}

const getNotificationIcon = (type: string) => {
  const icons = {
    info: 'i-carbon-information',
    warning: 'i-carbon-warning',
    error: 'i-carbon-error',
    success: 'i-carbon-checkmark'
  }
  return icons[type] || 'i-carbon-information'
}

const formatTime = (time: Date) => {
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  const minutes = Math.floor(diff / 60000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  
  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}小时前`
  
  const days = Math.floor(hours / 24)
  return `${days}天前`
}

// 键盘快捷键
const handleKeydown = (event: KeyboardEvent) => {
  // Cmd/Ctrl + K 打开搜索
  if ((event.metaKey || event.ctrlKey) && event.key === 'k') {
    event.preventDefault()
    const searchInput = document.querySelector('.search-input') as HTMLInputElement
    searchInput?.focus()
  }
  
  // Escape 关闭下拉菜单
  if (event.key === 'Escape') {
    showNotifications.value = false
    showUserMenu.value = false
    showSearchResults.value = false
  }
}

// 点击外部关闭下拉菜单
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as Element
  
  if (!target.closest('.notification-btn') && !target.closest('.notifications-panel')) {
    showNotifications.value = false
  }
  
  if (!target.closest('.user-menu') && !target.closest('.user-menu-dropdown')) {
    showUserMenu.value = false
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
  document.addEventListener('click', handleClickOutside)
  
  // 监听全屏变化
  document.addEventListener('fullscreenchange', () => {
    isFullscreen.value = !!document.fullscreenElement
  })
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
/* 🎛️ 顶部导航容器 */
.dashboard-header-container {
  height: var(--header-height);
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0 1.5rem;
  background: var(--quantum-bg-surface);
  border-bottom: 1px solid var(--quantum-border-color);
  position: relative;
}

/* 🍔 移动端菜单按钮 */
.mobile-menu-btn {
  display: none;
  width: 2.5rem;
  height: 2.5rem;
  align-items: center;
  justify-content: center;
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  color: var(--quantum-fg-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.mobile-menu-btn:hover,
.mobile-menu-btn.active {
  background: var(--quantum-primary);
  color: white;
  border-color: var(--quantum-primary);
}

/* 🔄 桌面端侧边栏切换按钮 */
.desktop-sidebar-toggle {
  display: flex;
  width: 2.5rem;
  height: 2.5rem;
  align-items: center;
  justify-content: center;
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  color: var(--quantum-fg-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.desktop-sidebar-toggle:hover {
  background: var(--quantum-primary);
  color: white;
  border-color: var(--quantum-primary);
  box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.3);
}

.desktop-sidebar-toggle i {
  font-size: 1.125rem;
  transition: transform var(--transition-normal);
}

/* 🔍 搜索区域 */
.header-search {
  flex: 1;
  max-width: 32rem;
  position: relative;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  color: var(--quantum-fg-muted);
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  padding-right: 3rem;
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.75rem;
  color: var(--quantum-fg-primary);
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--quantum-primary);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.search-shortcut {
  position: absolute;
  right: 0.75rem;
  padding: 0.25rem 0.5rem;
  background: var(--quantum-bg-muted);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.25rem;
  font-size: 0.75rem;
  color: var(--quantum-fg-muted);
}

/* 搜索结果 */
.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 0.5rem;
  background: var(--quantum-bg-surface);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.75rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  z-index: var(--dashboard-z-dropdown);
  max-height: 20rem;
  overflow-y: auto;
}

.search-section {
  padding: 0.75rem;
}

.search-section:not(:last-child) {
  border-bottom: 1px solid var(--quantum-border-color);
}

.search-section-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--quantum-fg-muted);
  text-transform: uppercase;
  margin-bottom: 0.5rem;
}

.search-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: background var(--dashboard-transition-fast);
}

.search-item:hover {
  background: var(--quantum-bg-elevated);
}

/* 🎛️ 控制区域 */
.header-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.control-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all var(--dashboard-transition-fast);
  position: relative;
}

.control-item:hover {
  background: var(--quantum-bg-elevated);
}

/* 通知按钮 */
.notification-btn {
  position: relative;
}

.notification-badge {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  background: var(--quantum-error);
  color: white;
  font-size: 0.625rem;
  font-weight: 600;
  padding: 0.125rem 0.25rem;
  border-radius: 9999px;
  min-width: 1rem;
  text-align: center;
}

/* 系统状态 */
.system-status {
  font-size: 0.875rem;
}

.system-status.online .status-icon {
  color: var(--quantum-success);
}

.system-status.warning .status-icon {
  color: var(--quantum-warning);
}

.system-status.error .status-icon {
  color: var(--quantum-error);
}

/* 用户菜单 */
.user-menu {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.75rem;
}

.user-avatar {
  position: relative;
  width: 2rem;
  height: 2rem;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  border: 2px solid var(--quantum-bg-surface);
}

.status-indicator.online {
  background: var(--quantum-success);
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--quantum-fg-primary);
}

.user-role {
  font-size: 0.75rem;
  color: var(--quantum-fg-secondary);
}

.dropdown-icon {
  transition: transform var(--dashboard-transition-fast);
}

.user-menu:hover .dropdown-icon {
  transform: rotate(180deg);
}

/* 👤 用户菜单下拉 */
.user-menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  min-width: 12rem;
  background: var(--quantum-bg-surface);
  backdrop-filter: blur(20px);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.75rem;
  box-shadow: var(--quantum-shadow-elevated);
  z-index: 1000;
  overflow: hidden;
  animation: slideDown 0.2s ease-out;
}

.menu-section {
  padding: 0.5rem;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  color: var(--quantum-fg-primary);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.875rem;
}

.menu-item:hover {
  background: var(--quantum-bg-elevated);
  color: var(--quantum-primary);
  box-shadow: inset 3px 0 0 var(--quantum-primary);
}

.menu-item.danger {
  color: var(--quantum-error);
}

.menu-item.danger:hover {
  background: rgba(255, 71, 87, 0.1);
  color: var(--quantum-error);
}

.menu-item i {
  font-size: 1rem;
  opacity: 0.7;
}

.menu-divider {
  height: 1px;
  background: var(--quantum-border-color);
  margin: 0.5rem 0;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 🔔 通知面板样式 */
.notifications-panel {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  width: 24rem;
  max-width: 90vw;
  background: var(--quantum-bg-surface);
  backdrop-filter: blur(20px);
  border: 1px solid var(--quantum-border-color);
  border-radius: 1rem;
  box-shadow: var(--quantum-shadow-elevated);
  z-index: 1000;
  overflow: hidden;
  animation: slideDown 0.3s ease-out;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  border-bottom: 1px solid var(--quantum-border-color);
  background: var(--quantum-bg-elevated);
}

.panel-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--quantum-fg-primary);
}

.mark-all-read {
  padding: 0.375rem 0.75rem;
  background: transparent;
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.375rem;
  color: var(--quantum-fg-secondary);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.mark-all-read:hover {
  background: var(--quantum-primary);
  color: white;
  border-color: var(--quantum-primary);
}

.notifications-list {
  max-height: 20rem;
  overflow-y: auto;
  padding: 0.5rem;
}

.notification-item {
  display: flex;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 0.75rem;
  margin-bottom: 0.5rem;
  transition: all var(--transition-fast);
  cursor: pointer;
}

.notification-item:hover {
  background: var(--quantum-bg-elevated);
}

.notification-item.unread {
  background: rgba(0, 212, 255, 0.05);
  border: 1px solid rgba(0, 212, 255, 0.1);
}

.notification-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.notification-icon.info {
  background: rgba(0, 212, 255, 0.1);
  color: var(--quantum-primary);
}

.notification-icon.warning {
  background: rgba(255, 170, 0, 0.1);
  color: var(--quantum-warning);
}

.notification-icon.error {
  background: rgba(255, 71, 87, 0.1);
  color: var(--quantum-error);
}

.notification-icon.success {
  background: rgba(0, 255, 136, 0.1);
  color: var(--quantum-success);
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--quantum-fg-primary);
  margin-bottom: 0.25rem;
}

.notification-message {
  font-size: 0.75rem;
  color: var(--quantum-fg-secondary);
  line-height: 1.4;
  margin-bottom: 0.25rem;
}

.notification-time {
  font-size: 0.625rem;
  color: var(--quantum-fg-muted);
}

/* 📱 响应式适配 - 与布局断点保持一致 */

/* 桌面端 (1024px+) - 显示完整Header功能 */
@media (min-width: 1024px) {
  .mobile-menu-btn {
    display: none;
  }

  .desktop-sidebar-toggle {
    display: flex;
  }

  .header-search {
    max-width: 20rem;
  }

  .user-info {
    display: flex;
  }

  .status-text {
    display: inline;
  }

  .search-shortcut {
    display: block;
  }
}

/* 移动端和平板 (1023px及以下) - 简化Header功能 */
@media (max-width: 1023px) {
  .mobile-menu-btn {
    display: flex;
  }

  .desktop-sidebar-toggle {
    display: none;
  }

  .user-info {
    display: none;
  }

  .header-search {
    max-width: 16rem;
  }

  .status-text {
    display: none;
  }
}

/* 手机端 (640px及以下) - 最小化Header */
@media (max-width: 640px) {
  .dashboard-header-container {
    padding: 0 1rem;
    gap: 0.5rem;
  }

  .header-search {
    max-width: 12rem;
  }

  .search-shortcut {
    display: none;
  }
}
</style>
