<template>
  <Teleport to="body">
    <!-- 模态框遮罩 -->
    <Transition name="modal-backdrop">
      <div
        v-if="isVisible"
        class="modal-backdrop"
        @click="handleBackdropClick"
      >
        <!-- 模态框内容 -->
        <Transition name="modal-content">
          <div
            v-if="isVisible"
            class="modal-container"
            :class="[
              `modal-${size}`,
              { 'modal-fullscreen': fullscreen }
            ]"
            @click.stop
          >
            <!-- 模态框头部 -->
            <div v-if="showHeader" class="modal-header">
              <div class="modal-title-section">
                <div v-if="icon" class="modal-icon">
                  <i :class="icon"></i>
                </div>
                <h2 class="modal-title">{{ title }}</h2>
              </div>
              <button
                v-if="closable"
                @click="close"
                class="modal-close-btn"
                :title="closeButtonTitle"
              >
                <i class="i-carbon-close"></i>
              </button>
            </div>

            <!-- 模态框内容区 -->
            <div class="modal-body" :class="{ 'no-padding': noPadding }">
              <slot>
                <div v-if="content" v-html="content"></div>
              </slot>
            </div>

            <!-- 模态框底部 -->
            <div v-if="showFooter" class="modal-footer">
              <slot name="footer">
                <div class="modal-actions">
                  <button
                    v-if="showCancelButton"
                    @click="handleCancel"
                    class="modal-btn modal-btn-secondary"
                  >
                    {{ cancelButtonText }}
                  </button>
                  <button
                    v-if="showConfirmButton"
                    @click="handleConfirm"
                    class="modal-btn modal-btn-primary"
                    :disabled="confirmDisabled"
                  >
                    {{ confirmButtonText }}
                  </button>
                </div>
              </slot>
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

// Props
interface Props {
  modelValue?: boolean
  title?: string
  content?: string
  icon?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  fullscreen?: boolean
  closable?: boolean
  closeOnBackdrop?: boolean
  closeOnEscape?: boolean
  showHeader?: boolean
  showFooter?: boolean
  noPadding?: boolean
  showCancelButton?: boolean
  showConfirmButton?: boolean
  cancelButtonText?: string
  confirmButtonText?: string
  confirmDisabled?: boolean
  closeButtonTitle?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '',
  content: '',
  size: 'md',
  fullscreen: false,
  closable: true,
  closeOnBackdrop: true,
  closeOnEscape: true,
  showHeader: true,
  showFooter: false,
  noPadding: false,
  showCancelButton: true,
  showConfirmButton: true,
  cancelButtonText: '取消',
  confirmButtonText: '确定',
  confirmDisabled: false,
  closeButtonTitle: '关闭'
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'close': []
  'cancel': []
  'confirm': []
  'opened': []
  'closed': []
}>()

// 内部状态
const isVisible = ref(props.modelValue)

// 计算属性
const showHeader = computed(() => props.showHeader && (props.title || props.icon || props.closable))
const showFooter = computed(() => props.showFooter || props.showCancelButton || props.showConfirmButton)

// 监听 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  if (newValue !== isVisible.value) {
    if (newValue) {
      open()
    } else {
      close()
    }
  }
})

// 方法
const open = () => {
  isVisible.value = true
  emit('update:modelValue', true)
  
  // 禁用页面滚动
  if (process.client) {
    document.body.style.overflow = 'hidden'
  }
  
  nextTick(() => {
    emit('opened')
  })
}

const close = () => {
  isVisible.value = false
  emit('update:modelValue', false)
  emit('close')
  
  // 恢复页面滚动
  if (process.client) {
    document.body.style.overflow = ''
  }
  
  nextTick(() => {
    emit('closed')
  })
}

const handleBackdropClick = () => {
  if (props.closeOnBackdrop) {
    close()
  }
}

const handleCancel = () => {
  emit('cancel')
  close()
}

const handleConfirm = () => {
  emit('confirm')
  // 不自动关闭，让父组件决定
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.closeOnEscape && isVisible.value) {
    close()
  }
}

// 生命周期
onMounted(() => {
  if (process.client) {
    document.addEventListener('keydown', handleKeydown)
  }
  
  // 如果初始值为 true，则打开模态框
  if (props.modelValue) {
    open()
  }
})

onUnmounted(() => {
  if (process.client) {
    document.removeEventListener('keydown', handleKeydown)
    // 确保恢复页面滚动
    document.body.style.overflow = ''
  }
})

// 导出方法
defineExpose({
  open,
  close,
  isVisible: computed(() => isVisible.value)
})
</script>

<style scoped>
/* 🎭 模态框遮罩 */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  z-index: var(--dashboard-z-modal-backdrop);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

/* 🎪 模态框容器 */
.modal-container {
  background: var(--quantum-bg-surface);
  border: 1px solid var(--quantum-border-color);
  border-radius: 1rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 模态框尺寸 */
.modal-sm {
  width: 100%;
  max-width: 24rem;
}

.modal-md {
  width: 100%;
  max-width: 32rem;
}

.modal-lg {
  width: 100%;
  max-width: 48rem;
}

.modal-xl {
  width: 100%;
  max-width: 64rem;
}

.modal-full {
  width: 100%;
  max-width: 90vw;
}

.modal-fullscreen {
  width: 100vw;
  height: 100vh;
  max-width: none;
  max-height: none;
  border-radius: 0;
}

/* 📋 模态框头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--quantum-border-color);
  flex-shrink: 0;
}

.modal-title-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modal-icon {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--quantum-bg-elevated);
  border-radius: 0.5rem;
  color: var(--quantum-primary);
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--quantum-fg-primary);
  margin: 0;
}

.modal-close-btn {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  border-radius: 0.5rem;
  color: var(--quantum-fg-muted);
  cursor: pointer;
  transition: all var(--dashboard-transition-fast);
}

.modal-close-btn:hover {
  background: var(--quantum-bg-elevated);
  color: var(--quantum-fg-primary);
}

/* 📄 模态框内容 */
.modal-body {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

.modal-body.no-padding {
  padding: 0;
}

/* 🎛️ 模态框底部 */
.modal-footer {
  padding: 1.5rem;
  border-top: 1px solid var(--quantum-border-color);
  flex-shrink: 0;
}

.modal-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

.modal-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--dashboard-transition-fast);
}

.modal-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.modal-btn-primary {
  background: var(--quantum-primary);
  color: white;
}

.modal-btn-primary:hover:not(:disabled) {
  background: var(--quantum-bg-elevated);
  transform: translateY(-1px);
}

.modal-btn-secondary {
  background: var(--quantum-bg-elevated);
  color: var(--quantum-fg-primary);
  border: 1px solid var(--quantum-border-color);
}

.modal-btn-secondary:hover:not(:disabled) {
  background: var(--quantum-bg-surface);
  border-color: var(--quantum-border-emphasis);
}

/* 🎬 过渡动画 */
.modal-backdrop-enter-active {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.modal-backdrop-leave-active {
  transition: all 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

.modal-backdrop-enter-from,
.modal-backdrop-leave-to {
  opacity: 0;
  backdrop-filter: blur(0px);
  -webkit-backdrop-filter: blur(0px);
}

.modal-content-enter-active {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.modal-content-leave-active {
  transition: all 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

.modal-content-enter-from {
  opacity: 0;
  transform: scale(0.9) translateY(-20px);
}

.modal-content-leave-to {
  opacity: 0;
  transform: scale(0.9) translateY(20px);
}

/* 📱 响应式 */
@media (max-width: 768px) {
  .modal-backdrop {
    padding: 0.5rem;
  }
  
  .modal-container {
    border-radius: 0.75rem;
  }
  
  .modal-sm,
  .modal-md,
  .modal-lg,
  .modal-xl,
  .modal-full {
    max-width: none;
    width: 100%;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }
  
  .modal-title {
    font-size: 1.125rem;
  }
  
  .modal-actions {
    flex-direction: column-reverse;
  }
  
  .modal-btn {
    width: 100%;
  }
}

@media (max-width: 640px) {
  .modal-backdrop {
    padding: 0;
  }
  
  .modal-container {
    border-radius: 0;
    height: 100vh;
    max-height: none;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 0.75rem;
  }
}

/* 🎭 性能优化 */
@media (prefers-reduced-motion: reduce) {
  .modal-backdrop-enter-active,
  .modal-backdrop-leave-active,
  .modal-content-enter-active,
  .modal-content-leave-active {
    transition: none !important;
  }
}
</style>
