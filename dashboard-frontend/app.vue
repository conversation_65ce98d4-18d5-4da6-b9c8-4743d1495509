<script setup lang="ts">
import { onMounted } from 'vue'
import { useGlobalTheme } from '~/composables/useTheme'

// 初始化主题系统
const { initTheme } = useGlobalTheme()

// 初始化认证状态
const authStore = useAuthStore()

// 在客户端初始化主题和认证
onMounted(() => {
  initTheme()
  authStore.initAuth()

  // 全局权限检查 - 确保只有管理员能访问Dashboard
  const route = useRoute()
  if (route.path !== '/login') {
    // 检查是否已登录且是管理员
    if (!authStore.isLoggedIn) {
      console.log('用户未登录，重定向到登录页')
      navigateTo('/login')
      return
    }

    const currentUser = authStore.currentUser
    const hasAccess = currentUser && (
      currentUser.is_superuser ||
      currentUser.roles?.some(role => role.name === 'developer' || role.name === 'admin')
    )

    if (!hasAccess) {
      console.log('用户不是管理员或开发者，拒绝访问Dashboard')
      alert('访问被拒绝：只有管理员或开发者才能访问管理后台')
      // 重定向到web-frontend
      window.location.href = 'http://localhost:3001'
      return
    }
  }
})

// 页面元数据
useHead({
  title: 'AR-System Dashboard',
  meta: [
    { name: 'description', content: 'AR System Dashboard - 量子星核管理控制台' },
    { name: 'keywords', content: 'AR, Dashboard, Management, 量子星核, 管理控制台' },
    { name: 'author', content: 'AR-System Team' },
    { property: 'og:title', content: 'AR-System Dashboard' },
    { property: 'og:description', content: 'AR System Dashboard - 量子星核管理控制台' },
    { property: 'og:type', content: 'website' },
  ],
  link: [
    { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
    { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
    { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: '' },
  ]
});

// 全局错误处理
const handleError = (error: any) => {
  console.error('Global error:', error);
  // 这里可以添加错误上报逻辑
};

// 监听未捕获的错误
if (process.client) {
  window.addEventListener('error', handleError);
  window.addEventListener('unhandledrejection', handleError);
}
</script>

<template>
  <div id="app" class="dashboard-app">
    <!-- 布局系统 -->
    <NuxtLayout>
      <!-- 页面内容 -->
      <NuxtPage />
    </NuxtLayout>
  </div>
</template>

<style>
/* 🌟 全局样式 - 不重复定义变量，使用quantum-variables.css中的变量 */

/* 🎨 Dashboard应用样式 */
.dashboard-app {
  min-height: 100vh;
  background: var(--quantum-bg-primary);
  color: var(--quantum-fg-primary);
  font-family: var(--font-sans);
  transition: background-color var(--transition-normal),
              color var(--transition-normal);
  overflow-x: hidden;
}

/* 🔄 页面过渡动画 */
.page-enter-active,
.page-leave-active {
  transition: all var(--transition-normal);
}

.page-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}
</style>
