<template>
  <div class="quantum-realtime-page">
    <!-- 🌌 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title quantum-text-neon">
          📊 QUANTUM REAL-TIME ANALYTICS
        </h1>
        <p class="page-subtitle quantum-text-glow">
          >> LIVE SYSTEM PERFORMANCE & DATA MONITORING <<
        </p>
        <div class="quantum-status-bar">
          <span class="quantum-hud-element">
            <i class="i-carbon-radio-button-checked"></i>
            LIVE: {{ isLiveMode ? 'ACTIVE' : 'PAUSED' }}
          </span>
          <span class="quantum-hud-element">
            <i class="i-carbon-time"></i>
            INTERVAL: {{ refreshInterval }}s
          </span>
          <span class="quantum-hud-element">
            <i class="i-carbon-analytics"></i>
            METRICS: {{ totalMetrics }}
          </span>
          <span class="quantum-hud-element">
            <i class="i-carbon-update-now"></i>
            UPDATED: {{ lastUpdateTime }}
          </span>
        </div>
      </div>
      <div class="header-actions">
        <button @click="toggleLiveMode" 
                :class="{ 'active': isLiveMode }"
                class="quantum-btn-secondary quantum-ripple">
          <i :class="isLiveMode ? 'i-carbon-pause' : 'i-carbon-play'"></i>
          <span>{{ isLiveMode ? '暂停监控' : '开始监控' }}</span>
        </button>
        <button @click="exportRealTimeData" class="quantum-btn-primary quantum-ripple">
          <i class="i-carbon-download"></i>
          <span>导出数据</span>
        </button>
      </div>
    </div>

    <!-- 📊 实时指标概览 -->
    <div class="quantum-metrics-grid">
      <div v-for="metric in realtimeMetrics" :key="metric.id"
           class="quantum-card-hologram quantum-metric-card">
        <div class="quantum-metric-header">
          <div class="quantum-metric-ring">
            <i :class="metric.icon" class="quantum-metric-icon"></i>
          </div>
          <div class="quantum-metric-status">
            <span class="quantum-hud-element" :class="metric.statusClass">{{ metric.status }}</span>
          </div>
        </div>
        <div class="quantum-metric-content">
          <div class="quantum-metric-value quantum-text-glow">
            {{ metric.value }}
          </div>
          <div class="quantum-metric-label">
            {{ metric.label }}
          </div>
          <div class="quantum-metric-progress">
            <div class="quantum-progress-bar">
              <div class="quantum-progress-fill" 
                   :style="{ width: `${metric.progress}%` }"
                   :class="getProgressClass(metric.progress)"></div>
            </div>
          </div>
          <div class="quantum-metric-change" :class="metric.changeType">
            <i :class="getChangeIcon(metric.changeType)"></i>
            <span>{{ Math.abs(metric.change) }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 📈 实时图表区域 -->
    <div class="realtime-layout">
      <!-- 主要监控图表 -->
      <div class="quantum-card-hologram main-charts">
        <div class="chart-header">
          <h2 class="quantum-text-neon">实时性能监控</h2>
          <div class="chart-controls">
            <select v-model="selectedTimeRange" class="quantum-select">
              <option value="1m">最近1分钟</option>
              <option value="5m">最近5分钟</option>
              <option value="15m">最近15分钟</option>
              <option value="1h">最近1小时</option>
            </select>
            <button @click="resetCharts" class="quantum-btn-ghost">
              <i class="i-carbon-reset"></i>
              重置
            </button>
          </div>
        </div>
        
        <div class="charts-grid">
          <div class="chart-item">
            <h3 class="chart-title">系统负载</h3>
            <div class="chart-container">
              <div class="chart-placeholder">
                <i class="i-carbon-chart-line text-4xl text-gray-400"></i>
                <p>实时系统负载图表</p>
                <div class="live-indicator" :class="{ 'active': isLiveMode }">
                  <span class="pulse-dot"></span>
                  LIVE
                </div>
              </div>
            </div>
          </div>
          
          <div class="chart-item">
            <h3 class="chart-title">网络流量</h3>
            <div class="chart-container">
              <div class="chart-placeholder">
                <i class="i-carbon-chart-area text-4xl text-gray-400"></i>
                <p>实时网络流量图表</p>
                <div class="live-indicator" :class="{ 'active': isLiveMode }">
                  <span class="pulse-dot"></span>
                  LIVE
                </div>
              </div>
            </div>
          </div>
          
          <div class="chart-item">
            <h3 class="chart-title">用户活动</h3>
            <div class="chart-container">
              <div class="chart-placeholder">
                <i class="i-carbon-chart-bubble text-4xl text-gray-400"></i>
                <p>实时用户活动图表</p>
                <div class="live-indicator" :class="{ 'active': isLiveMode }">
                  <span class="pulse-dot"></span>
                  LIVE
                </div>
              </div>
            </div>
          </div>
          
          <div class="chart-item">
            <h3 class="chart-title">错误率监控</h3>
            <div class="chart-container">
              <div class="chart-placeholder">
                <i class="i-carbon-chart-bar text-4xl text-gray-400"></i>
                <p>实时错误率图表</p>
                <div class="live-indicator" :class="{ 'active': isLiveMode }">
                  <span class="pulse-dot"></span>
                  LIVE
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 实时事件流 -->
      <div class="quantum-card-hologram events-stream">
        <div class="section-header">
          <h2 class="quantum-text-neon">实时事件流</h2>
          <div class="stream-controls">
            <button @click="pauseEventStream" 
                    :class="{ 'active': !eventStreamPaused }"
                    class="quantum-btn-ghost">
              <i :class="eventStreamPaused ? 'i-carbon-play' : 'i-carbon-pause'"></i>
              {{ eventStreamPaused ? '恢复' : '暂停' }}
            </button>
            <button @click="clearEventStream" class="quantum-btn-ghost">
              <i class="i-carbon-clean"></i>
              清空
            </button>
          </div>
        </div>
        
        <div class="events-container">
          <div v-for="event in realtimeEvents" :key="event.id"
               class="event-item" :class="event.type">
            <div class="event-timestamp">{{ event.timestamp }}</div>
            <div class="event-type">{{ event.type.toUpperCase() }}</div>
            <div class="event-message">{{ event.message }}</div>
            <div class="event-source">{{ event.source }}</div>
          </div>
          
          <div v-if="realtimeEvents.length === 0" class="no-events">
            <i class="i-carbon-information text-2xl"></i>
            <p>暂无实时事件</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 🔧 监控配置面板 -->
    <div class="quantum-card-hologram config-panel">
      <div class="section-header">
        <h2 class="quantum-text-neon">监控配置</h2>
        <button @click="saveConfiguration" class="quantum-btn-primary">
          <i class="i-carbon-save"></i>
          保存配置
        </button>
      </div>
      
      <div class="config-grid">
        <div class="config-group">
          <h3 class="config-title">刷新设置</h3>
          <div class="config-items">
            <div class="config-item">
              <label class="config-label">刷新间隔 (秒)</label>
              <select v-model="refreshInterval" class="quantum-select">
                <option :value="1">1秒</option>
                <option :value="5">5秒</option>
                <option :value="10">10秒</option>
                <option :value="30">30秒</option>
              </select>
            </div>
            <div class="config-item">
              <label class="config-label">数据保留时间</label>
              <select v-model="dataRetention" class="quantum-select">
                <option value="1h">1小时</option>
                <option value="6h">6小时</option>
                <option value="24h">24小时</option>
                <option value="7d">7天</option>
              </select>
            </div>
          </div>
        </div>
        
        <div class="config-group">
          <h3 class="config-title">告警设置</h3>
          <div class="config-items">
            <div class="config-item">
              <label class="config-label">CPU告警阈值 (%)</label>
              <input v-model="alertThresholds.cpu" type="number" min="0" max="100" class="quantum-input" />
            </div>
            <div class="config-item">
              <label class="config-label">内存告警阈值 (%)</label>
              <input v-model="alertThresholds.memory" type="number" min="0" max="100" class="quantum-input" />
            </div>
          </div>
        </div>
        
        <div class="config-group">
          <h3 class="config-title">显示选项</h3>
          <div class="config-items">
            <div class="config-item">
              <label class="config-checkbox">
                <input type="checkbox" v-model="displayOptions.showGrid" />
                <span class="checkmark"></span>
                显示网格线
              </label>
            </div>
            <div class="config-item">
              <label class="config-checkbox">
                <input type="checkbox" v-model="displayOptions.showLegend" />
                <span class="checkmark"></span>
                显示图例
              </label>
            </div>
            <div class="config-item">
              <label class="config-checkbox">
                <input type="checkbox" v-model="displayOptions.enableSound" />
                <span class="checkmark"></span>
                启用声音提醒
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 页面元数据
definePageMeta({
  title: 'Real-time Analytics - AR System Dashboard',
  description: 'Quantum real-time system performance and data monitoring'
})

// 响应式数据
const isLiveMode = ref(true)
const refreshInterval = ref(5)
const selectedTimeRange = ref('5m')
const eventStreamPaused = ref(false)
const dataRetention = ref('6h')

// 告警阈值配置
const alertThresholds = ref({
  cpu: 80,
  memory: 85,
  disk: 90,
  network: 1000
})

// 显示选项配置
const displayOptions = ref({
  showGrid: true,
  showLegend: true,
  enableSound: false,
  autoScale: true
})

// 实时指标数据
const realtimeMetrics = ref([
  {
    id: 1,
    label: 'System Load',
    value: '2.34',
    icon: 'i-carbon-cpu',
    status: 'NORMAL',
    statusClass: 'success',
    progress: 68,
    change: 5,
    changeType: 'positive'
  },
  {
    id: 2,
    label: 'Active Users',
    value: '1,247',
    icon: 'i-carbon-user-online',
    status: 'HIGH',
    statusClass: 'warning',
    progress: 82,
    change: 12,
    changeType: 'positive'
  },
  {
    id: 3,
    label: 'Response Time',
    value: '156ms',
    icon: 'i-carbon-time',
    status: 'FAST',
    statusClass: 'success',
    progress: 45,
    change: 8,
    changeType: 'negative'
  },
  {
    id: 4,
    label: 'Error Rate',
    value: '0.12%',
    icon: 'i-carbon-warning',
    status: 'LOW',
    statusClass: 'success',
    progress: 12,
    change: 2,
    changeType: 'negative'
  }
])

// 实时事件数据
const realtimeEvents = ref([
  {
    id: 'evt-001',
    timestamp: '14:35:22',
    type: 'info',
    message: 'User authentication successful',
    source: 'Auth Service'
  },
  {
    id: 'evt-002',
    timestamp: '14:35:18',
    type: 'warning',
    message: 'High memory usage detected',
    source: 'System Monitor'
  },
  {
    id: 'evt-003',
    timestamp: '14:35:15',
    type: 'success',
    message: 'Database backup completed',
    source: 'Backup Service'
  },
  {
    id: 'evt-004',
    timestamp: '14:35:10',
    type: 'error',
    message: 'API rate limit exceeded',
    source: 'API Gateway'
  }
])

// 计算属性
const totalMetrics = computed(() => realtimeMetrics.value.length)
const lastUpdateTime = computed(() => new Date().toLocaleTimeString())

// 方法
const getChangeIcon = (changeType: string) => {
  return changeType === 'positive' ? 'i-carbon-arrow-up' : 'i-carbon-arrow-down'
}

const getProgressClass = (progress: number) => {
  if (progress >= 90) return 'critical'
  if (progress >= 70) return 'warning'
  return 'normal'
}

const toggleLiveMode = () => {
  isLiveMode.value = !isLiveMode.value
  if (isLiveMode.value) {
    startRealTimeMonitoring()
  } else {
    stopRealTimeMonitoring()
  }
}

const pauseEventStream = () => {
  eventStreamPaused.value = !eventStreamPaused.value
}

const clearEventStream = () => {
  realtimeEvents.value = []
}

const resetCharts = () => {
  console.log('Resetting charts...')
}

const exportRealTimeData = () => {
  console.log('Exporting real-time data...')
}

const saveConfiguration = () => {
  console.log('Saving monitoring configuration...')
}

let monitoringInterval: NodeJS.Timeout | null = null

const startRealTimeMonitoring = () => {
  monitoringInterval = setInterval(() => {
    // 模拟实时数据更新
    updateMetrics()
    if (!eventStreamPaused.value) {
      addRandomEvent()
    }
  }, refreshInterval.value * 1000)
}

const stopRealTimeMonitoring = () => {
  if (monitoringInterval) {
    clearInterval(monitoringInterval)
    monitoringInterval = null
  }
}

const updateMetrics = () => {
  realtimeMetrics.value.forEach(metric => {
    // 模拟数据变化
    const change = (Math.random() - 0.5) * 10
    metric.change = Math.round(change)
    metric.changeType = change >= 0 ? 'positive' : 'negative'
    
    // 更新进度值
    metric.progress = Math.max(0, Math.min(100, metric.progress + change))
  })
}

const addRandomEvent = () => {
  const eventTypes = ['info', 'warning', 'success', 'error']
  const messages = [
    'System health check completed',
    'New user registration',
    'Cache cleared successfully',
    'Database connection timeout',
    'API response time improved',
    'Memory usage optimized'
  ]
  
  const newEvent = {
    id: `evt-${Date.now()}`,
    timestamp: new Date().toLocaleTimeString(),
    type: eventTypes[Math.floor(Math.random() * eventTypes.length)],
    message: messages[Math.floor(Math.random() * messages.length)],
    source: 'System Monitor'
  }
  
  realtimeEvents.value.unshift(newEvent)
  
  // 保持最多50个事件
  if (realtimeEvents.value.length > 50) {
    realtimeEvents.value = realtimeEvents.value.slice(0, 50)
  }
}

onMounted(() => {
  console.log('Quantum Real-time Analytics initialized')
  if (isLiveMode.value) {
    startRealTimeMonitoring()
  }
})

onUnmounted(() => {
  stopRealTimeMonitoring()
})
</script>

<style scoped>
.quantum-realtime-page {
  padding: var(--space-6);
  min-height: 100vh;
}

.quantum-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.quantum-progress-fill.critical {
  background: linear-gradient(90deg, var(--quantum-error), #ff4757);
}

.quantum-progress-fill.warning {
  background: linear-gradient(90deg, var(--quantum-warning), #ffa502);
}

.quantum-progress-fill.normal {
  background: linear-gradient(90deg, var(--quantum-primary), var(--quantum-accent));
}

.realtime-layout {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.main-charts {
  padding: var(--space-6);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
}

.chart-controls {
  display: flex;
  gap: var(--space-2);
  align-items: center;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-4);
}

.chart-item {
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  padding: var(--space-4);
  position: relative;
}

.chart-title {
  color: var(--quantum-primary);
  font-weight: 600;
  margin-bottom: var(--space-3);
  font-size: var(--text-base);
}

.chart-container {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed var(--quantum-border-color);
  border-radius: 0.25rem;
  position: relative;
}

.chart-placeholder {
  text-align: center;
  color: var(--quantum-fg-muted);
}

.live-indicator {
  position: absolute;
  top: var(--space-2);
  right: var(--space-2);
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: 0.25rem 0.5rem;
  background: var(--quantum-bg-glass);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.25rem;
  font-size: var(--text-xs);
  font-weight: 600;
  color: var(--quantum-fg-muted);
  transition: all var(--transition-normal);
}

.live-indicator.active {
  color: var(--quantum-success);
  border-color: var(--quantum-success);
  background: rgba(46, 213, 115, 0.1);
}

.pulse-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--quantum-fg-muted);
  transition: all var(--transition-normal);
}

.live-indicator.active .pulse-dot {
  background: var(--quantum-success);
  animation: quantumPulse 2s ease-in-out infinite;
}

.events-stream {
  padding: var(--space-6);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--quantum-border-color);
}

.stream-controls {
  display: flex;
  gap: var(--space-2);
}

.events-container {
  max-height: 400px;
  overflow-y: auto;
  display: grid;
  gap: var(--space-2);
}

.event-item {
  display: grid;
  grid-template-columns: auto auto 1fr auto;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--quantum-bg-elevated);
  border-radius: 0.25rem;
  border-left: 3px solid;
  font-size: var(--text-sm);
  transition: all var(--transition-fast);
}

.event-item:hover {
  background: var(--quantum-bg-surface);
}

.event-item.info {
  border-left-color: var(--quantum-primary);
}

.event-item.warning {
  border-left-color: var(--quantum-warning);
}

.event-item.success {
  border-left-color: var(--quantum-success);
}

.event-item.error {
  border-left-color: var(--quantum-error);
}

.event-timestamp {
  color: var(--quantum-fg-muted);
  font-family: var(--font-mono);
  font-size: var(--text-xs);
}

.event-type {
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
}

.event-item.info .event-type {
  background: rgba(0, 212, 255, 0.2);
  color: var(--quantum-primary);
}

.event-item.warning .event-type {
  background: rgba(255, 170, 0, 0.2);
  color: var(--quantum-warning);
}

.event-item.success .event-type {
  background: rgba(46, 213, 115, 0.2);
  color: var(--quantum-success);
}

.event-item.error .event-type {
  background: rgba(255, 71, 87, 0.2);
  color: var(--quantum-error);
}

.event-message {
  color: var(--quantum-fg-secondary);
}

.event-source {
  color: var(--quantum-fg-muted);
  font-size: var(--text-xs);
}

.no-events {
  text-align: center;
  padding: var(--space-8);
  color: var(--quantum-fg-muted);
}

.config-panel {
  padding: var(--space-6);
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
}

.config-group {
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  padding: var(--space-4);
}

.config-title {
  color: var(--quantum-primary);
  font-weight: 600;
  margin-bottom: var(--space-3);
  padding-bottom: var(--space-2);
  border-bottom: 1px solid var(--quantum-border-color);
}

.config-items {
  display: grid;
  gap: var(--space-3);
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.config-label {
  color: var(--quantum-fg-secondary);
  font-size: var(--text-sm);
  font-weight: 500;
}

.config-checkbox {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  cursor: pointer;
  font-size: var(--text-sm);
}

.config-checkbox input[type="checkbox"] {
  margin: 0;
}

.checkmark {
  position: relative;
  height: 20px;
  width: 20px;
  background-color: var(--quantum-bg-primary);
  border: 1px solid var(--quantum-border-color);
  border-radius: 3px;
}

.config-checkbox input:checked ~ .checkmark {
  background-color: var(--quantum-primary);
  border-color: var(--quantum-primary);
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.config-checkbox input:checked ~ .checkmark:after {
  display: block;
}

.config-checkbox .checkmark:after {
  left: 6px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

@media (max-width: 1024px) {
  .realtime-layout {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .config-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    gap: var(--space-3);
    align-items: stretch;
  }

  .event-item {
    grid-template-columns: 1fr;
    gap: var(--space-1);
  }

  .section-header {
    flex-direction: column;
    gap: var(--space-2);
    align-items: stretch;
  }
}
</style>
