<template>
  <div class="quantum-export-page">
    <!-- 🌌 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title quantum-text-neon">
          📤 QUANTUM DATA EXPORT CENTER
        </h1>
        <p class="page-subtitle quantum-text-glow">
          >> ADVANCED DATA EXTRACTION & EXPORT MANAGEMENT <<
        </p>
        <div class="quantum-status-bar">
          <span class="quantum-hud-element">
            <i class="i-carbon-export"></i>
            EXPORTS: {{ totalExports }}
          </span>
          <span class="quantum-hud-element">
            <i class="i-carbon-in-progress"></i>
            PROCESSING: {{ processingExports }}
          </span>
          <span class="quantum-hud-element">
            <i class="i-carbon-data-base"></i>
            VOLUME: {{ totalVolume }}
          </span>
          <span class="quantum-hud-element">
            <i class="i-carbon-time"></i>
            QUEUE: {{ queueLength }}
          </span>
        </div>
      </div>
      <div class="header-actions">
        <button @click="clearHistory" class="quantum-btn-secondary quantum-ripple">
          <i class="i-carbon-clean"></i>
          <span>清空历史</span>
        </button>
        <button @click="startBulkExport" class="quantum-btn-primary quantum-ripple">
          <i class="i-carbon-batch-job"></i>
          <span>批量导出</span>
        </button>
      </div>
    </div>

    <!-- 📊 导出统计概览 -->
    <div class="quantum-metrics-grid">
      <div v-for="metric in exportMetrics" :key="metric.id"
           class="quantum-card-hologram quantum-metric-card">
        <div class="quantum-metric-header">
          <div class="quantum-metric-ring">
            <i :class="metric.icon" class="quantum-metric-icon"></i>
          </div>
          <div class="quantum-metric-status">
            <span class="quantum-hud-element">{{ metric.status }}</span>
          </div>
        </div>
        <div class="quantum-metric-content">
          <div class="quantum-metric-value quantum-text-glow">
            {{ metric.value }}
          </div>
          <div class="quantum-metric-label">
            {{ metric.label }}
          </div>
          <div class="quantum-metric-progress">
            <div class="quantum-progress-bar">
              <div class="quantum-progress-fill" :style="{ width: `${metric.progress}%` }"></div>
            </div>
          </div>
          <div class="quantum-metric-change" :class="metric.changeType">
            <i :class="getChangeIcon(metric.changeType)"></i>
            <span>{{ Math.abs(metric.change) }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 🔧 导出配置面板 -->
    <div class="export-layout">
      <!-- 数据选择和配置 -->
      <div class="quantum-card-hologram export-config">
        <div class="section-header">
          <h2 class="quantum-text-neon">数据导出配置</h2>
          <button @click="resetConfig" class="quantum-btn-ghost">
            <i class="i-carbon-reset"></i>
            重置配置
          </button>
        </div>
        
        <div class="config-form">
          <div class="form-section">
            <h3 class="section-title">数据源选择</h3>
            <div class="data-sources-grid">
              <div v-for="source in dataSources" :key="source.id"
                   class="source-card" :class="{ 'selected': selectedSources.includes(source.id) }"
                   @click="toggleSource(source.id)">
                <div class="source-icon">
                  <i :class="source.icon" class="text-2xl"></i>
                </div>
                <div class="source-info">
                  <h4 class="source-name">{{ source.name }}</h4>
                  <p class="source-description">{{ source.description }}</p>
                  <div class="source-meta">
                    <span class="record-count">{{ source.recordCount }} 条记录</span>
                    <span class="estimated-size">{{ source.estimatedSize }}</span>
                  </div>
                </div>
                <div class="source-checkbox">
                  <i :class="selectedSources.includes(source.id) ? 'i-carbon-checkmark-filled' : 'i-carbon-add'"
                     class="checkbox-icon"></i>
                </div>
              </div>
            </div>
          </div>
          
          <div class="form-section">
            <h3 class="section-title">导出格式</h3>
            <div class="format-options">
              <button v-for="format in exportFormats" :key="format.type"
                      @click="selectedFormat = format.type"
                      :class="{ 'active': selectedFormat === format.type }"
                      class="format-btn">
                <i :class="format.icon" class="format-icon"></i>
                <div class="format-info">
                  <span class="format-name">{{ format.name }}</span>
                  <span class="format-description">{{ format.description }}</span>
                </div>
              </button>
            </div>
          </div>
          
          <div class="form-section">
            <h3 class="section-title">时间范围</h3>
            <div class="time-range-config">
              <div class="range-presets">
                <button v-for="preset in timePresets" :key="preset.value"
                        @click="selectedTimeRange = preset.value"
                        :class="{ 'active': selectedTimeRange === preset.value }"
                        class="preset-btn">
                  {{ preset.label }}
                </button>
              </div>
              
              <div v-if="selectedTimeRange === 'custom'" class="custom-range">
                <div class="date-inputs">
                  <div class="input-group">
                    <label class="input-label">开始时间</label>
                    <input v-model="customStartDate" type="datetime-local" class="quantum-input" />
                  </div>
                  <div class="input-group">
                    <label class="input-label">结束时间</label>
                    <input v-model="customEndDate" type="datetime-local" class="quantum-input" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="form-section">
            <h3 class="section-title">高级选项</h3>
            <div class="advanced-options">
              <div class="option-group">
                <label class="option-checkbox">
                  <input type="checkbox" v-model="exportOptions.includeMetadata" />
                  <span class="checkmark"></span>
                  包含元数据
                </label>
                <p class="option-description">导出文件中包含字段描述和数据类型信息</p>
              </div>
              
              <div class="option-group">
                <label class="option-checkbox">
                  <input type="checkbox" v-model="exportOptions.compressOutput" />
                  <span class="checkmark"></span>
                  压缩输出
                </label>
                <p class="option-description">使用ZIP格式压缩导出文件以减小文件大小</p>
              </div>
              
              <div class="option-group">
                <label class="option-checkbox">
                  <input type="checkbox" v-model="exportOptions.splitLargeFiles" />
                  <span class="checkmark"></span>
                  分割大文件
                </label>
                <p class="option-description">当文件超过指定大小时自动分割为多个文件</p>
              </div>
              
              <div class="option-group">
                <label class="input-label">文件命名模式</label>
                <input v-model="exportOptions.fileNamePattern" type="text" class="quantum-input" 
                       placeholder="export_{source}_{date}" />
                <p class="option-description">支持变量: {source}, {date}, {time}, {format}</p>
              </div>
            </div>
          </div>
          
          <div class="form-actions">
            <button @click="previewExport" class="quantum-btn-secondary">
              <i class="i-carbon-view"></i>
              预览数据
            </button>
            <button @click="startExport" class="quantum-btn-primary" :disabled="!canStartExport">
              <i class="i-carbon-export"></i>
              开始导出
            </button>
          </div>
        </div>
      </div>

      <!-- 导出队列和历史 -->
      <div class="export-status">
        <!-- 当前队列 -->
        <div class="quantum-card-hologram export-queue">
          <div class="section-header">
            <h2 class="quantum-text-neon">导出队列</h2>
            <span class="queue-count quantum-hud-element">{{ exportQueue.length }} 个任务</span>
          </div>
          
          <div class="queue-list">
            <div v-for="task in exportQueue" :key="task.id"
                 class="queue-item">
              <div class="task-info">
                <h4 class="task-name">{{ task.name }}</h4>
                <p class="task-description">{{ task.description }}</p>
                <div class="task-meta">
                  <span class="task-format">{{ task.format.toUpperCase() }}</span>
                  <span class="task-size">{{ task.estimatedSize }}</span>
                </div>
              </div>
              
              <div class="task-status">
                <div class="status-indicator" :class="task.status">
                  <i :class="getStatusIcon(task.status)"></i>
                </div>
                <span class="status-text">{{ getStatusText(task.status) }}</span>
              </div>
              
              <div v-if="task.status === 'processing'" class="task-progress">
                <div class="progress-bar">
                  <div class="progress-fill" :style="{ width: `${task.progress}%` }"></div>
                </div>
                <span class="progress-text">{{ task.progress }}%</span>
              </div>
              
              <div class="task-actions">
                <button v-if="task.status === 'pending'" @click="cancelTask(task)" class="quantum-btn-ghost">
                  <i class="i-carbon-close"></i>
                </button>
                <button v-if="task.status === 'completed'" @click="downloadTask(task)" class="quantum-btn-ghost">
                  <i class="i-carbon-download"></i>
                </button>
              </div>
            </div>
            
            <div v-if="exportQueue.length === 0" class="empty-queue">
              <i class="i-carbon-information text-2xl"></i>
              <p>当前没有导出任务</p>
            </div>
          </div>
        </div>

        <!-- 导出历史 -->
        <div class="quantum-card-hologram export-history">
          <div class="section-header">
            <h2 class="quantum-text-neon">导出历史</h2>
            <div class="history-filters">
              <select v-model="historyFilter" class="quantum-select">
                <option value="">所有状态</option>
                <option value="completed">已完成</option>
                <option value="failed">已失败</option>
                <option value="cancelled">已取消</option>
              </select>
            </div>
          </div>
          
          <div class="history-list">
            <div v-for="record in filteredHistory" :key="record.id"
                 class="history-item">
              <div class="record-info">
                <h4 class="record-name">{{ record.name }}</h4>
                <p class="record-time">{{ formatDateTime(record.completedAt) }}</p>
                <div class="record-meta">
                  <span class="record-format">{{ record.format.toUpperCase() }}</span>
                  <span class="record-size">{{ record.fileSize }}</span>
                </div>
              </div>
              
              <div class="record-status">
                <span class="quantum-hud-element" :class="record.statusClass">
                  {{ record.status.toUpperCase() }}
                </span>
              </div>
              
              <div class="record-actions">
                <button v-if="record.status === 'completed'" @click="downloadRecord(record)" class="quantum-btn-ghost">
                  <i class="i-carbon-download"></i>
                  下载
                </button>
                <button @click="deleteRecord(record)" class="quantum-btn-ghost text-red-400">
                  <i class="i-carbon-trash-can"></i>
                  删除
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'

// 页面元数据
definePageMeta({
  title: 'Data Export - AR System Dashboard',
  description: 'Quantum advanced data extraction and export management'
})

// API调用
const { analytics, users } = useApi()

// 响应式数据
const selectedSources = ref([])
const selectedFormat = ref('csv')
const selectedTimeRange = ref('7d')
const customStartDate = ref('')
const customEndDate = ref('')
const historyFilter = ref('')
const isLoading = ref(true)
const error = ref('')

// 导出选项
const exportOptions = ref({
  includeMetadata: true,
  compressOutput: false,
  splitLargeFiles: true,
  fileNamePattern: 'export_{source}_{date}'
})

// 导出统计指标
const exportMetrics = ref([])
const exportStats = ref(null)

// 数据源
const dataSources = ref([])

// API数据加载方法
const loadExportData = async () => {
  try {
    isLoading.value = true
    error.value = ''

    // 并行加载数据
    const [dashboardResponse, userStatsResponse, performanceResponse] = await Promise.all([
      analytics.dashboardData(),
      users.stats(),
      analytics.performanceMetrics('7d')
    ])

    // 处理仪表盘数据
    if (dashboardResponse.success) {
      const data = dashboardResponse.data

      // 更新导出统计指标
      exportMetrics.value = [
        {
          id: 1,
          label: 'Total Exports',
          value: Math.floor((data.active_sessions || 0) / 5),
          icon: 'i-carbon-export',
          status: 'ACTIVE',
          progress: 85,
          change: 12,
          changeType: 'positive'
        },
        {
          id: 2,
          label: 'Success Rate',
          value: `${(100 - (data.error_rate || 0)).toFixed(1)}%`,
          icon: 'i-carbon-checkmark',
          status: 'EXCELLENT',
          progress: 100 - (data.error_rate || 0),
          change: 2,
          changeType: 'positive'
        },
        {
          id: 3,
          label: 'Processing Time',
          value: `${(data.avg_response_time / 1000 || 2.3).toFixed(1)}min`,
          icon: 'i-carbon-time',
          status: 'FAST',
          progress: Math.max(100 - (data.avg_response_time / 10), 0),
          change: 8,
          changeType: 'negative'
        },
        {
          id: 4,
          label: 'Queue Length',
          value: Math.floor((data.system_load || 0) / 20),
          icon: 'i-carbon-in-progress',
          status: 'LOW',
          progress: Math.max(100 - (data.system_load || 0), 0),
          change: 5,
          changeType: 'negative'
        }
      ]
    }

    // 处理用户统计数据
    if (userStatsResponse.success) {
      const userData = userStatsResponse.data

      // 更新数据源信息
      dataSources.value = [
        {
          id: 'users',
          name: '用户数据',
          description: '用户账户、配置文件和活动记录',
          icon: 'i-carbon-user-multiple',
          recordCount: userData.total_users || 0,
          estimatedSize: `${((userData.total_users || 0) / 5000).toFixed(1)}MB`
        },
        {
          id: 'analytics',
          name: '分析数据',
          description: '系统性能指标和使用统计',
          icon: 'i-carbon-analytics',
          recordCount: Math.floor((userData.total_visits || 0) / 10),
          estimatedSize: `${((userData.total_visits || 0) / 10000).toFixed(1)}MB`
        },
        {
          id: 'logs',
          name: '系统日志',
          description: '应用程序和系统事件日志',
          icon: 'i-carbon-document',
          recordCount: Math.floor((userData.total_visits || 0) / 5),
          estimatedSize: `${((userData.total_visits || 0) / 2000).toFixed(1)}MB`
        },
        {
          id: 'sessions',
          name: '会话记录',
          description: '用户会话和活动记录',
          icon: 'i-carbon-user-activity',
          recordCount: userData.active_users || 0,
          estimatedSize: `${((userData.active_users || 0) / 1000).toFixed(1)}MB`
        }
      ]
    }

  } catch (err) {
    console.error('Failed to load export data:', err)
    error.value = '加载导出数据失败，请稍后重试'
  } finally {
    isLoading.value = false
  }
}

// 导出格式
const exportFormats = ref([
  {
    type: 'csv',
    name: 'CSV',
    description: '逗号分隔值文件',
    icon: 'i-carbon-document-export'
  },
  {
    type: 'json',
    name: 'JSON',
    description: 'JavaScript对象表示法',
    icon: 'i-carbon-code'
  },
  {
    type: 'excel',
    name: 'Excel',
    description: 'Microsoft Excel工作簿',
    icon: 'i-carbon-table'
  },
  {
    type: 'xml',
    name: 'XML',
    description: '可扩展标记语言',
    icon: 'i-carbon-document-xml'
  }
])

// 时间预设
const timePresets = ref([
  { value: '1h', label: '最近1小时' },
  { value: '24h', label: '最近24小时' },
  { value: '7d', label: '最近7天' },
  { value: '30d', label: '最近30天' },
  { value: 'custom', label: '自定义范围' }
])

// 导出队列
const exportQueue = ref([
  {
    id: 'exp-001',
    name: '用户数据导出',
    description: '导出最近30天的用户活动数据',
    format: 'csv',
    status: 'processing',
    progress: 65,
    estimatedSize: '2.4MB'
  },
  {
    id: 'exp-002',
    name: '系统日志导出',
    description: '导出本周的系统错误日志',
    format: 'json',
    status: 'pending',
    progress: 0,
    estimatedSize: '8.7MB'
  }
])

// 导出历史
const exportHistory = ref([
  {
    id: 'hist-001',
    name: '分析数据导出',
    format: 'excel',
    status: 'completed',
    statusClass: 'success',
    fileSize: '3.2MB',
    completedAt: new Date('2024-01-15T14:30:00')
  },
  {
    id: 'hist-002',
    name: '交易记录导出',
    format: 'csv',
    status: 'failed',
    statusClass: 'error',
    fileSize: '0MB',
    completedAt: new Date('2024-01-15T13:45:00')
  }
])

// 计算属性
const totalExports = computed(() => 1247)
const processingExports = computed(() => exportQueue.value.filter(t => t.status === 'processing').length)
const totalVolume = computed(() => '156.7GB')
const queueLength = computed(() => exportQueue.value.length)

const canStartExport = computed(() => {
  return selectedSources.value.length > 0 && selectedFormat.value
})

const filteredHistory = computed(() => {
  if (!historyFilter.value) return exportHistory.value
  return exportHistory.value.filter(record => record.status === historyFilter.value)
})

// 方法
const getChangeIcon = (changeType: string) => {
  return changeType === 'positive' ? 'i-carbon-arrow-up' : 'i-carbon-arrow-down'
}

const toggleSource = (sourceId: string) => {
  const index = selectedSources.value.indexOf(sourceId)
  if (index > -1) {
    selectedSources.value.splice(index, 1)
  } else {
    selectedSources.value.push(sourceId)
  }
}

const resetConfig = () => {
  selectedSources.value = []
  selectedFormat.value = 'csv'
  selectedTimeRange.value = '7d'
  exportOptions.value = {
    includeMetadata: true,
    compressOutput: false,
    splitLargeFiles: true,
    fileNamePattern: 'export_{source}_{date}'
  }
}

const getStatusIcon = (status: string) => {
  const icons = {
    pending: 'i-carbon-time',
    processing: 'i-carbon-in-progress',
    completed: 'i-carbon-checkmark',
    failed: 'i-carbon-error',
    cancelled: 'i-carbon-close'
  }
  return icons[status] || 'i-carbon-information'
}

const getStatusText = (status: string) => {
  const texts = {
    pending: '等待中',
    processing: '处理中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const formatDateTime = (date: Date) => {
  return date.toLocaleString()
}

const previewExport = () => {
  console.log('Previewing export data...')
}

const startExport = () => {
  console.log('Starting export with config:', {
    sources: selectedSources.value,
    format: selectedFormat.value,
    timeRange: selectedTimeRange.value,
    options: exportOptions.value
  })
}

const startBulkExport = () => {
  console.log('Starting bulk export...')
}

const clearHistory = () => {
  exportHistory.value = []
}

const cancelTask = (task: any) => {
  task.status = 'cancelled'
}

const downloadTask = (task: any) => {
  console.log('Downloading task:', task.id)
}

const downloadRecord = (record: any) => {
  console.log('Downloading record:', record.id)
}

const deleteRecord = (record: any) => {
  const index = exportHistory.value.findIndex(r => r.id === record.id)
  if (index > -1) {
    exportHistory.value.splice(index, 1)
  }
}

onMounted(() => {
  console.log('Quantum Data Export Center initialized')
  loadExportData()
})
</script>

<style scoped>
.quantum-export-page {
  padding: var(--space-6);
  min-height: 100vh;
}

.quantum-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.export-layout {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-6);
}

.export-config {
  padding: var(--space-6);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--quantum-border-color);
}

.config-form {
  display: grid;
  gap: var(--space-6);
}

.form-section {
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  padding: var(--space-4);
}

.section-title {
  color: var(--quantum-primary);
  font-weight: 600;
  margin-bottom: var(--space-3);
  padding-bottom: var(--space-2);
  border-bottom: 1px solid var(--quantum-border-color);
}

.data-sources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-3);
}

.source-card {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all var(--transition-normal);
  background: var(--quantum-bg-primary);
}

.source-card:hover {
  border-color: var(--quantum-primary);
  box-shadow: var(--quantum-glow-primary);
}

.source-card.selected {
  border-color: var(--quantum-primary);
  background: rgba(0, 212, 255, 0.1);
}

.source-icon {
  color: var(--quantum-primary);
}

.source-info {
  flex: 1;
}

.source-name {
  font-weight: 600;
  margin-bottom: var(--space-1);
}

.source-description {
  color: var(--quantum-fg-secondary);
  font-size: var(--text-sm);
  margin-bottom: var(--space-2);
}

.source-meta {
  display: flex;
  gap: var(--space-3);
  font-size: var(--text-xs);
  color: var(--quantum-fg-muted);
}

.checkbox-icon {
  color: var(--quantum-primary);
  font-size: var(--text-lg);
}

.format-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-3);
}

.format-btn {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  background: var(--quantum-bg-primary);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.format-btn:hover {
  border-color: var(--quantum-primary);
  box-shadow: var(--quantum-glow-primary);
}

.format-btn.active {
  border-color: var(--quantum-primary);
  background: rgba(0, 212, 255, 0.1);
}

.format-icon {
  color: var(--quantum-primary);
  font-size: var(--text-xl);
}

.format-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.format-name {
  font-weight: 600;
}

.format-description {
  color: var(--quantum-fg-secondary);
  font-size: var(--text-sm);
}

.time-range-config {
  display: grid;
  gap: var(--space-4);
}

.range-presets {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.preset-btn {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.25rem;
  background: var(--quantum-bg-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--text-sm);
}

.preset-btn:hover {
  border-color: var(--quantum-primary);
}

.preset-btn.active {
  border-color: var(--quantum-primary);
  background: var(--quantum-primary);
  color: white;
}

.custom-range {
  padding: var(--space-3);
  background: var(--quantum-bg-elevated);
  border-radius: 0.25rem;
}

.date-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3);
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.input-label {
  color: var(--quantum-fg-secondary);
  font-size: var(--text-sm);
  font-weight: 500;
}

.advanced-options {
  display: grid;
  gap: var(--space-4);
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.option-checkbox {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  cursor: pointer;
  font-size: var(--text-sm);
  font-weight: 500;
}

.option-checkbox input[type="checkbox"] {
  margin: 0;
}

.checkmark {
  position: relative;
  height: 20px;
  width: 20px;
  background-color: var(--quantum-bg-primary);
  border: 1px solid var(--quantum-border-color);
  border-radius: 3px;
}

.option-checkbox input:checked ~ .checkmark {
  background-color: var(--quantum-primary);
  border-color: var(--quantum-primary);
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.option-checkbox input:checked ~ .checkmark:after {
  display: block;
}

.option-checkbox .checkmark:after {
  left: 6px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.option-description {
  color: var(--quantum-fg-muted);
  font-size: var(--text-xs);
  margin-left: var(--space-6);
}

.form-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
  padding-top: var(--space-4);
  border-top: 1px solid var(--quantum-border-color);
}

.export-status {
  display: grid;
  gap: var(--space-6);
}

.export-queue,
.export-history {
  padding: var(--space-4);
}

.queue-count {
  font-size: var(--text-xs);
}

.queue-list,
.history-list {
  display: grid;
  gap: var(--space-3);
  max-height: 400px;
  overflow-y: auto;
}

.queue-item,
.history-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  transition: all var(--transition-fast);
}

.queue-item:hover,
.history-item:hover {
  border-color: var(--quantum-primary);
}

.task-info,
.record-info {
  flex: 1;
}

.task-name,
.record-name {
  font-weight: 600;
  margin-bottom: var(--space-1);
}

.task-description {
  color: var(--quantum-fg-secondary);
  font-size: var(--text-sm);
  margin-bottom: var(--space-2);
}

.record-time {
  color: var(--quantum-fg-muted);
  font-size: var(--text-sm);
  margin-bottom: var(--space-2);
}

.task-meta,
.record-meta {
  display: flex;
  gap: var(--space-2);
  font-size: var(--text-xs);
  color: var(--quantum-fg-muted);
}

.task-format,
.record-format {
  background: var(--quantum-bg-primary);
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 600;
}

.status-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-sm);
}

.status-indicator.pending {
  background: rgba(255, 170, 0, 0.2);
  color: var(--quantum-warning);
}

.status-indicator.processing {
  background: rgba(0, 212, 255, 0.2);
  color: var(--quantum-primary);
}

.status-indicator.completed {
  background: rgba(46, 213, 115, 0.2);
  color: var(--quantum-success);
}

.status-indicator.failed {
  background: rgba(255, 71, 87, 0.2);
  color: var(--quantum-error);
}

.task-progress {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
  min-width: 100px;
}

.progress-bar {
  height: 6px;
  background: var(--quantum-bg-primary);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--quantum-primary), var(--quantum-accent));
  transition: width var(--transition-normal);
}

.progress-text {
  font-size: var(--text-xs);
  color: var(--quantum-fg-muted);
  text-align: center;
}

.status-text {
  font-size: var(--text-sm);
  color: var(--quantum-fg-secondary);
}

.task-actions,
.record-actions {
  display: flex;
  gap: var(--space-1);
}

.empty-queue {
  text-align: center;
  padding: var(--space-8);
  color: var(--quantum-fg-muted);
}

.history-filters {
  display: flex;
  gap: var(--space-2);
}

@media (max-width: 1024px) {
  .export-layout {
    grid-template-columns: 1fr;
  }

  .data-sources-grid {
    grid-template-columns: 1fr;
  }

  .format-options {
    grid-template-columns: 1fr;
  }

  .date-inputs {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .range-presets {
    flex-direction: column;
  }

  .form-actions {
    flex-direction: column;
  }

  .queue-item,
  .history-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
}
</style>
