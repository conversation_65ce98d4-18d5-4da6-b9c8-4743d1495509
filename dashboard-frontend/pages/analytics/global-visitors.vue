<template>
  <div class="quantum-global-visitors-page">
    <!-- 🌍 量子全球访客分析页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title quantum-text-neon quantum-glow-effect">
          🌍 QUANTUM GLOBAL VISITORS MATRIX
        </h1>
        <p class="page-subtitle quantum-text-glow">
          >> REAL-TIME GLOBAL USER ACTIVITY & GEOGRAPHIC INTELLIGENCE <<
        </p>
        <div class="quantum-status-bar">
          <span class="quantum-hud-element">🌐 COUNTRIES: {{ totalCountries }}</span>
          <span class="quantum-hud-element">👥 VISITORS: {{ formatNumber(totalVisitors) }}</span>
          <span class="quantum-hud-element">📊 SESSIONS: {{ formatNumber(totalSessions) }}</span>
          <span class="quantum-hud-element">⚡ SYNC: {{ lastSyncTime }}</span>
        </div>
      </div>
      <div class="header-actions">
        <button @click="refreshGlobalData" class="action-btn refresh-btn">
          <i class="i-carbon-refresh"></i>
          <span>刷新数据</span>
        </button>
        <button @click="exportGlobalData" class="action-btn secondary-btn">
          <i class="i-carbon-download"></i>
          <span>导出数据</span>
        </button>
        <button @click="toggleRealtime" class="action-btn" :class="{ 'primary-btn': isRealtime }">
          <i class="i-carbon-radio-button-checked" v-if="isRealtime"></i>
          <i class="i-carbon-radio-button" v-else></i>
          <span>{{ isRealtime ? '实时模式' : '静态模式' }}</span>
        </button>
      </div>
    </div>

    <!-- 🌐 量子3D地球可视化 -->
    <div class="quantum-card-hologram quantum-globe-container mb-6">
      <div class="quantum-globe-header flex justify-between items-center mb-4">
        <h3 class="quantum-text-matrix text-xl font-bold">
          QUANTUM 3D EARTH VISUALIZATION
        </h3>
        <div class="quantum-globe-controls flex gap-3">
          <select v-model="globeView" class="quantum-select">
            <option value="visitors">访客分布</option>
            <option value="sessions">会话分布</option>
            <option value="growth">增长率</option>
          </select>
          <select v-model="timeRange" class="quantum-select">
            <option value="realtime">实时</option>
            <option value="1h">1小时</option>
            <option value="24h">24小时</option>
            <option value="7d">7天</option>
          </select>
        </div>
      </div>
      <div class="quantum-globe-wrapper">
        <SimpleQuantumGlobe 
          :visitor-data="globalVisitorData" 
          :auto-rotate="true"
          :show-stats="true"
          :height="500"
        />
      </div>
    </div>

    <!-- 📊 全球统计面板 -->
    <div class="quantum-global-stats-grid grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-6">
      <div v-for="stat in globalStats" :key="stat.id" class="quantum-card-hologram quantum-stat-card">
        <div class="stat-header flex items-center justify-between mb-4">
          <div class="stat-icon quantum-energy-ring">
            <i :class="stat.icon"></i>
          </div>
          <div class="stat-trend" :class="stat.trendType">
            <i :class="getTrendIcon(stat.trendType)"></i>
            <span>{{ Math.abs(stat.trend) }}%</span>
          </div>
        </div>
        <div class="stat-content">
          <div class="stat-value quantum-text-glow">{{ formatNumber(stat.value) }}</div>
          <div class="stat-label">{{ stat.label }}</div>
          <div class="stat-description">{{ stat.description }}</div>
        </div>
      </div>
    </div>

    <!-- 🗺️ 地区详细数据表格 -->
    <div class="quantum-card-hologram quantum-regions-table">
      <div class="table-header flex justify-between items-center mb-6">
        <h3 class="quantum-text-matrix text-xl font-bold">
          REGIONAL ANALYTICS MATRIX
        </h3>
        <div class="table-controls flex gap-3">
          <input 
            v-model="searchQuery" 
            type="text" 
            placeholder="搜索国家/地区..."
            class="quantum-input"
          >
          <select v-model="sortBy" class="quantum-select">
            <option value="visitors">按访客数排序</option>
            <option value="sessions">按会话数排序</option>
            <option value="growth">按增长率排序</option>
            <option value="country">按国家名排序</option>
          </select>
        </div>
      </div>
      
      <div class="quantum-table-container">
        <table class="quantum-table">
          <thead>
            <tr>
              <th>国家/地区</th>
              <th>访客数</th>
              <th>会话数</th>
              <th>转化率</th>
              <th>增长率</th>
              <th>状态</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="country in filteredCountries" :key="country.id" class="quantum-table-row">
              <td class="country-cell">
                <div class="flex items-center gap-3">
                  <span class="country-flag text-2xl">{{ country.flag }}</span>
                  <div>
                    <div class="country-name font-semibold">{{ country.country }}</div>
                    <div class="country-code text-sm opacity-70">{{ country.id }}</div>
                  </div>
                </div>
              </td>
              <td class="visitors-cell">
                <div class="stat-value quantum-text-glow">{{ formatNumber(country.visitors) }}</div>
              </td>
              <td class="sessions-cell">
                <div class="stat-value">{{ formatNumber(country.sessions) }}</div>
              </td>
              <td class="conversion-cell">
                <div class="conversion-rate">{{ ((country.sessions / country.visitors) * 100).toFixed(1) }}%</div>
              </td>
              <td class="growth-cell">
                <div class="growth-indicator" :class="getGrowthClass(country.growth)">
                  <i :class="getGrowthIcon(country.growth)"></i>
                  <span>{{ Math.abs(country.growth) }}%</span>
                </div>
              </td>
              <td class="status-cell">
                <span class="status-badge online">在线</span>
              </td>
              <td class="actions-cell">
                <div class="flex gap-2">
                  <button @click="viewCountryDetails(country)" class="action-btn-sm">
                    <i class="i-carbon-view"></i>
                  </button>
                  <button @click="exportCountryData(country)" class="action-btn-sm">
                    <i class="i-carbon-download"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import SimpleQuantumGlobe from '@/components/ui/SimpleQuantumGlobe.vue'

// 访客数据类型
interface VisitorData {
  id: string
  country: string
  flag: string
  lat: number
  lng: number
  visitors: number
  sessions: number
  growth: number
}

// 页面元数据
definePageMeta({
  title: 'Global Visitors - AR System Dashboard',
  description: 'Quantum global visitor analytics and geographic intelligence'
})

// 响应式数据
const globeView = ref('visitors')
const timeRange = ref('realtime')
const isRealtime = ref(true)
const searchQuery = ref('')
const sortBy = ref('visitors')
const lastSyncTime = ref('2024-01-15 14:30:25')

// 全球访客数据
const globalVisitorData = ref<VisitorData[]>([
  { id: 'US', country: '美国', flag: '🇺🇸', lat: 39.8283, lng: -98.5795, visitors: 1250000, sessions: 2100000, growth: 12.5 },
  { id: 'CN', country: '中国', flag: '🇨🇳', lat: 35.8617, lng: 104.1954, visitors: 980000, sessions: 1650000, growth: 18.3 },
  { id: 'JP', country: '日本', flag: '🇯🇵', lat: 36.2048, lng: 138.2529, visitors: 750000, sessions: 1200000, growth: 8.7 },
  { id: 'DE', country: '德国', flag: '🇩🇪', lat: 51.1657, lng: 10.4515, visitors: 680000, sessions: 1100000, growth: 15.2 },
  { id: 'GB', country: '英国', flag: '🇬🇧', lat: 55.3781, lng: -3.4360, visitors: 620000, sessions: 980000, growth: 9.8 },
  { id: 'FR', country: '法国', flag: '🇫🇷', lat: 46.2276, lng: 2.2137, visitors: 580000, sessions: 920000, growth: 11.4 },
  { id: 'KR', country: '韩国', flag: '🇰🇷', lat: 35.9078, lng: 127.7669, visitors: 520000, sessions: 850000, growth: 22.1 },
  { id: 'CA', country: '加拿大', flag: '🇨🇦', lat: 56.1304, lng: -106.3468, visitors: 480000, sessions: 780000, growth: 7.3 },
  { id: 'AU', country: '澳大利亚', flag: '🇦🇺', lat: -25.2744, lng: 133.7751, visitors: 420000, sessions: 680000, growth: 13.9 },
  { id: 'IT', country: '意大利', flag: '🇮🇹', lat: 41.8719, lng: 12.5674, visitors: 380000, sessions: 620000, growth: 6.2 }
])

// 全球统计数据
const globalStats = ref([
  {
    id: 1,
    label: 'TOTAL VISITORS',
    value: 6708000,
    description: '全球总访客数',
    icon: 'i-carbon-user-multiple',
    trend: 14.2,
    trendType: 'positive'
  },
  {
    id: 2,
    label: 'ACTIVE SESSIONS',
    value: 10780000,
    description: '活跃会话数',
    icon: 'i-carbon-activity',
    trend: 8.7,
    trendType: 'positive'
  },
  {
    id: 3,
    label: 'COUNTRIES',
    value: 156,
    description: '覆盖国家数',
    icon: 'i-carbon-earth-filled',
    trend: 2.1,
    trendType: 'positive'
  },
  {
    id: 4,
    label: 'AVG SESSION TIME',
    value: 4.2,
    description: '平均会话时长(分钟)',
    icon: 'i-carbon-time',
    trend: -1.3,
    trendType: 'negative'
  }
])

// 计算属性
const totalCountries = computed(() => globalVisitorData.value.length)
const totalVisitors = computed(() => globalVisitorData.value.reduce((sum, country) => sum + country.visitors, 0))
const totalSessions = computed(() => globalVisitorData.value.reduce((sum, country) => sum + country.sessions, 0))

const filteredCountries = computed(() => {
  let filtered = globalVisitorData.value

  // 搜索过滤
  if (searchQuery.value) {
    filtered = filtered.filter(country => 
      country.country.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      country.id.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  // 排序
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'visitors':
        return b.visitors - a.visitors
      case 'sessions':
        return b.sessions - a.sessions
      case 'growth':
        return b.growth - a.growth
      case 'country':
        return a.country.localeCompare(b.country)
      default:
        return 0
    }
  })

  return filtered
})

// 方法
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const getTrendIcon = (type: string): string => {
  return type === 'positive' ? 'i-carbon-trending-up' : 'i-carbon-trending-down'
}

const getGrowthClass = (growth: number): string => {
  return growth >= 0 ? 'positive' : 'negative'
}

const getGrowthIcon = (growth: number): string => {
  return growth >= 0 ? 'i-carbon-arrow-up' : 'i-carbon-arrow-down'
}

const refreshGlobalData = () => {
  console.log('Refreshing global visitor data...')
  lastSyncTime.value = new Date().toLocaleString('zh-CN')
}

const exportGlobalData = () => {
  console.log('Exporting global visitor data...')
}

const toggleRealtime = () => {
  isRealtime.value = !isRealtime.value
  console.log('Realtime mode:', isRealtime.value)
}

const viewCountryDetails = (country: VisitorData) => {
  console.log('Viewing details for:', country.country)
}

const exportCountryData = (country: VisitorData) => {
  console.log('Exporting data for:', country.country)
}

onMounted(() => {
  console.log('Quantum Global Visitors Matrix initialized')
})
</script>

<style scoped>
.quantum-global-visitors-page {
  padding: var(--space-6);
  min-height: 100vh;
}

.quantum-globe-container {
  position: relative;
}

.quantum-globe-wrapper {
  height: 500px;
  border-radius: 1rem;
  overflow: hidden;
}

.quantum-global-stats-grid {
  animation: quantumFadeIn 0.6s ease-out;
}

.quantum-stat-card {
  transition: all var(--transition-normal);
}

.quantum-stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--quantum-glow-primary);
}

.stat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
}

.stat-trend.positive {
  color: var(--quantum-success);
}

.stat-trend.negative {
  color: var(--quantum-error);
}

.stat-value {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--space-2);
}

.stat-label {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--quantum-fg-primary);
  margin-bottom: var(--space-1);
}

.stat-description {
  font-size: var(--text-xs);
  color: var(--quantum-fg-secondary);
}

/* 表格样式 */
.quantum-table-container {
  overflow-x: auto;
  border-radius: 0.5rem;
  border: 1px solid var(--quantum-border-color);
}

.quantum-table {
  width: 100%;
  border-collapse: collapse;
}

.quantum-table th {
  background: var(--quantum-bg-elevated);
  padding: var(--space-4);
  text-align: left;
  font-weight: var(--font-semibold);
  color: var(--quantum-fg-primary);
  border-bottom: 1px solid var(--quantum-border-color);
}

.quantum-table-row {
  transition: all var(--transition-fast);
}

.quantum-table-row:hover {
  background: var(--quantum-bg-elevated);
}

.quantum-table td {
  padding: var(--space-4);
  border-bottom: 1px solid var(--quantum-border-color);
}

.country-flag {
  font-size: 1.5rem;
}

.country-name {
  color: var(--quantum-fg-primary);
}

.country-code {
  color: var(--quantum-fg-secondary);
}

.conversion-rate {
  font-weight: var(--font-semibold);
  color: var(--quantum-primary);
}

.growth-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-weight: var(--font-semibold);
}

.growth-indicator.positive {
  color: var(--quantum-success);
}

.growth-indicator.negative {
  color: var(--quantum-error);
}

.status-badge {
  padding: var(--space-1) var(--space-2);
  border-radius: 0.25rem;
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
}

.status-badge.online {
  background: rgba(46, 213, 115, 0.2);
  color: var(--quantum-success);
  border: 1px solid var(--quantum-success);
}

.action-btn-sm {
  padding: var(--space-1);
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.25rem;
  color: var(--quantum-fg-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.action-btn-sm:hover {
  background: var(--quantum-bg-surface);
  border-color: var(--quantum-primary);
  color: var(--quantum-primary);
}

/* 输入框样式 */
.quantum-input,
.quantum-select {
  padding: var(--space-2) var(--space-3);
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  color: var(--quantum-fg-primary);
  font-size: var(--text-sm);
  transition: all var(--transition-fast);
}

.quantum-input:focus,
.quantum-select:focus {
  outline: none;
  border-color: var(--quantum-primary);
  box-shadow: var(--quantum-glow-primary);
}

/* 响应式设计 */
@media (max-width: 640px) {
  .quantum-global-visitors-page {
    padding: var(--space-4);
  }
  
  .quantum-globe-wrapper {
    height: 300px;
  }
  
  .quantum-global-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .table-header {
    flex-direction: column;
    gap: var(--space-3);
  }
  
  .table-controls {
    flex-direction: column;
    width: 100%;
  }
}

@keyframes quantumFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
