<template>
  <div class="quantum-analytics-page">
    <!-- 🌌 量子数据分析页面标题 -->
    <div class="page-header quantum-data-stream quantum-matrix-bg">
      <div class="header-content">
        <h1 class="page-title quantum-text-neon quantum-glow-effect">
          📊 QUANTUM DATA ANALYTICS CENTER
        </h1>
        <p class="page-subtitle quantum-text-glow">
          >> REAL-TIME AR SYSTEM INTELLIGENCE & INSIGHTS MATRIX <<
        </p>
        <div class="quantum-status-bar">
          <span class="quantum-hud-element">📈 METRICS: {{ totalMetrics }}</span>
          <span class="quantum-hud-element">🔄 REAL-TIME: {{ realtimeStreams }}</span>
          <span class="quantum-hud-element">📊 REPORTS: {{ activeReports }}</span>
          <span class="quantum-hud-element">⚡ SYNC: {{ lastSyncTime }}</span>
        </div>
      </div>
      <div class="header-actions">
        <button @click="refreshAnalytics" class="quantum-btn-pulse quantum-glow-effect">
          <i class="i-carbon-refresh"></i>
          <span>QUANTUM REFRESH</span>
        </button>
        <button @click="exportData" class="quantum-btn-pulse quantum-border-energy">
          <i class="i-carbon-download"></i>
          <span>EXPORT DATA</span>
        </button>
      </div>
    </div>

    <!-- 📊 量子关键指标面板 -->
    <div class="quantum-metrics-grid mb-6">
      <div v-for="metric in keyMetrics" :key="metric.id"
           class="quantum-card-hologram quantum-metric-card p-4 rounded-xl">
        <div class="quantum-metric-header flex justify-between items-start mb-4">
          <div class="quantum-energy-ring quantum-metric-ring w-12 h-12">
            <i :class="metric.icon" class="quantum-metric-icon text-lg"></i>
          </div>
          <div class="quantum-metric-status">
            <span class="quantum-hud-element text-xs px-2 py-1 rounded">{{ metric.status }}</span>
          </div>
        </div>
        <div class="quantum-metric-content text-center">
          <div class="quantum-metric-value quantum-text-glow text-2xl font-bold mb-2">
            {{ metric.value }}
          </div>
          <div class="quantum-metric-label text-sm text-[var(--quantum-fg-secondary)] mb-3">
            {{ metric.label }}
          </div>
          <div class="quantum-metric-progress mb-2">
            <div class="quantum-progress-bar h-2 rounded-full overflow-hidden">
              <div class="quantum-progress-fill h-full transition-all duration-500"
                   :style="{ width: `${metric.progress}%` }"></div>
            </div>
          </div>
          <div class="quantum-metric-change text-sm flex items-center justify-center gap-1" 
               :class="metric.changeType">
            <i :class="getChangeIcon(metric.changeType)" class="text-xs"></i>
            <span>{{ Math.abs(metric.change) }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 📈 量子图表分析区域 -->
    <div class="quantum-charts-section grid grid-cols-1 xl:grid-cols-2 gap-6 mb-6">
      <!-- 系统性能趋势 -->
      <div class="quantum-card-hologram quantum-chart-panel p-6 rounded-xl">
        <div class="quantum-chart-header quantum-border-animated flex justify-between items-center mb-6 p-4 rounded-lg">
          <h3 class="quantum-chart-title quantum-text-matrix text-lg font-bold">
            SYSTEM PERFORMANCE TRENDS
          </h3>
          <div class="quantum-chart-controls">
            <select v-model="performanceTimeRange" class="quantum-select text-sm px-3 py-2 rounded">
              <option value="1h">1H</option>
              <option value="24h">24H</option>
              <option value="7d">7D</option>
              <option value="30d">30D</option>
            </select>
          </div>
        </div>
        <div class="quantum-chart-container h-64 quantum-data-stream rounded-lg overflow-hidden">
          <div class="quantum-chart-placeholder h-full flex items-center justify-center">
            <div class="quantum-placeholder-content text-center">
              <i class="i-carbon-analytics text-5xl quantum-text-glow mb-4"></i>
              <h4 class="text-xl font-semibold quantum-text-matrix mb-2">
                PERFORMANCE ANALYTICS
              </h4>
              <p class="text-sm text-[var(--quantum-fg-secondary)]">
                Real-time system performance monitoring
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 用户活动热图 -->
      <div class="quantum-card-hologram quantum-chart-panel p-6 rounded-xl">
        <div class="quantum-chart-header quantum-border-animated flex justify-between items-center mb-6 p-4 rounded-lg">
          <h3 class="quantum-chart-title quantum-text-matrix text-lg font-bold">
            USER ACTIVITY HEATMAP
          </h3>
          <div class="quantum-chart-controls">
            <button @click="toggleHeatmapView" class="quantum-btn-pulse text-sm px-3 py-2 rounded">
              <i class="i-carbon-view mr-2"></i>
              {{ heatmapView }}
            </button>
          </div>
        </div>
        <div class="quantum-chart-container h-64 quantum-data-stream rounded-lg overflow-hidden">
          <div class="quantum-chart-placeholder h-full flex items-center justify-center">
            <div class="quantum-placeholder-content text-center">
              <i class="i-carbon-heatmap text-5xl quantum-text-glow mb-4"></i>
              <h4 class="text-xl font-semibold quantum-text-matrix mb-2">
                ACTIVITY HEATMAP
              </h4>
              <p class="text-sm text-[var(--quantum-fg-secondary)]">
                Global user activity visualization
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 🌍 量子全球分析面板 -->
    <div class="quantum-card-hologram quantum-global-analytics p-6 mb-6 rounded-xl">
      <div class="quantum-analytics-header quantum-border-animated flex justify-between items-center mb-6 p-4 rounded-lg">
        <h3 class="quantum-analytics-title quantum-text-matrix text-xl font-bold">
          GLOBAL AR NETWORK ANALYTICS
        </h3>
        <div class="quantum-analytics-controls flex gap-4">
          <select v-model="globalTimeRange" class="quantum-select">
            <option value="realtime">Real-time</option>
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
          </select>
          <button @click="toggleGlobalView" class="quantum-btn-pulse">
            <i class="i-carbon-earth-filled mr-2"></i>
            {{ globalView }}
          </button>
        </div>
      </div>
      
      <div class="quantum-global-visualization h-96 quantum-data-stream rounded-lg overflow-hidden">
        <!-- 3D地球组件 -->
        <SimpleQuantumGlobe :visitor-data="globalVisitorData" />
      </div>

      <!-- 全球访客统计 -->
      <div class="quantum-global-stats grid grid-cols-4 gap-4 mt-6">
        <div class="quantum-stat-item text-center p-4 rounded-lg bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)]">
          <div class="quantum-stat-value text-xl font-bold quantum-text-glow">{{ totalCountries }}</div>
          <div class="quantum-stat-label text-sm text-[var(--quantum-fg-secondary)]">活跃国家</div>
        </div>
        <div class="quantum-stat-item text-center p-4 rounded-lg bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)]">
          <div class="quantum-stat-value text-xl font-bold quantum-text-glow">{{ formatNumber(totalVisitors) }}</div>
          <div class="quantum-stat-label text-sm text-[var(--quantum-fg-secondary)]">总访客数</div>
        </div>
        <div class="quantum-stat-item text-center p-4 rounded-lg bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)]">
          <div class="quantum-stat-value text-xl font-bold quantum-text-glow">{{ averageGrowth }}%</div>
          <div class="quantum-stat-label text-sm text-[var(--quantum-fg-secondary)]">平均增长率</div>
        </div>
        <div class="quantum-stat-item text-center p-4 rounded-lg bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)]">
          <div class="quantum-stat-value text-xl font-bold quantum-text-glow">{{ topCountry }}</div>
          <div class="quantum-stat-label text-sm text-[var(--quantum-fg-secondary)]">最大访客源</div>
        </div>
      </div>
    </div>

    <!-- 📊 量子实时数据流 -->
    <div class="quantum-realtime-section grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 实时事件流 -->
      <div class="quantum-card-hologram quantum-events-panel p-6 rounded-xl">
        <div class="quantum-events-header flex justify-between items-center mb-6">
          <h3 class="quantum-text-matrix text-lg font-bold">
            REAL-TIME EVENT STREAM
          </h3>
          <button @click="pauseEventStream" class="quantum-btn-secondary text-sm">
            <i :class="eventStreamPaused ? 'i-carbon-play' : 'i-carbon-pause'" class="mr-2"></i>
            {{ eventStreamPaused ? 'RESUME' : 'PAUSE' }}
          </button>
        </div>
        
        <div class="quantum-events-list space-y-3 max-h-80 overflow-y-auto">
          <div v-for="event in realtimeEvents" :key="event.id"
               class="quantum-event-item p-3 rounded-lg bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-subtle)]">
            <div class="event-header flex justify-between items-start mb-2">
              <span class="event-type quantum-hud-element text-xs px-2 py-1 rounded" 
                    :class="getEventTypeClass(event.type)">
                {{ event.type.toUpperCase() }}
              </span>
              <span class="event-time text-xs text-[var(--quantum-fg-muted)]">{{ event.time }}</span>
            </div>
            <p class="event-message text-sm text-[var(--quantum-fg-secondary)]">{{ event.message }}</p>
          </div>
        </div>
      </div>

      <!-- 系统健康监控 -->
      <div class="quantum-card-hologram quantum-health-panel p-6 rounded-xl">
        <div class="quantum-health-header flex justify-between items-center mb-6">
          <h3 class="quantum-text-matrix text-lg font-bold">
            SYSTEM HEALTH MONITOR
          </h3>
          <span class="quantum-hud-element text-sm px-3 py-1 rounded quantum-status-healthy">
            OPTIMAL
          </span>
        </div>
        
        <div class="quantum-health-metrics space-y-4">
          <div v-for="health in healthMetrics" :key="health.id" class="health-metric">
            <div class="metric-header flex justify-between items-center mb-2">
              <span class="metric-name text-sm font-medium text-[var(--quantum-fg-secondary)]">
                {{ health.name }}
              </span>
              <span class="metric-value text-sm font-bold quantum-text-glow">
                {{ health.value }}{{ health.unit }}
              </span>
            </div>
            <div class="quantum-progress-bar h-2 rounded-full overflow-hidden">
              <div class="quantum-progress-fill h-full transition-all duration-500"
                   :style="{ 
                     width: `${health.percentage}%`,
                     background: getHealthColor(health.status)
                   }"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import SimpleQuantumGlobe from '@/components/ui/SimpleQuantumGlobe.vue'

// 访客数据类型
interface VisitorData {
  id: string
  country: string
  flag: string
  lat: number
  lng: number
  visitors: number
  sessions: number
  growth: number
}

// 页面元数据
definePageMeta({
  title: 'Data Analytics - AR System Dashboard',
  description: 'Quantum AR data analytics and intelligence center'
})

// API调用
const { analytics, users } = useApi()

// 分析数据状态
const performanceTimeRange = ref('24h')
const heatmapView = ref('Global')
const globalTimeRange = ref('realtime')
const globalView = ref('3D Globe')
const eventStreamPaused = ref(false)
const isLoading = ref(true)
const error = ref('')

// 关键指标数据
const keyMetrics = ref([])
const performanceData = ref([])
const businessData = ref([])
const dashboardData = ref(null)

// 实时事件数据
const realtimeEvents = ref([
  {
    id: 1,
    type: 'info',
    message: 'New AR session started from Tokyo, Japan',
    time: '14:32:15'
  },
  {
    id: 2,
    type: 'success',
    message: 'Application deployment completed successfully',
    time: '14:31:42'
  },
  {
    id: 3,
    type: 'warning',
    message: 'High CPU usage detected on server cluster 3',
    time: '14:30:18'
  },
  {
    id: 4,
    type: 'info',
    message: 'User authentication successful from New York',
    time: '14:29:55'
  }
])

// 全球访客数据
const globalVisitorData = ref<VisitorData[]>([
  { id: 'US', country: '美国', flag: '🇺🇸', lat: 39.8283, lng: -98.5795, visitors: 1250000, sessions: 2100000, growth: 12.5 },
  { id: 'CN', country: '中国', flag: '🇨🇳', lat: 35.8617, lng: 104.1954, visitors: 980000, sessions: 1650000, growth: 18.3 },
  { id: 'JP', country: '日本', flag: '🇯🇵', lat: 36.2048, lng: 138.2529, visitors: 750000, sessions: 1200000, growth: 8.7 },
  { id: 'DE', country: '德国', flag: '🇩🇪', lat: 51.1657, lng: 10.4515, visitors: 680000, sessions: 1100000, growth: 15.2 },
  { id: 'GB', country: '英国', flag: '🇬🇧', lat: 55.3781, lng: -3.4360, visitors: 620000, sessions: 980000, growth: 9.8 },
  { id: 'FR', country: '法国', flag: '🇫🇷', lat: 46.2276, lng: 2.2137, visitors: 580000, sessions: 920000, growth: 11.4 },
  { id: 'KR', country: '韩国', flag: '🇰🇷', lat: 35.9078, lng: 127.7669, visitors: 520000, sessions: 850000, growth: 22.1 },
  { id: 'CA', country: '加拿大', flag: '🇨🇦', lat: 56.1304, lng: -106.3468, visitors: 480000, sessions: 780000, growth: 7.3 },
  { id: 'AU', country: '澳大利亚', flag: '🇦🇺', lat: -25.2744, lng: 133.7751, visitors: 420000, sessions: 680000, growth: 13.6 },
  { id: 'BR', country: '巴西', flag: '🇧🇷', lat: -14.2350, lng: -51.9253, visitors: 380000, sessions: 620000, growth: 16.8 },
  { id: 'IN', country: '印度', flag: '🇮🇳', lat: 20.5937, lng: 78.9629, visitors: 350000, sessions: 580000, growth: 28.4 },
  { id: 'IT', country: '意大利', flag: '🇮🇹', lat: 41.8719, lng: 12.5674, visitors: 320000, sessions: 520000, growth: 6.9 },
  { id: 'ES', country: '西班牙', flag: '🇪🇸', lat: 40.4637, lng: -3.7492, visitors: 290000, sessions: 470000, growth: 10.2 },
  { id: 'NL', country: '荷兰', flag: '🇳🇱', lat: 52.1326, lng: 5.2913, visitors: 260000, sessions: 420000, growth: 14.7 },
  { id: 'SE', country: '瑞典', flag: '🇸🇪', lat: 60.1282, lng: 18.6435, visitors: 230000, sessions: 380000, growth: 12.3 },
  { id: 'SG', country: '新加坡', flag: '🇸🇬', lat: 1.3521, lng: 103.8198, visitors: 200000, sessions: 340000, growth: 19.5 },
  { id: 'CH', country: '瑞士', flag: '🇨🇭', lat: 46.8182, lng: 8.2275, visitors: 180000, sessions: 300000, growth: 8.1 },
  { id: 'NO', country: '挪威', flag: '🇳🇴', lat: 60.4720, lng: 8.4689, visitors: 160000, sessions: 260000, growth: 11.8 },
  { id: 'DK', country: '丹麦', flag: '🇩🇰', lat: 56.2639, lng: 9.5018, visitors: 140000, sessions: 230000, growth: 9.4 },
  { id: 'FI', country: '芬兰', flag: '🇫🇮', lat: 61.9241, lng: 25.7482, visitors: 120000, sessions: 200000, growth: 13.2 }
])

// 系统健康指标
const healthMetrics = ref([
  {
    id: 1,
    name: 'CPU Usage',
    value: 68,
    unit: '%',
    percentage: 68,
    status: 'good'
  },
  {
    id: 2,
    name: 'Memory Usage',
    value: 72,
    unit: '%',
    percentage: 72,
    status: 'good'
  },
  {
    id: 3,
    name: 'Network I/O',
    value: 45,
    unit: 'MB/s',
    percentage: 45,
    status: 'excellent'
  },
  {
    id: 4,
    name: 'Disk Usage',
    value: 34,
    unit: '%',
    percentage: 34,
    status: 'excellent'
  }
])

// 计算属性
const totalMetrics = computed(() => keyMetrics.value.length)
const realtimeStreams = computed(() => 8)
const activeReports = computed(() => 24)
const lastSyncTime = computed(() => new Date().toLocaleTimeString())

// 全球访客统计计算属性
const totalCountries = computed(() => globalVisitorData.value.length)
const totalVisitors = computed(() =>
  globalVisitorData.value.reduce((sum, country) => sum + country.visitors, 0)
)
const averageGrowth = computed(() => {
  const total = globalVisitorData.value.reduce((sum, country) => sum + country.growth, 0)
  return (total / globalVisitorData.value.length).toFixed(1)
})
const topCountry = computed(() => {
  const top = globalVisitorData.value.reduce((max, country) =>
    country.visitors > max.visitors ? country : max
  )
  return top.country
})

// API数据加载方法
const loadAnalyticsData = async () => {
  try {
    isLoading.value = true
    error.value = ''

    // 并行加载所有分析数据
    const [dashboardResponse, performanceResponse, businessResponse, userStatsResponse] = await Promise.all([
      analytics.dashboardData(),
      analytics.performanceMetrics(performanceTimeRange.value),
      analytics.businessMetrics(globalTimeRange.value),
      users.stats()
    ])

    // 处理仪表盘数据
    if (dashboardResponse.success) {
      dashboardData.value = dashboardResponse.data

      // 构建关键指标
      keyMetrics.value = [
        {
          id: 1,
          label: 'Active Sessions',
          value: formatNumber(dashboardResponse.data.active_sessions || 0),
          icon: 'i-carbon-user-activity',
          status: 'LIVE',
          progress: Math.min((dashboardResponse.data.active_sessions / 10000) * 100, 100),
          change: dashboardResponse.data.session_growth || 0,
          changeType: (dashboardResponse.data.session_growth || 0) >= 0 ? 'positive' : 'negative'
        },
        {
          id: 2,
          label: 'Data Throughput',
          value: `${(dashboardResponse.data.data_throughput || 0).toFixed(1)}GB/s`,
          icon: 'i-carbon-data-vis-1',
          status: 'OPTIMAL',
          progress: Math.min((dashboardResponse.data.data_throughput / 5) * 100, 100),
          change: dashboardResponse.data.throughput_growth || 0,
          changeType: (dashboardResponse.data.throughput_growth || 0) >= 0 ? 'positive' : 'negative'
        },
        {
          id: 3,
          label: 'Response Time',
          value: `${dashboardResponse.data.avg_response_time || 0}ms`,
          icon: 'i-carbon-time',
          status: 'FAST',
          progress: Math.max(100 - (dashboardResponse.data.avg_response_time / 10), 0),
          change: -(dashboardResponse.data.response_time_change || 0),
          changeType: (dashboardResponse.data.response_time_change || 0) <= 0 ? 'positive' : 'negative'
        },
        {
          id: 4,
          label: 'Error Rate',
          value: `${(dashboardResponse.data.error_rate || 0).toFixed(2)}%`,
          icon: 'i-carbon-warning',
          status: 'LOW',
          progress: Math.max(100 - (dashboardResponse.data.error_rate * 10), 0),
          change: -(dashboardResponse.data.error_rate_change || 0),
          changeType: (dashboardResponse.data.error_rate_change || 0) <= 0 ? 'positive' : 'negative'
        }
      ]
    }

    // 处理性能数据
    if (performanceResponse.success) {
      performanceData.value = performanceResponse.data

      // 更新健康指标
      if (performanceResponse.data.length > 0) {
        const latest = performanceResponse.data[performanceResponse.data.length - 1]
        healthMetrics.value = [
          {
            id: 1,
            name: 'CPU Usage',
            value: Math.round(latest.cpu_usage || 0),
            unit: '%',
            percentage: latest.cpu_usage || 0,
            status: latest.cpu_usage < 70 ? 'excellent' : latest.cpu_usage < 85 ? 'good' : 'warning'
          },
          {
            id: 2,
            name: 'Memory Usage',
            value: Math.round(latest.memory_usage || 0),
            unit: '%',
            percentage: latest.memory_usage || 0,
            status: latest.memory_usage < 70 ? 'excellent' : latest.memory_usage < 85 ? 'good' : 'warning'
          },
          {
            id: 3,
            name: 'Network I/O',
            value: Math.round(latest.network_io || 0),
            unit: 'MB/s',
            percentage: Math.min((latest.network_io / 100) * 100, 100),
            status: latest.network_io < 50 ? 'excellent' : latest.network_io < 80 ? 'good' : 'warning'
          },
          {
            id: 4,
            name: 'Disk Usage',
            value: Math.round(latest.disk_usage || 0),
            unit: '%',
            percentage: latest.disk_usage || 0,
            status: latest.disk_usage < 50 ? 'excellent' : latest.disk_usage < 80 ? 'good' : 'warning'
          }
        ]
      }
    }

    // 处理业务数据
    if (businessResponse.success) {
      businessData.value = businessResponse.data
    }

  } catch (err) {
    console.error('Failed to load analytics data:', err)
    error.value = '加载分析数据失败，请稍后重试'
  } finally {
    isLoading.value = false
  }
}

// 方法
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const getChangeIcon = (changeType: string) => {
  return changeType === 'positive' ? 'i-carbon-arrow-up' : 'i-carbon-arrow-down'
}

const getEventTypeClass = (type: string) => {
  const classes = {
    'info': 'quantum-event-info',
    'success': 'quantum-event-success',
    'warning': 'quantum-event-warning',
    'error': 'quantum-event-error'
  }
  return classes[type] || ''
}

const getHealthColor = (status: string) => {
  const colors = {
    'excellent': 'linear-gradient(90deg, var(--quantum-accent), var(--quantum-primary))',
    'good': 'linear-gradient(90deg, var(--quantum-primary), var(--quantum-secondary))',
    'warning': 'linear-gradient(90deg, var(--quantum-warning), var(--quantum-warning))',
    'critical': 'linear-gradient(90deg, var(--quantum-error), var(--quantum-warning))'
  }
  return colors[status] || colors.good
}

const refreshAnalytics = async () => {
  await loadAnalyticsData()
}

const exportData = () => {
  console.log('Exporting analytics data...')
  // TODO: 实现数据导出功能
}

const toggleHeatmapView = () => {
  heatmapView.value = heatmapView.value === 'Global' ? 'Regional' : 'Global'
}

const toggleGlobalView = () => {
  globalView.value = globalView.value === '3D Globe' ? '2D Map' : '3D Globe'
}

const pauseEventStream = () => {
  eventStreamPaused.value = !eventStreamPaused.value
}

// 监听时间范围变化
watch([performanceTimeRange, globalTimeRange], () => {
  loadAnalyticsData()
})

// 实时数据更新
let eventInterval: any

onMounted(() => {
  console.log('Quantum Data Analytics Center initialized')
  loadAnalyticsData()

  // 模拟实时事件流
  eventInterval = setInterval(() => {
    if (!eventStreamPaused.value) {
      const newEvent = {
        id: Date.now(),
        type: ['info', 'success', 'warning'][Math.floor(Math.random() * 3)],
        message: `System event ${Math.floor(Math.random() * 1000)} detected`,
        time: new Date().toLocaleTimeString()
      }
      realtimeEvents.value.unshift(newEvent)
      if (realtimeEvents.value.length > 10) {
        realtimeEvents.value.pop()
      }
    }
  }, 5000)
})

onUnmounted(() => {
  if (eventInterval) {
    clearInterval(eventInterval)
  }
})
</script>

<style scoped>
.quantum-analytics-page {
  padding: var(--space-6);
  min-height: 100vh;
}

.quantum-charts-section {
  animation: quantumFadeIn 0.6s ease-out;
}

.quantum-event-info {
  background: rgba(0, 255, 255, 0.2);
  border-color: var(--quantum-primary);
  color: var(--quantum-primary);
}

.quantum-event-success {
  background: rgba(0, 255, 65, 0.2);
  border-color: var(--quantum-success);
  color: var(--quantum-success);
}

.quantum-event-warning {
  background: rgba(255, 170, 0, 0.2);
  border-color: var(--quantum-warning);
  color: var(--quantum-warning);
}

.quantum-event-error {
  background: rgba(255, 71, 87, 0.2);
  border-color: var(--quantum-error);
  color: var(--quantum-error);
}

.quantum-status-healthy {
  background: rgba(0, 255, 65, 0.2);
  border-color: var(--quantum-success);
  color: var(--quantum-success);
}

@keyframes quantumFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
