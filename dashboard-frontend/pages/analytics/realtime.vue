<template>
  <div class="quantum-realtime-page">
    <!-- 🌌 量子页面标题 -->
    <div class="page-header quantum-data-stream quantum-matrix-bg">
      <div class="header-content">
        <div class="breadcrumb mb-2">
          <NuxtLink to="/analytics" class="breadcrumb-link text-[var(--quantum-fg-secondary)] hover:text-[var(--quantum-primary)]">
            数据分析
          </NuxtLink>
          <span class="breadcrumb-separator mx-2 text-[var(--quantum-fg-muted)]">/</span>
          <span class="breadcrumb-current text-[var(--quantum-fg-primary)]">实时监控</span>
        </div>
        <h1 class="page-title quantum-text-neon quantum-glow-effect">
          📊 实时监控中心
        </h1>
        <p class="page-subtitle quantum-text-glow">
          >> 量子系统实时性能监控与预警中心 <<
        </p>
        <div class="quantum-status-bar">
          <span class="quantum-hud-element">⚡ 在线: {{ onlineNodes }}</span>
          <span class="quantum-hud-element">📈 TPS: {{ currentTPS }}</span>
          <span class="quantum-hud-element">🔄 延迟: {{ avgLatency }}ms</span>
          <span class="quantum-hud-element">⚡ 同步: {{ lastSyncTime }}</span>
        </div>
      </div>
      <div class="header-actions">
        <button @click="refreshData" class="action-btn refresh-btn">
          <i class="i-carbon-refresh"></i>
          <span>刷新</span>
        </button>
        <button @click="exportReport" class="action-btn secondary-btn">
          <i class="i-carbon-document-export"></i>
          <span>导出报告</span>
        </button>
        <button @click="toggleAutoRefresh" class="action-btn" :class="autoRefresh ? 'primary-btn' : 'secondary-btn'">
          <i class="i-carbon-play" v-if="!autoRefresh"></i>
          <i class="i-carbon-pause" v-else></i>
          <span>{{ autoRefresh ? '暂停' : '开始' }}自动刷新</span>
        </button>
      </div>
    </div>

    <div class="realtime-layout grid grid-cols-1 xl:grid-cols-4 gap-6">
      <!-- 左侧：实时指标 -->
      <div class="metrics-section xl:col-span-3 space-y-6">
        <!-- 核心性能指标 -->
        <div class="quantum-card-hologram p-6 rounded-xl">
          <h2 class="section-title quantum-text-matrix text-xl font-semibold mb-4">核心性能指标</h2>
          <div class="core-metrics grid grid-cols-2 md:grid-cols-4 gap-4">
            <div 
              v-for="metric in coreMetrics" 
              :key="metric.id"
              class="metric-card quantum-card-hologram p-4 rounded-lg"
            >
              <div class="metric-header flex items-center justify-between mb-3">
                <div class="metric-icon quantum-energy-ring w-10 h-10 flex items-center justify-center">
                  <i :class="[metric.icon, getMetricColor(metric.status)]"></i>
                </div>
                <div class="metric-status">
                  <span class="status-dot w-3 h-3 rounded-full" :class="getStatusDotClass(metric.status)"></span>
                </div>
              </div>
              <div class="metric-content">
                <div class="metric-value text-2xl font-bold quantum-text-glow mb-1">{{ metric.value }}</div>
                <div class="metric-label text-sm text-[var(--quantum-fg-secondary)] mb-2">{{ metric.label }}</div>
                <div class="metric-change flex items-center gap-1 text-sm" :class="getChangeClass(metric.change)">
                  <i :class="getChangeIcon(metric.change)" class="text-xs"></i>
                  <span>{{ Math.abs(metric.change) }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 实时图表 -->
        <div class="quantum-card-hologram p-6 rounded-xl">
          <div class="chart-header flex justify-between items-center mb-6">
            <h2 class="section-title quantum-text-matrix text-xl font-semibold">实时性能趋势</h2>
            <div class="chart-controls flex gap-2">
              <select v-model="chartTimeRange" class="chart-select px-3 py-1 bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)] rounded text-sm">
                <option value="1m">1分钟</option>
                <option value="5m">5分钟</option>
                <option value="15m">15分钟</option>
                <option value="1h">1小时</option>
              </select>
            </div>
          </div>
          <div class="chart-container h-80 quantum-data-stream rounded-lg overflow-hidden">
            <div class="chart-placeholder h-full flex items-center justify-center">
              <div class="placeholder-content text-center">
                <i class="i-carbon-analytics text-5xl quantum-text-glow mb-4"></i>
                <h4 class="text-xl font-semibold quantum-text-matrix mb-2">实时性能图表</h4>
                <p class="text-sm text-[var(--quantum-fg-secondary)]">CPU、内存、网络、磁盘实时监控</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统资源监控 -->
        <div class="quantum-card-hologram p-6 rounded-xl">
          <h2 class="section-title quantum-text-matrix text-xl font-semibold mb-4">系统资源监控</h2>
          <div class="resource-monitors space-y-4">
            <div 
              v-for="resource in systemResources" 
              :key="resource.id"
              class="resource-item p-4 bg-[var(--quantum-bg-elevated)] rounded-lg"
            >
              <div class="resource-header flex justify-between items-center mb-3">
                <div class="resource-info flex items-center gap-3">
                  <i :class="resource.icon" class="text-[var(--quantum-primary)]"></i>
                  <span class="resource-name font-semibold">{{ resource.name }}</span>
                </div>
                <div class="resource-value quantum-text-glow font-bold">{{ resource.usage }}%</div>
              </div>
              <div class="resource-progress mb-2">
                <div class="progress-bar h-2 bg-[var(--quantum-bg-surface)] rounded-full overflow-hidden">
                  <div 
                    class="progress-fill h-full transition-all duration-500 rounded-full"
                    :class="getProgressClass(resource.usage)"
                    :style="{ width: `${resource.usage}%` }"
                  ></div>
                </div>
              </div>
              <div class="resource-details grid grid-cols-3 gap-4 text-sm">
                <div class="detail-item">
                  <span class="detail-label text-[var(--quantum-fg-muted)]">当前</span>
                  <span class="detail-value ml-2">{{ resource.current }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label text-[var(--quantum-fg-muted)]">峰值</span>
                  <span class="detail-value ml-2">{{ resource.peak }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label text-[var(--quantum-fg-muted)]">平均</span>
                  <span class="detail-value ml-2">{{ resource.average }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 网络流量监控 -->
        <div class="quantum-card-hologram p-6 rounded-xl">
          <h2 class="section-title quantum-text-matrix text-xl font-semibold mb-4">网络流量监控</h2>
          <div class="network-stats grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="traffic-chart">
              <h3 class="chart-title text-lg font-semibold quantum-text-glow mb-3">入站流量</h3>
              <div class="chart-container h-32 quantum-data-stream rounded-lg flex items-center justify-center">
                <div class="chart-placeholder text-center">
                  <i class="i-carbon-arrow-down text-2xl text-[var(--quantum-success)] mb-2"></i>
                  <div class="traffic-value text-xl font-bold quantum-text-glow">{{ networkStats.inbound }}</div>
                  <div class="traffic-unit text-sm text-[var(--quantum-fg-muted)]">MB/s</div>
                </div>
              </div>
            </div>
            <div class="traffic-chart">
              <h3 class="chart-title text-lg font-semibold quantum-text-glow mb-3">出站流量</h3>
              <div class="chart-container h-32 quantum-data-stream rounded-lg flex items-center justify-center">
                <div class="chart-placeholder text-center">
                  <i class="i-carbon-arrow-up text-2xl text-[var(--quantum-warning)] mb-2"></i>
                  <div class="traffic-value text-xl font-bold quantum-text-glow">{{ networkStats.outbound }}</div>
                  <div class="traffic-unit text-sm text-[var(--quantum-fg-muted)]">MB/s</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：实时状态和警报 -->
      <div class="status-section space-y-6">
        <!-- 系统状态 -->
        <div class="quantum-card-hologram p-4 rounded-xl">
          <h3 class="section-title quantum-text-matrix text-lg font-semibold mb-4">系统状态</h3>
          <div class="system-status space-y-3">
            <div 
              v-for="status in systemStatus" 
              :key="status.id"
              class="status-item flex items-center justify-between p-2 bg-[var(--quantum-bg-elevated)] rounded"
            >
              <div class="status-info flex items-center gap-2">
                <span class="status-dot w-3 h-3 rounded-full" :class="getStatusDotClass(status.status)"></span>
                <span class="status-name text-sm">{{ status.name }}</span>
              </div>
              <span class="status-value text-sm font-semibold" :class="getStatusTextClass(status.status)">
                {{ getStatusLabel(status.status) }}
              </span>
            </div>
          </div>
        </div>

        <!-- 实时警报 -->
        <div class="quantum-card-hologram p-4 rounded-xl">
          <h3 class="section-title quantum-text-matrix text-lg font-semibold mb-4">实时警报</h3>
          <div class="alerts-list space-y-3">
            <div 
              v-for="alert in realtimeAlerts" 
              :key="alert.id"
              class="alert-item p-3 rounded-lg border-l-4" 
              :class="getAlertClass(alert.level)"
            >
              <div class="alert-header flex items-center gap-2 mb-1">
                <i :class="[alert.icon, getAlertIconClass(alert.level)]"></i>
                <span class="alert-title text-sm font-semibold">{{ alert.title }}</span>
              </div>
              <div class="alert-message text-xs text-[var(--quantum-fg-secondary)] mb-2">{{ alert.message }}</div>
              <div class="alert-time text-xs text-[var(--quantum-fg-muted)]">{{ formatTime(alert.timestamp) }}</div>
            </div>
          </div>
        </div>

        <!-- 快速操作 -->
        <div class="quantum-card-hologram p-4 rounded-xl">
          <h3 class="section-title quantum-text-matrix text-lg font-semibold mb-4">快速操作</h3>
          <div class="quick-actions space-y-2">
            <button @click="clearCache" class="action-btn secondary-btn w-full justify-center text-sm">
              <i class="i-carbon-clean"></i>
              <span>清理缓存</span>
            </button>
            <button @click="restartServices" class="action-btn secondary-btn w-full justify-center text-sm">
              <i class="i-carbon-restart"></i>
              <span>重启服务</span>
            </button>
            <button @click="optimizePerformance" class="action-btn secondary-btn w-full justify-center text-sm">
              <i class="i-carbon-flash"></i>
              <span>性能优化</span>
            </button>
            <button @click="generateReport" class="action-btn primary-btn w-full justify-center text-sm">
              <i class="i-carbon-document"></i>
              <span>生成报告</span>
            </button>
          </div>
        </div>

        <!-- 监控配置 -->
        <div class="quantum-card-hologram p-4 rounded-xl">
          <h3 class="section-title quantum-text-matrix text-lg font-semibold mb-4">监控配置</h3>
          <div class="monitor-settings space-y-4">
            <div class="setting-item">
              <label class="setting-label text-sm text-[var(--quantum-fg-secondary)] mb-2 block">刷新间隔</label>
              <select v-model="refreshInterval" class="setting-select w-full px-3 py-2 bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)] rounded text-sm">
                <option value="1000">1秒</option>
                <option value="5000">5秒</option>
                <option value="10000">10秒</option>
                <option value="30000">30秒</option>
              </select>
            </div>

            <div class="setting-item">
              <label class="setting-label text-sm text-[var(--quantum-fg-secondary)] mb-2 block">警报阈值</label>
              <div class="threshold-settings space-y-2">
                <div class="threshold-item flex justify-between items-center">
                  <span class="threshold-name text-xs">CPU</span>
                  <input 
                    v-model="alertThresholds.cpu" 
                    type="number" 
                    min="0" 
                    max="100" 
                    class="threshold-input w-16 px-2 py-1 bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)] rounded text-xs"
                  />
                </div>
                <div class="threshold-item flex justify-between items-center">
                  <span class="threshold-name text-xs">内存</span>
                  <input 
                    v-model="alertThresholds.memory" 
                    type="number" 
                    min="0" 
                    max="100" 
                    class="threshold-input w-16 px-2 py-1 bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)] rounded text-xs"
                  />
                </div>
                <div class="threshold-item flex justify-between items-center">
                  <span class="threshold-name text-xs">磁盘</span>
                  <input 
                    v-model="alertThresholds.disk" 
                    type="number" 
                    min="0" 
                    max="100" 
                    class="threshold-input w-16 px-2 py-1 bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)] rounded text-xs"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

// API调用
const { analytics, devices } = useApi()

// 页面数据
const onlineNodes = ref(24)
const currentTPS = ref(1247)
const avgLatency = ref(23)
const lastSyncTime = ref(new Date().toLocaleTimeString())
const autoRefresh = ref(true)
const refreshInterval = ref(5000)
const chartTimeRange = ref('5m')
const isLoading = ref(true)
const error = ref('')

// 警报阈值
const alertThresholds = ref({
  cpu: 80,
  memory: 85,
  disk: 90
})

// 核心性能指标
const coreMetrics = ref([])
const performanceData = ref([])
const systemOverview = ref(null)

// 系统资源
const systemResources = ref([])

// 网络统计
const networkStats = ref({
  inbound: 0,
  outbound: 0
})

// 系统状态
const systemStatus = ref([])

// 实时警报
const realtimeAlerts = ref([])

// 定时器
let refreshTimer: NodeJS.Timeout | null = null

// API数据加载方法
const loadRealtimeData = async () => {
  try {
    isLoading.value = true
    error.value = ''

    // 并行加载实时数据
    const [overviewResponse, performanceResponse, deviceStatsResponse] = await Promise.all([
      analytics.overview(),
      analytics.performanceMetrics('1h'),
      devices.stats()
    ])

    // 处理系统概览数据
    if (overviewResponse.success) {
      systemOverview.value = overviewResponse.data

      // 更新页面头部状态
      onlineNodes.value = overviewResponse.data.active_nodes || 0
      currentTPS.value = overviewResponse.data.transactions_per_second || 0
      avgLatency.value = overviewResponse.data.avg_response_time || 0
      lastSyncTime.value = new Date().toLocaleTimeString()
    }

    // 处理性能数据
    if (performanceResponse.success && performanceResponse.data.length > 0) {
      performanceData.value = performanceResponse.data
      const latest = performanceResponse.data[performanceResponse.data.length - 1]

      // 构建核心性能指标
      coreMetrics.value = [
        {
          id: 1,
          label: 'CPU使用率',
          value: `${Math.round(latest.cpu_usage || 0)}%`,
          change: latest.cpu_change || 0,
          status: (latest.cpu_usage || 0) < 70 ? 'good' : (latest.cpu_usage || 0) < 85 ? 'normal' : 'warning',
          icon: 'i-carbon-chip'
        },
        {
          id: 2,
          label: '内存使用率',
          value: `${Math.round(latest.memory_usage || 0)}%`,
          change: latest.memory_change || 0,
          status: (latest.memory_usage || 0) < 70 ? 'good' : (latest.memory_usage || 0) < 85 ? 'normal' : 'warning',
          icon: 'i-carbon-data-base'
        },
        {
          id: 3,
          label: '磁盘I/O',
          value: `${Math.round(latest.disk_io || 0)}MB/s`,
          change: latest.disk_io_change || 0,
          status: (latest.disk_io || 0) < 100 ? 'good' : (latest.disk_io || 0) < 200 ? 'normal' : 'warning',
          icon: 'i-carbon-data-volume'
        },
        {
          id: 4,
          label: '网络延迟',
          value: `${Math.round(latest.network_latency || 0)}ms`,
          change: -(latest.latency_change || 0),
          status: (latest.network_latency || 0) < 50 ? 'good' : (latest.network_latency || 0) < 100 ? 'normal' : 'warning',
          icon: 'i-carbon-network-3'
        }
      ]

      // 构建系统资源数据
      systemResources.value = [
        {
          id: 1,
          name: 'CPU',
          usage: Math.round(latest.cpu_usage || 0),
          current: `${(latest.cpu_frequency || 2.4).toFixed(1)}GHz`,
          peak: `${(latest.cpu_frequency_peak || 3.2).toFixed(1)}GHz`,
          average: `${(latest.cpu_frequency_avg || 2.1).toFixed(1)}GHz`,
          icon: 'i-carbon-chip'
        },
        {
          id: 2,
          name: '内存',
          usage: Math.round(latest.memory_usage || 0),
          current: `${(latest.memory_used || 12.5).toFixed(1)}GB`,
          peak: `${(latest.memory_peak || 15.2).toFixed(1)}GB`,
          average: `${(latest.memory_avg || 10.8).toFixed(1)}GB`,
          icon: 'i-carbon-data-base'
        },
        {
          id: 3,
          name: '磁盘',
          usage: Math.round(latest.disk_usage || 0),
          current: `${Math.round(latest.disk_used || 450)}GB`,
          peak: `${Math.round(latest.disk_peak || 500)}GB`,
          average: `${Math.round(latest.disk_avg || 420)}GB`,
          icon: 'i-carbon-data-volume'
        },
        {
          id: 4,
          name: '网络',
          usage: Math.round((latest.network_io || 0) / 10), // 转换为百分比
          current: `${Math.round(latest.network_io || 125)}MB/s`,
          peak: `${Math.round(latest.network_peak || 250)}MB/s`,
          average: `${Math.round(latest.network_avg || 98)}MB/s`,
          icon: 'i-carbon-network-3'
        }
      ]

      // 更新网络统计
      networkStats.value = {
        inbound: latest.network_inbound || 0,
        outbound: latest.network_outbound || 0
      }

      // 生成系统状态
      systemStatus.value = [
        {
          id: 1,
          name: 'API服务',
          status: (latest.api_response_time || 0) < 100 ? 'good' : (latest.api_response_time || 0) < 500 ? 'normal' : 'warning'
        },
        {
          id: 2,
          name: '数据库',
          status: (latest.db_response_time || 0) < 50 ? 'good' : (latest.db_response_time || 0) < 200 ? 'normal' : 'warning'
        },
        {
          id: 3,
          name: '缓存服务',
          status: (latest.cache_hit_rate || 0) > 80 ? 'good' : (latest.cache_hit_rate || 0) > 60 ? 'normal' : 'warning'
        },
        {
          id: 4,
          name: '消息队列',
          status: (latest.queue_size || 0) < 100 ? 'good' : (latest.queue_size || 0) < 500 ? 'normal' : 'warning'
        },
        {
          id: 5,
          name: '文件存储',
          status: (latest.storage_usage || 0) < 80 ? 'good' : (latest.storage_usage || 0) < 90 ? 'normal' : 'warning'
        },
        {
          id: 6,
          name: '监控服务',
          status: 'good'
        }
      ]

      // 生成实时警报
      const alerts = []
      if ((latest.memory_usage || 0) > alertThresholds.value.memory) {
        alerts.push({
          id: 1,
          title: '内存使用率过高',
          message: `内存使用率已达到${Math.round(latest.memory_usage || 0)}%，建议检查内存泄漏`,
          level: 'warning',
          icon: 'i-carbon-warning',
          timestamp: new Date()
        })
      }

      if ((latest.cpu_usage || 0) > alertThresholds.value.cpu) {
        alerts.push({
          id: 2,
          title: 'CPU使用率过高',
          message: `CPU使用率已达到${Math.round(latest.cpu_usage || 0)}%，系统负载较高`,
          level: 'warning',
          icon: 'i-carbon-chip',
          timestamp: new Date()
        })
      }

      if ((latest.disk_usage || 0) > alertThresholds.value.disk) {
        alerts.push({
          id: 3,
          title: '磁盘空间不足',
          message: `磁盘使用率已达到${Math.round(latest.disk_usage || 0)}%，请及时清理`,
          level: 'error',
          icon: 'i-carbon-data-volume',
          timestamp: new Date()
        })
      }

      if ((latest.api_response_time || 0) > 500) {
        alerts.push({
          id: 4,
          title: 'API响应延迟',
          message: `API响应时间超过${Math.round(latest.api_response_time || 0)}ms`,
          level: 'info',
          icon: 'i-carbon-time',
          timestamp: new Date()
        })
      }

      realtimeAlerts.value = alerts
    }

  } catch (err) {
    console.error('Failed to load realtime data:', err)
    error.value = '加载实时监控数据失败，请稍后重试'
  } finally {
    isLoading.value = false
  }
}

// 方法
const getMetricColor = (status: string) => {
  const colors = {
    good: 'text-[var(--quantum-success)]',
    normal: 'text-[var(--quantum-primary)]',
    warning: 'text-[var(--quantum-warning)]',
    error: 'text-[var(--quantum-error)]'
  }
  return colors[status as keyof typeof colors] || 'text-[var(--quantum-fg-muted)]'
}

const getStatusDotClass = (status: string) => {
  const classes = {
    good: 'bg-[var(--quantum-success)]',
    normal: 'bg-[var(--quantum-primary)]',
    warning: 'bg-[var(--quantum-warning)]',
    error: 'bg-[var(--quantum-error)]'
  }
  return classes[status as keyof typeof classes] || 'bg-[var(--quantum-fg-muted)]'
}

const getChangeClass = (change: number) => {
  if (change > 0) return 'text-[var(--quantum-success)]'
  if (change < 0) return 'text-[var(--quantum-error)]'
  return 'text-[var(--quantum-fg-muted)]'
}

const getChangeIcon = (change: number) => {
  if (change > 0) return 'i-carbon-arrow-up'
  if (change < 0) return 'i-carbon-arrow-down'
  return 'i-carbon-subtract'
}

const getProgressClass = (usage: number) => {
  if (usage >= 90) return 'bg-[var(--quantum-error)]'
  if (usage >= 80) return 'bg-[var(--quantum-warning)]'
  if (usage >= 60) return 'bg-[var(--quantum-primary)]'
  return 'bg-[var(--quantum-success)]'
}

const getStatusLabel = (status: string) => {
  const labels = {
    good: '正常',
    normal: '运行中',
    warning: '警告',
    error: '错误'
  }
  return labels[status as keyof typeof labels] || status
}

const getStatusTextClass = (status: string) => {
  const classes = {
    good: 'text-[var(--quantum-success)]',
    normal: 'text-[var(--quantum-primary)]',
    warning: 'text-[var(--quantum-warning)]',
    error: 'text-[var(--quantum-error)]'
  }
  return classes[status as keyof typeof classes] || 'text-[var(--quantum-fg-muted)]'
}

const getAlertClass = (level: string) => {
  const classes = {
    info: 'bg-[var(--quantum-bg-elevated)] border-l-[var(--quantum-primary)]',
    warning: 'bg-[var(--quantum-bg-elevated)] border-l-[var(--quantum-warning)]',
    error: 'bg-[var(--quantum-bg-elevated)] border-l-[var(--quantum-error)]',
    success: 'bg-[var(--quantum-bg-elevated)] border-l-[var(--quantum-success)]'
  }
  return classes[level as keyof typeof classes] || 'bg-[var(--quantum-bg-elevated)] border-l-[var(--quantum-fg-muted)]'
}

const getAlertIconClass = (level: string) => {
  const classes = {
    info: 'text-[var(--quantum-primary)]',
    warning: 'text-[var(--quantum-warning)]',
    error: 'text-[var(--quantum-error)]',
    success: 'text-[var(--quantum-success)]'
  }
  return classes[level as keyof typeof classes] || 'text-[var(--quantum-fg-muted)]'
}

const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / (1000 * 60))

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`

  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}小时前`

  const days = Math.floor(hours / 24)
  return `${days}天前`
}

const refreshData = async () => {
  await loadRealtimeData()
}

const exportReport = () => {
  console.log('导出监控报告')
  // TODO: 实现报告导出功能
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  if (autoRefresh.value) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

const clearCache = () => {
  console.log('清理系统缓存')
  // TODO: 实现缓存清理功能
}

const restartServices = () => {
  console.log('重启系统服务')
  // TODO: 实现服务重启功能
}

const optimizePerformance = () => {
  console.log('执行性能优化')
  // TODO: 实现性能优化功能
}

const generateReport = () => {
  console.log('生成监控报告')
  // TODO: 实现报告生成功能
}

const startAutoRefresh = () => {
  if (refreshTimer) clearInterval(refreshTimer)
  refreshTimer = setInterval(async () => {
    await loadRealtimeData()
  }, refreshInterval.value)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 监听刷新间隔变化
watch(refreshInterval, () => {
  if (autoRefresh.value) {
    startAutoRefresh()
  }
})

// 监听图表时间范围变化
watch(chartTimeRange, () => {
  loadRealtimeData()
})

onMounted(() => {
  console.log('实时监控页面已加载')
  loadRealtimeData()
  if (autoRefresh.value) {
    startAutoRefresh()
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
/* 面包屑导航 */
.breadcrumb-link {
  transition: color var(--quantum-transition-fast);
}

/* 指标卡片 */
.metric-card {
  position: relative;
  overflow: hidden;
  transition: all var(--quantum-transition-fast);
  border: 1px solid var(--quantum-border-color);
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--quantum-primary), var(--quantum-accent));
  opacity: 0;
  transition: opacity var(--quantum-transition-fast);
}

.metric-card:hover::before {
  opacity: 1;
}

.metric-card:hover {
  transform: translateY(-2px);
  border-color: var(--quantum-border-glow);
  box-shadow: var(--quantum-glow-primary);
}

/* 资源项 */
.resource-item {
  transition: all var(--quantum-transition-fast);
  border-left: 3px solid transparent;
}

.resource-item:hover {
  border-left-color: var(--quantum-primary);
  transform: translateX(2px);
}

/* 进度条 */
.progress-fill {
  background: linear-gradient(90deg, var(--quantum-primary), var(--quantum-accent));
  position: relative;
  overflow: hidden;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: quantumProgressScan 2s linear infinite;
}

/* 状态项 */
.status-item {
  transition: all var(--quantum-transition-fast);
}

.status-item:hover {
  background: var(--quantum-bg-hover);
  transform: translateX(2px);
}

/* 警报项 */
.alert-item {
  transition: all var(--quantum-transition-fast);
}

.alert-item:hover {
  transform: translateX(2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 图表容器 */
.chart-container {
  position: relative;
  overflow: hidden;
}

.chart-placeholder {
  background: var(--quantum-data-stream-bg);
}

/* 量子动画 */
@keyframes quantumProgressScan {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 响应式设计 */
@media (max-width: 1280px) {
  .realtime-layout {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .core-metrics {
    grid-template-columns: repeat(2, 1fr);
  }

  .network-stats {
    grid-template-columns: 1fr;
  }

  .resource-details {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 640px) {
  .core-metrics {
    grid-template-columns: 1fr;
  }
}
</style>
