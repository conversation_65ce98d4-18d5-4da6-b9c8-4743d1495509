<template>
  <div class="user-behavior-analytics-page">
    <!-- 🌌 页面标题 -->
    <div class="page-header quantum-card-hologram">
      <div class="header-content">
        <h1 class="page-title quantum-text-neon">
          👥 USER BEHAVIOR ANALYTICS
        </h1>
        <p class="page-subtitle quantum-text-glow">
          >> QUANTUM ANALYSIS OF USER PATTERNS & BEHAVIORAL INSIGHTS <<
        </p>
      </div>
      <div class="header-actions">
        <button @click="refreshData" class="quantum-btn-pulse">
          <i class="i-carbon-refresh"></i>
          <span>⚡ REFRESH DATA</span>
        </button>
        <button @click="generateInsights" class="quantum-btn-secondary">
          <i class="i-carbon-ai-results"></i>
          <span>AI INSIGHTS</span>
        </button>
        <button @click="exportReport" class="quantum-btn-ghost">
          <i class="i-carbon-download"></i>
          <span>EXPORT</span>
        </button>
      </div>
    </div>

    <!-- 🔮 用户行为指标 -->
    <div class="quantum-metrics-grid">
      <div v-for="metric in behaviorMetrics" :key="metric.id" class="quantum-card-hologram quantum-metric-card">
        <div class="quantum-metric-header">
          <div class="quantum-energy-ring quantum-metric-ring">
            <i :class="metric.icon" class="quantum-metric-icon"></i>
          </div>
          <div class="quantum-metric-status">
            <span class="quantum-hud-element">{{ metric.status }}</span>
          </div>
        </div>
        <div class="quantum-metric-content">
          <div class="quantum-metric-value quantum-text-glow">
            {{ formatMetricValue(metric.value, metric.unit) }}
          </div>
          <div class="quantum-metric-label quantum-text-neon">{{ metric.label }}</div>
          <div class="quantum-metric-progress">
            <div class="quantum-progress-bar">
              <div class="quantum-progress-fill" :style="{ width: `${metric.progress}%` }"></div>
            </div>
          </div>
          <div class="quantum-metric-change" :class="metric.changeType">
            <i :class="getChangeIcon(metric.changeType)"></i>
            <span>{{ Math.abs(metric.change) }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 📊 用户行为分析 -->
    <div class="analytics-grid">
      <div class="quantum-card-hologram user-journey-section">
        <div class="section-header">
          <h2 class="section-title quantum-text-neon">
            <i class="i-carbon-flow"></i>
            USER JOURNEY FLOW
          </h2>
          <div class="section-controls">
            <select v-model="journeyTimeRange" class="quantum-select">
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="quarter">This Quarter</option>
            </select>
          </div>
        </div>
        <div class="journey-visualization">
          <div class="journey-canvas" ref="journeyCanvas">
            <div class="journey-placeholder quantum-text-glow">
              🌊 Quantum User Journey Flow Loading...
            </div>
          </div>
          <div class="journey-stats">
            <div class="journey-step" v-for="step in journeySteps" :key="step.id">
              <div class="step-icon">
                <i :class="step.icon"></i>
              </div>
              <div class="step-info">
                <h3 class="step-name">{{ step.name }}</h3>
                <div class="step-metrics">
                  <span class="step-users">{{ step.users }} users</span>
                  <span class="step-conversion">{{ step.conversion }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 🎯 用户画像分析 -->
      <div class="quantum-card-hologram user-personas-section">
        <div class="section-header">
          <h2 class="section-title quantum-text-neon">
            <i class="i-carbon-user-avatar"></i>
            USER PERSONAS
          </h2>
          <div class="section-controls">
            <select v-model="personaCategory" class="quantum-select">
              <option value="all">All Users</option>
              <option value="new">New Users</option>
              <option value="active">Active Users</option>
              <option value="power">Power Users</option>
            </select>
          </div>
        </div>
        <div class="personas-list">
          <div
            v-for="persona in userPersonas"
            :key="persona.id"
            class="persona-card"
          >
            <div class="persona-avatar">
              <div class="avatar-ring" :class="persona.type">
                <i :class="persona.icon"></i>
              </div>
            </div>
            <div class="persona-info">
              <h3 class="persona-name quantum-text-neon">{{ persona.name }}</h3>
              <p class="persona-description">{{ persona.description }}</p>
              <div class="persona-stats">
                <div class="stat-item">
                  <span class="stat-label">Users</span>
                  <span class="stat-value">{{ persona.userCount }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Avg Session</span>
                  <span class="stat-value">{{ persona.avgSession }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Conversion</span>
                  <span class="stat-value">{{ persona.conversion }}%</span>
                </div>
              </div>
            </div>
            <div class="persona-trend">
              <div class="trend-chart" :ref="`persona-chart-${persona.id}`">
                <div class="chart-placeholder">📈</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 🕒 用户活动时间分析 -->
    <div class="quantum-card-hologram activity-heatmap-section">
      <div class="section-header">
        <h2 class="section-title quantum-text-neon">
          <i class="i-carbon-time"></i>
          ACTIVITY HEATMAP ANALYSIS
        </h2>
        <div class="section-controls">
          <div class="heatmap-controls">
            <button
              v-for="period in heatmapPeriods"
              :key="period.value"
              @click="selectedHeatmapPeriod = period.value"
              class="period-btn"
              :class="{ active: selectedHeatmapPeriod === period.value }"
            >
              {{ period.label }}
            </button>
          </div>
        </div>
      </div>
      <div class="heatmap-visualization">
        <div class="heatmap-canvas" ref="heatmapCanvas">
          <div class="heatmap-placeholder quantum-text-glow">
            🔥 Quantum Activity Heatmap Loading...
          </div>
        </div>
        <div class="heatmap-insights">
          <div class="insight-card" v-for="insight in activityInsights" :key="insight.id">
            <div class="insight-icon">
              <i :class="insight.icon"></i>
            </div>
            <div class="insight-content">
              <h3 class="insight-title">{{ insight.title }}</h3>
              <p class="insight-description">{{ insight.description }}</p>
              <div class="insight-value quantum-text-glow">{{ insight.value }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 🎮 功能使用分析 -->
    <div class="quantum-card-hologram feature-usage-section">
      <div class="section-header">
        <h2 class="section-title quantum-text-neon">
          <i class="i-carbon-function"></i>
          FEATURE USAGE ANALYSIS
        </h2>
        <div class="section-controls">
          <select v-model="featureTimeRange" class="quantum-select">
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
        </div>
      </div>
      <div class="feature-usage-grid">
        <div
          v-for="feature in featureUsage"
          :key="feature.id"
          class="feature-card"
          :class="feature.popularityLevel"
        >
          <div class="feature-header">
            <div class="feature-icon">
              <i :class="feature.icon"></i>
            </div>
            <div class="feature-info">
              <h3 class="feature-name">{{ feature.name }}</h3>
              <p class="feature-category">{{ feature.category }}</p>
            </div>
            <div class="feature-popularity">
              <div class="popularity-indicator" :class="feature.popularityLevel">
                <span class="popularity-text">{{ feature.popularityText }}</span>
              </div>
            </div>
          </div>
          <div class="feature-metrics">
            <div class="metric-row">
              <span class="metric-label">Usage Rate</span>
              <span class="metric-value">{{ feature.usageRate }}%</span>
            </div>
            <div class="metric-row">
              <span class="metric-label">Active Users</span>
              <span class="metric-value">{{ formatNumber(feature.activeUsers) }}</span>
            </div>
            <div class="metric-row">
              <span class="metric-label">Avg Duration</span>
              <span class="metric-value">{{ feature.avgDuration }}</span>
            </div>
          </div>
          <div class="feature-trend">
            <div class="trend-indicator" :class="feature.trendType">
              <i :class="getTrendIcon(feature.trendType)"></i>
              <span>{{ feature.trendValue }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useTheme } from '~/composables/useTheme'

// 主题管理
const { currentTheme } = useTheme()

// 响应式数据
const journeyTimeRange = ref('week')
const personaCategory = ref('all')
const selectedHeatmapPeriod = ref('week')
const featureTimeRange = ref('30d')

// 用户行为指标数据
const behaviorMetrics = ref([
  {
    id: 'active-users',
    icon: 'i-carbon-user-activity',
    label: 'Active Users',
    value: 12847,
    unit: 'users',
    status: 'GROWING',
    progress: 85,
    change: 15.3,
    changeType: 'positive'
  },
  {
    id: 'session-duration',
    icon: 'i-carbon-time',
    label: 'Avg Session Duration',
    value: 24.5,
    unit: 'min',
    status: 'STABLE',
    progress: 72,
    change: 2.1,
    changeType: 'positive'
  },
  {
    id: 'bounce-rate',
    icon: 'i-carbon-exit',
    label: 'Bounce Rate',
    value: 23.8,
    unit: '%',
    status: 'IMPROVING',
    progress: 76,
    change: -5.2,
    changeType: 'negative'
  },
  {
    id: 'conversion-rate',
    icon: 'i-carbon-target',
    label: 'Conversion Rate',
    value: 4.7,
    unit: '%',
    status: 'EXCELLENT',
    progress: 94,
    change: 8.9,
    changeType: 'positive'
  }
])

// 用户旅程步骤数据
const journeySteps = ref([
  {
    id: 1,
    name: 'Landing',
    icon: 'i-carbon-login',
    users: 10000,
    conversion: 100
  },
  {
    id: 2,
    name: 'Registration',
    icon: 'i-carbon-user-plus',
    users: 7500,
    conversion: 75
  },
  {
    id: 3,
    name: 'Onboarding',
    icon: 'i-carbon-education',
    users: 6200,
    conversion: 62
  },
  {
    id: 4,
    name: 'First Action',
    icon: 'i-carbon-play',
    users: 5100,
    conversion: 51
  },
  {
    id: 5,
    name: 'Conversion',
    icon: 'i-carbon-checkmark',
    users: 470,
    conversion: 4.7
  }
])

// 用户画像数据
const userPersonas = ref([
  {
    id: 1,
    name: 'Tech Enthusiast',
    description: 'Early adopters who love exploring new AR features',
    icon: 'i-carbon-rocket',
    type: 'power',
    userCount: 3247,
    avgSession: '45min',
    conversion: 12.3
  },
  {
    id: 2,
    name: 'Casual Explorer',
    description: 'Users who occasionally use AR for entertainment',
    icon: 'i-carbon-user',
    type: 'casual',
    userCount: 8934,
    avgSession: '18min',
    conversion: 3.8
  },
  {
    id: 3,
    name: 'Professional User',
    description: 'Business users leveraging AR for work purposes',
    icon: 'i-carbon-business',
    type: 'professional',
    userCount: 1892,
    avgSession: '67min',
    conversion: 18.7
  }
])

// 热力图时间段选项
const heatmapPeriods = ref([
  { label: 'Day', value: 'day' },
  { label: 'Week', value: 'week' },
  { label: 'Month', value: 'month' }
])

// 活动洞察数据
const activityInsights = ref([
  {
    id: 1,
    icon: 'i-carbon-time',
    title: 'Peak Activity',
    description: 'Highest user activity period',
    value: '2:00 PM - 4:00 PM'
  },
  {
    id: 2,
    icon: 'i-carbon-calendar',
    title: 'Best Day',
    description: 'Most active day of the week',
    value: 'Wednesday'
  },
  {
    id: 3,
    icon: 'i-carbon-location',
    title: 'Top Region',
    description: 'Highest activity region',
    value: 'North America'
  }
])

// 功能使用数据
const featureUsage = ref([
  {
    id: 1,
    name: 'AR Object Placement',
    category: 'Core Features',
    icon: 'i-carbon-3d-curve-auto-colon',
    usageRate: 89.2,
    activeUsers: 11456,
    avgDuration: '12min',
    popularityLevel: 'high',
    popularityText: 'High',
    trendType: 'positive',
    trendValue: 15.3
  },
  {
    id: 2,
    name: 'Social Sharing',
    category: 'Social Features',
    icon: 'i-carbon-share',
    usageRate: 67.8,
    activeUsers: 8712,
    avgDuration: '5min',
    popularityLevel: 'medium',
    popularityText: 'Medium',
    trendType: 'positive',
    trendValue: 8.7
  },
  {
    id: 3,
    name: 'Voice Commands',
    category: 'Interaction',
    icon: 'i-carbon-microphone',
    usageRate: 34.5,
    activeUsers: 4432,
    avgDuration: '3min',
    popularityLevel: 'low',
    popularityText: 'Low',
    trendType: 'negative',
    trendValue: -2.1
  }
])

// 方法
const formatMetricValue = (value: number, unit: string): string => {
  if (unit === 'users') {
    return formatNumber(value)
  } else if (unit === 'min') {
    return `${value.toFixed(1)}min`
  } else if (unit === '%') {
    return `${value.toFixed(1)}%`
  }
  return `${value.toFixed(1)}${unit}`
}

const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const getChangeIcon = (type: string): string => {
  switch (type) {
    case 'positive': return 'i-carbon-trending-up'
    case 'negative': return 'i-carbon-trending-down'
    default: return 'i-carbon-trending-flat'
  }
}

const getTrendIcon = (type: string): string => {
  return getChangeIcon(type)
}

const refreshData = () => {
  console.log('Refreshing user behavior data...')
}

const generateInsights = () => {
  console.log('Generating AI insights...')
}

const exportReport = () => {
  console.log('Exporting behavior report...')
}

// 生命周期
onMounted(() => {
  console.log('Initializing user behavior analytics...')
})

// 页面元数据
definePageMeta({
  title: 'User Behavior Analytics',
  description: 'Quantum analysis of user patterns and behavioral insights'
})
</script>

<style scoped>
/* 🎨 页面基础样式 */
.user-behavior-analytics-page {
  padding: var(--space-6);
  min-height: 100vh;
  background: var(--quantum-bg-primary);
}

/* 📊 分析网格布局 */
.analytics-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

/* 🌊 用户旅程区域 */
.user-journey-section {
  display: flex;
  flex-direction: column;
}

.journey-visualization {
  display: flex;
  gap: var(--space-6);
}

.journey-canvas {
  flex: 1;
  height: 300px;
  background: var(--quantum-bg-glass);
  border: 1px solid var(--quantum-border-color);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.journey-placeholder {
  font-size: var(--text-lg);
  text-align: center;
}

.journey-stats {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  min-width: 200px;
}

.journey-step {
  display: flex;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--quantum-bg-glass);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  transition: all var(--transition-normal);
}

.journey-step:hover {
  transform: translateY(-1px);
  box-shadow: var(--quantum-glow-primary);
}

.step-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--quantum-primary);
  border-radius: 50%;
  color: white;
}

.step-info {
  flex: 1;
}

.step-name {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--quantum-fg-primary);
  margin-bottom: var(--space-1);
}

.step-metrics {
  display: flex;
  gap: var(--space-2);
  font-size: var(--text-xs);
  color: var(--quantum-fg-secondary);
}

/* 🎯 用户画像区域 */
.user-personas-section {
  display: flex;
  flex-direction: column;
}

.personas-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.persona-card {
  display: flex;
  gap: var(--space-4);
  padding: var(--space-4);
  background: var(--quantum-bg-glass);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.75rem;
  transition: all var(--transition-normal);
}

.persona-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--quantum-glow-primary);
}

.persona-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-ring {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  color: white;
}

.avatar-ring.power {
  background: var(--quantum-primary);
  box-shadow: var(--quantum-glow-primary);
}

.avatar-ring.casual {
  background: var(--quantum-secondary);
  box-shadow: var(--quantum-glow-secondary);
}

.avatar-ring.professional {
  background: var(--quantum-accent);
  box-shadow: var(--quantum-glow-accent);
}

.persona-info {
  flex: 1;
}

.persona-name {
  font-size: var(--text-base);
  font-weight: 600;
  margin-bottom: var(--space-1);
}

.persona-description {
  font-size: var(--text-sm);
  color: var(--quantum-fg-secondary);
  margin-bottom: var(--space-3);
}

.persona-stats {
  display: flex;
  gap: var(--space-4);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: var(--text-xs);
  color: var(--quantum-fg-muted);
  margin-bottom: var(--space-1);
}

.stat-value {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--quantum-fg-primary);
}

.persona-trend {
  display: flex;
  align-items: center;
}

.trend-chart {
  width: 60px;
  height: 40px;
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  font-size: var(--text-lg);
}

/* 🕒 活动热力图区域 */
.activity-heatmap-section {
  margin-bottom: var(--space-8);
}

.heatmap-controls {
  display: flex;
  gap: var(--space-2);
}

.period-btn {
  padding: var(--space-2) var(--space-3);
  background: var(--quantum-bg-glass);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  color: var(--quantum-fg-secondary);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-size: var(--text-sm);
}

.period-btn:hover,
.period-btn.active {
  background: var(--quantum-primary);
  color: white;
  box-shadow: var(--quantum-glow-primary);
}

.heatmap-visualization {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-6);
}

.heatmap-canvas {
  height: 300px;
  background: var(--quantum-bg-glass);
  border: 1px solid var(--quantum-border-color);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.heatmap-placeholder {
  font-size: var(--text-lg);
  text-align: center;
}

.heatmap-insights {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.insight-card {
  display: flex;
  gap: var(--space-3);
  padding: var(--space-4);
  background: var(--quantum-bg-glass);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.75rem;
}

.insight-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--quantum-primary);
  border-radius: 50%;
  color: white;
}

.insight-content {
  flex: 1;
}

.insight-title {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--quantum-fg-primary);
  margin-bottom: var(--space-1);
}

.insight-description {
  font-size: var(--text-sm);
  color: var(--quantum-fg-secondary);
  margin-bottom: var(--space-2);
}

.insight-value {
  font-size: var(--text-lg);
  font-weight: 700;
}

/* 🎮 功能使用区域 */
.feature-usage-section {
  margin-bottom: var(--space-8);
}

.feature-usage-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-4);
}

.feature-card {
  background: var(--quantum-bg-glass);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.75rem;
  padding: var(--space-4);
  transition: all var(--transition-normal);
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--quantum-glow-primary);
}

.feature-card.high {
  border-color: var(--quantum-success);
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.2);
}

.feature-card.medium {
  border-color: var(--quantum-warning);
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.2);
}

.feature-card.low {
  border-color: var(--quantum-error);
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.2);
}

.feature-header {
  display: flex;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.feature-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--quantum-primary);
  border-radius: 50%;
  color: white;
}

.feature-info {
  flex: 1;
}

.feature-name {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--quantum-fg-primary);
  margin-bottom: var(--space-1);
}

.feature-category {
  font-size: var(--text-sm);
  color: var(--quantum-fg-muted);
}

.feature-popularity {
  display: flex;
  align-items: center;
}

.popularity-indicator {
  padding: var(--space-1) var(--space-2);
  border-radius: 0.25rem;
  font-size: var(--text-xs);
  font-weight: 600;
}

.popularity-indicator.high {
  background: var(--quantum-success);
  color: white;
}

.popularity-indicator.medium {
  background: var(--quantum-warning);
  color: white;
}

.popularity-indicator.low {
  background: var(--quantum-error);
  color: white;
}

.feature-metrics {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.metric-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-size: var(--text-sm);
  color: var(--quantum-fg-muted);
}

.metric-value {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--quantum-fg-primary);
}

.feature-trend {
  display: flex;
  justify-content: center;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-size: var(--text-sm);
  font-weight: 600;
}

.trend-indicator.positive {
  color: var(--quantum-success);
}

.trend-indicator.negative {
  color: var(--quantum-error);
}

/* 📱 响应式设计 */
@media (max-width: 1024px) {
  .analytics-grid,
  .heatmap-visualization {
    grid-template-columns: 1fr;
  }

  .journey-visualization {
    flex-direction: column;
  }
}

@media (max-width: 640px) {
  .user-behavior-analytics-page {
    padding: var(--space-4);
  }

  .feature-usage-grid {
    grid-template-columns: 1fr;
  }

  .persona-card {
    flex-direction: column;
    text-align: center;
  }

  .persona-stats {
    justify-content: center;
  }

  .heatmap-controls {
    flex-wrap: wrap;
  }
}
</style>