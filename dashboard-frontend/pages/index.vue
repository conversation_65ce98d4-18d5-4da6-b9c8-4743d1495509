<template>
  <div class="dashboard-overview">
    <!-- 🌌 量子页面标题 -->
    <div class="page-header quantum-card-hologram">
      <div class="header-content">
        <h1 class="page-title quantum-text-neon">
          ⚡ AR-SYSTEM QUANTUM CONTROL CENTER ⚡
        </h1>
        <p class="page-subtitle quantum-text-glow">
          >> REAL-TIME QUANTUM MONITORING & NEURAL ANALYTICS <<
        </p>
        <div class="quantum-status-bar">
          <span class="quantum-hud-element">
            <i class="i-carbon-checkmark-filled"></i>
            STATUS: ONLINE
          </span>
          <span class="quantum-hud-element">
            <i class="i-carbon-time"></i>
            UPTIME: 99.9%
          </span>
          <span class="quantum-hud-element">
            <i class="i-carbon-security"></i>
            SECURITY: ACTIVE
          </span>
          <span class="quantum-hud-element">
            <i class="i-carbon-network-3"></i>
            NETWORK: STABLE
          </span>
        </div>
      </div>
      <div class="header-actions">
        <button @click="refreshData" class="quantum-btn-pulse">
          <i class="i-carbon-refresh"></i>
          <span>⚡ QUANTUM REFRESH</span>
        </button>
      </div>
    </div>

    <!-- 🔮 量子全息指标面板 -->
    <div class="quantum-metrics-grid">
      <div v-for="metric in keyMetrics" :key="metric.id" class="quantum-card-hologram quantum-metric-card">
        <div class="quantum-metric-header">
          <div class="quantum-energy-ring quantum-metric-ring">
            <i :class="metric.icon" class="quantum-metric-icon"></i>
          </div>
          <div class="quantum-metric-status">
            <span class="quantum-hud-element">{{ metric.status }}</span>
          </div>
        </div>
        <div class="quantum-metric-content">
          <div class="quantum-metric-value quantum-text-glow">
            {{ formatNumber(metric.value) }}
          </div>
          <div class="quantum-metric-label quantum-text-neon">{{ metric.label }}</div>
          <div class="quantum-metric-progress">
            <div class="quantum-progress-bar">
              <div class="quantum-progress-fill" :style="{ width: `${metric.progress}%` }"></div>
            </div>
          </div>
          <div class="quantum-metric-change" :class="metric.changeType">
            <i :class="getChangeIcon(metric.changeType)"></i>
            <span>{{ Math.abs(metric.change) }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 📊 量子数据可视化矩阵 -->
    <div class="quantum-charts-section grid grid-cols-1 xl:grid-cols-2 gap-3 sm:gap-4 md:gap-6 mb-4 md:mb-6 lg:mb-8">
      <!-- 量子系统性能监控 -->
      <div class="quantum-card-hologram quantum-chart-panel p-4 sm:p-6 rounded-xl">
        <div class="quantum-chart-header quantum-border-animated flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-4 mb-4 md:mb-6 p-3 sm:p-4 rounded-lg">
          <h3 class="quantum-chart-title quantum-text-matrix text-sm sm:text-base md:text-lg font-bold leading-tight">
            <span class="hidden md:inline">QUANTUM SYSTEM PERFORMANCE MATRIX</span>
            <span class="md:hidden">QUANTUM PERFORMANCE</span>
          </h3>
          <div class="quantum-chart-controls">
            <select v-model="performanceTimeRange" class="quantum-select text-xs sm:text-sm px-3 py-2 rounded">
              <option value="1h">1H</option>
              <option value="24h">24H</option>
              <option value="7d">7D</option>
            </select>
          </div>
        </div>
        <div class="quantum-chart-container h-40 sm:h-48 md:h-56 lg:h-64 quantum-data-stream rounded-lg overflow-hidden">
          <!-- <QuantumPerformanceChart :timeRange="performanceTimeRange" /> -->
          <div class="quantum-chart-placeholder h-full flex items-center justify-center">
            <div class="quantum-placeholder-content text-center px-2 sm:px-4">
              <i class="i-carbon-analytics text-2xl sm:text-3xl md:text-4xl lg:text-5xl quantum-text-glow mb-2 sm:mb-3 md:mb-4"></i>
              <h4 class="text-sm sm:text-base md:text-lg lg:text-xl font-semibold quantum-text-matrix mb-1 sm:mb-2 leading-tight">
                <span class="hidden sm:inline">QUANTUM PERFORMANCE MATRIX</span>
                <span class="sm:hidden">QUANTUM PERFORMANCE</span>
              </h4>
              <p class="text-xs sm:text-sm quantum-text-secondary leading-relaxed">
                <span class="hidden md:inline">Real-time quantum system performance monitoring</span>
                <span class="md:hidden">Real-time quantum monitoring</span>
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 量子用户活动分析 -->
      <div class="quantum-card-hologram quantum-chart-panel p-4 sm:p-6 rounded-xl">
        <div class="quantum-chart-header quantum-border-animated flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-4 mb-4 md:mb-6 p-3 sm:p-4 rounded-lg">
          <h3 class="quantum-chart-title quantum-text-matrix text-sm sm:text-base md:text-lg font-bold leading-tight">
            <span class="hidden md:inline">QUANTUM USER ACTIVITY ANALYSIS</span>
            <span class="md:hidden">QUANTUM USER ACTIVITY</span>
          </h3>
          <div class="quantum-chart-controls">
            <button @click="exportUserData" class="quantum-btn-pulse text-xs sm:text-sm px-3 py-2 rounded">
              <i class="i-carbon-download mr-1 sm:mr-2 text-xs sm:text-sm"></i>
              <span class="hidden sm:inline">EXPORT</span>
              <span class="sm:hidden">EXP</span>
            </button>
          </div>
        </div>
        <div class="quantum-chart-container h-40 sm:h-48 md:h-56 lg:h-64 quantum-data-stream rounded-lg overflow-hidden">
          <!-- <QuantumUserActivityChart /> -->
          <div class="quantum-chart-placeholder h-full flex items-center justify-center">
            <div class="quantum-placeholder-content text-center px-2 sm:px-4">
              <i class="i-carbon-user-activity text-2xl sm:text-3xl md:text-4xl lg:text-5xl quantum-text-glow mb-2 sm:mb-3 md:mb-4"></i>
              <h4 class="text-sm sm:text-base md:text-lg lg:text-xl font-semibold quantum-text-matrix mb-1 sm:mb-2 leading-tight">
                <span class="hidden sm:inline">QUANTUM USER ACTIVITY MATRIX</span>
                <span class="sm:hidden">QUANTUM USER ACTIVITY</span>
              </h4>
              <p class="text-xs sm:text-sm quantum-text-secondary leading-relaxed">
                <span class="hidden md:inline">Global quantum user activity visualization</span>
                <span class="md:hidden">Quantum activity visualization</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 🎛️ 控制面板区域 -->
    <div class="control-panels-section grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-3 sm:gap-4 md:gap-6 mb-4 md:mb-6 lg:mb-8">
      <!-- 设备状态 -->
      <div class="quantum-card-hologram p-4 sm:p-6 rounded-xl">
        <div class="card-header mb-4 md:mb-6 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-4">
          <h3 class="card-title text-sm sm:text-base md:text-lg font-semibold text-[var(--quantum-fg-primary)] leading-tight">
            设备状态
          </h3>
          <NuxtLink to="/devices" class="view-all-link text-xs sm:text-sm flex items-center gap-1 self-start sm:self-center">
            <span class="hidden sm:inline">查看全部</span>
            <span class="sm:hidden">全部</span>
            <i class="i-carbon-arrow-right text-xs"></i>
          </NuxtLink>
        </div>
        <div class="device-status-list space-y-2 md:space-y-3">
          <div v-for="device in deviceStatus" :key="device.id" class="device-item flex justify-between items-center p-2 sm:p-3 rounded-lg bg-[var(--quantum-bg-elevated)] hover:bg-[var(--quantum-bg-surface)] transition-colors">
            <div class="device-info flex-1 min-w-0 pr-2">
              <div class="device-name text-xs sm:text-sm font-medium text-[var(--quantum-fg-primary)] truncate leading-tight">
                {{ device.name }}
              </div>
              <div class="device-type text-xs text-[var(--quantum-fg-secondary)] truncate mt-1">
                {{ device.type }}
              </div>
            </div>
            <div class="device-status flex-shrink-0">
              <span class="dashboard-status-indicator text-xs px-2 py-1 rounded" :class="device.status">
                {{ getStatusText(device.status) }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 最新订单 -->
      <div class="quantum-card-hologram p-4 sm:p-6 rounded-xl">
        <div class="card-header mb-4 md:mb-6 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-4">
          <h3 class="card-title quantum-text-matrix text-sm sm:text-base md:text-lg font-semibold leading-tight">
            最新订单
          </h3>
          <NuxtLink to="/ecommerce/orders" class="view-all-link text-xs sm:text-sm flex items-center gap-1 self-start sm:self-center">
            <span class="hidden sm:inline">查看全部</span>
            <span class="sm:hidden">全部</span>
            <i class="i-carbon-arrow-right text-xs"></i>
          </NuxtLink>
        </div>
        <div class="orders-list space-y-2 md:space-y-3">
          <div v-for="order in recentOrders" :key="order.id" class="order-item flex justify-between items-center p-2 sm:p-3 rounded-lg bg-[var(--quantum-bg-elevated)] hover:bg-[var(--quantum-bg-surface)] transition-colors">
            <div class="order-info flex-1 min-w-0 pr-2">
              <div class="order-number text-xs sm:text-sm font-medium text-[var(--quantum-fg-primary)] truncate leading-tight">
                #{{ order.orderNumber }}
              </div>
              <div class="order-customer text-xs text-[var(--quantum-fg-secondary)] truncate mt-1">
                {{ order.customer }}
              </div>
            </div>
            <div class="order-amount text-xs sm:text-sm font-semibold quantum-text-gradient flex-shrink-0">
              <span class="hidden sm:inline">¥{{ order.amount.toLocaleString() }}</span>
              <span class="sm:hidden">¥{{ (order.amount / 1000).toFixed(1) }}K</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统通知 -->
      <div class="quantum-card-hologram p-4 sm:p-6 rounded-xl">
        <div class="card-header mb-4 md:mb-6 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-4">
          <h3 class="card-title quantum-text-matrix text-sm sm:text-base md:text-lg font-semibold leading-tight">
            系统通知
          </h3>
          <button @click="markAllNotificationsRead" class="text-xs text-[var(--quantum-primary)] hover:underline self-start sm:self-center">
            全部已读
          </button>
        </div>
        <div class="notifications-list space-y-2 md:space-y-3 max-h-64 sm:max-h-80 overflow-y-auto">
          <div v-for="notification in systemNotifications" :key="notification.id"
               class="notification-item flex items-start gap-2 md:gap-3 p-2 sm:p-3 rounded-lg bg-[var(--quantum-bg-elevated)] hover:bg-[var(--quantum-bg-surface)] transition-colors"
               :class="{ 'border-l-2 border-[var(--quantum-primary)]': !notification.read }">
            <div class="notification-icon flex-shrink-0 w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center rounded-full" :class="notification.type">
              <i :class="getNotificationIcon(notification.type)" class="text-xs"></i>
            </div>
            <div class="notification-content flex-1 min-w-0">
              <div class="notification-message text-xs sm:text-sm text-[var(--quantum-fg-primary)] leading-relaxed">
                {{ notification.message }}
              </div>
              <div class="notification-time text-xs text-[var(--quantum-fg-secondary)] mt-1">
                {{ formatTime(notification.time) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 🌍 量子全球网络监控中心 -->
    <div class="quantum-card-hologram quantum-global-network-center mb-4 md:mb-6 lg:mb-8 p-4 sm:p-6 rounded-xl">
      <div class="quantum-network-header quantum-border-animated flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 mb-4 md:mb-6 p-3 sm:p-4 rounded-lg">
        <h3 class="quantum-network-title quantum-text-matrix text-sm sm:text-base md:text-lg lg:text-xl font-bold leading-tight">
          <span class="hidden lg:inline">QUANTUM GLOBAL NETWORK MONITORING CENTER</span>
          <span class="lg:hidden">QUANTUM GLOBAL NETWORK CENTER</span>
        </h3>
        <div class="quantum-network-controls flex flex-wrap items-center gap-1 sm:gap-2 md:gap-4">
          <span class="quantum-hud-element text-xs px-2 py-1 rounded">SAT: 24</span>
          <span class="quantum-hud-element text-xs px-2 py-1 rounded">NODES: 156</span>
          <NuxtLink to="/analytics/global-visitors" class="quantum-btn-pulse text-xs sm:text-sm px-3 py-2 rounded">
            <i class="i-carbon-earth-filled mr-1 sm:mr-2 text-xs sm:text-sm"></i>
            <span class="hidden sm:inline">QUANTUM ANALYSIS</span>
            <span class="sm:hidden">QUANTUM</span>
          </NuxtLink>
        </div>
      </div>
      <div class="quantum-network-visualization-container h-48 sm:h-64 md:h-80 lg:h-96 quantum-data-stream rounded-lg overflow-hidden">
        <!-- <QuantumGlobeVisualization /> -->
        <div class="quantum-globe-placeholder h-full flex items-center justify-center">
          <div class="quantum-placeholder-content text-center px-2 sm:px-4">
            <i class="i-carbon-earth-filled text-3xl sm:text-4xl md:text-5xl lg:text-6xl quantum-text-glow mb-2 sm:mb-3 md:mb-4"></i>
            <h4 class="text-base sm:text-lg md:text-xl lg:text-2xl font-semibold quantum-text-matrix mb-1 sm:mb-2 leading-tight">
              <span class="hidden md:inline">QUANTUM GLOBAL NETWORK MATRIX</span>
              <span class="md:hidden">QUANTUM NETWORK MATRIX</span>
            </h4>
            <p class="text-xs sm:text-sm quantum-text-secondary mb-3 sm:mb-4 leading-relaxed">
              <span class="hidden lg:inline">3D Quantum Earth visualization with real-time data streams</span>
              <span class="lg:hidden">Real-time quantum global visualization</span>
            </p>
            <div class="quantum-global-stats grid grid-cols-2 sm:grid-cols-4 gap-1 sm:gap-2 md:gap-4 max-w-xs sm:max-w-sm md:max-w-md mx-auto">
              <div class="quantum-stat-item text-center p-1 sm:p-2 rounded-lg quantum-bg-elevated">
                <div class="quantum-stat-value text-xs sm:text-sm md:text-lg font-bold quantum-text-glow">24</div>
                <div class="quantum-stat-label text-xs quantum-text-secondary">SAT</div>
              </div>
              <div class="quantum-stat-item text-center p-1 sm:p-2 rounded-lg quantum-bg-elevated">
                <div class="quantum-stat-value text-xs sm:text-sm md:text-lg font-bold quantum-text-glow">156</div>
                <div class="quantum-stat-label text-xs quantum-text-secondary">NODES</div>
              </div>
              <div class="quantum-stat-item text-center p-1 sm:p-2 rounded-lg quantum-bg-elevated">
                <div class="quantum-stat-value text-xs sm:text-sm md:text-lg font-bold quantum-text-glow">2.4K</div>
                <div class="quantum-stat-label text-xs quantum-text-secondary">USERS</div>
              </div>
              <div class="quantum-stat-item text-center p-1 sm:p-2 rounded-lg quantum-bg-elevated">
                <div class="quantum-stat-value text-xs sm:text-sm md:text-lg font-bold quantum-text-glow">45ms</div>
                <div class="quantum-stat-label text-xs quantum-text-secondary">PING</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 📊 数据可视化网格 -->
    <div class="data-visualization-section mb-4 md:mb-6 lg:mb-8">
      <div class="section-header mb-4 md:mb-6 flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 p-3 sm:p-4 rounded-lg">
        <h3 class="section-title cyber-text-neon text-sm sm:text-base md:text-lg lg:text-xl font-bold leading-tight">
          <span class="hidden lg:inline">ADVANCED DATA VISUALIZATION MATRIX</span>
          <span class="lg:hidden">DATA VISUALIZATION</span>
        </h3>
        <div class="section-controls flex flex-wrap items-center gap-1 sm:gap-2 md:gap-4">
          <span class="cyber-hud-element text-xs px-2 py-1 rounded">REAL-TIME</span>
          <span class="cyber-hud-element text-xs px-2 py-1 rounded">6 PANELS</span>
          <button class="cyber-btn-pulse text-xs sm:text-sm px-3 py-2 rounded">
            <i class="i-carbon-analytics mr-1 sm:mr-2 text-xs sm:text-sm"></i>
            <span class="hidden sm:inline">FULL ANALYSIS</span>
            <span class="sm:hidden">ANALYSIS</span>
          </button>
        </div>
      </div>
      <!-- <DataVisualizationGrid /> -->
      <div class="data-visualization-placeholder cyber-card-hologram p-4 sm:p-6 md:p-8 rounded-xl">
        <div class="placeholder-content text-center">
          <i class="i-carbon-analytics text-3xl sm:text-4xl md:text-5xl lg:text-6xl cyber-text-glow mb-2 sm:mb-3 md:mb-4"></i>
          <h4 class="text-base sm:text-lg md:text-xl lg:text-2xl font-semibold cyber-text-matrix mb-1 sm:mb-2 leading-tight">
            <span class="hidden md:inline">ADVANCED DATA VISUALIZATION MATRIX</span>
            <span class="md:hidden">DATA VISUALIZATION</span>
          </h4>
          <p class="text-xs sm:text-sm text-[var(--quantum-fg-secondary)] mb-3 sm:mb-4 leading-relaxed">
            <span class="hidden lg:inline">6 interactive data panels with real-time monitoring</span>
            <span class="lg:hidden">6 interactive panels</span>
          </p>
          <div class="visualization-stats grid grid-cols-3 gap-1 sm:gap-2 md:gap-4 max-w-xs sm:max-w-sm md:max-w-md mx-auto">
            <div class="stat-item text-center p-1 sm:p-2 rounded-lg bg-[var(--quantum-bg-elevated)]">
              <div class="stat-value text-xs sm:text-sm md:text-lg font-bold cyber-text-glow">6</div>
              <div class="stat-label text-xs text-[var(--quantum-fg-secondary)]">PANELS</div>
            </div>
            <div class="stat-item text-center p-1 sm:p-2 rounded-lg bg-[var(--quantum-bg-elevated)]">
              <div class="stat-value text-xs sm:text-sm md:text-lg font-bold cyber-text-glow">24</div>
              <div class="stat-label text-xs text-[var(--quantum-fg-secondary)]">METRICS</div>
            </div>
            <div class="stat-item text-center p-1 sm:p-2 rounded-lg bg-[var(--quantum-bg-elevated)]">
              <div class="stat-value text-xs sm:text-sm md:text-lg font-bold cyber-text-glow">LIVE</div>
              <div class="stat-label text-xs text-[var(--quantum-fg-secondary)]">STATUS</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 🎯 系统状态监控中心 -->
    <div class="system-monitoring-section mb-4 md:mb-6 lg:mb-8">
      <div class="section-header mb-4 md:mb-6 flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 p-3 sm:p-4 rounded-lg">
        <h3 class="section-title cyber-text-neon text-sm sm:text-base md:text-lg lg:text-xl font-bold leading-tight">
          <span class="hidden md:inline">SYSTEM MONITORING CENTER</span>
          <span class="md:hidden">SYSTEM MONITORING</span>
        </h3>
        <div class="section-controls flex flex-wrap items-center gap-1 sm:gap-2 md:gap-4">
          <span class="cyber-hud-element text-xs px-2 py-1 rounded">REAL-TIME</span>
          <span class="cyber-hud-element text-xs px-2 py-1 rounded">AUTO-REFRESH</span>
          <button class="cyber-btn-pulse text-xs sm:text-sm px-3 py-2 rounded">
            <i class="i-carbon-dashboard mr-1 sm:mr-2 text-xs sm:text-sm"></i>
            <span class="hidden sm:inline">FULL DASHBOARD</span>
            <span class="sm:hidden">DASHBOARD</span>
          </button>
        </div>
      </div>
      <!-- <SystemStatusPanel /> -->
      <div class="system-status-placeholder cyber-card-hologram p-4 sm:p-6 md:p-8 rounded-xl">
        <div class="placeholder-content text-center">
          <i class="i-carbon-dashboard text-3xl sm:text-4xl md:text-5xl lg:text-6xl cyber-text-glow mb-2 sm:mb-3 md:mb-4"></i>
          <h4 class="text-base sm:text-lg md:text-xl lg:text-2xl font-semibold cyber-text-matrix mb-1 sm:mb-2 leading-tight">
            <span class="hidden md:inline">SYSTEM MONITORING CENTER</span>
            <span class="md:hidden">SYSTEM MONITORING</span>
          </h4>
          <p class="text-xs sm:text-sm text-[var(--quantum-fg-secondary)] mb-3 sm:mb-4 leading-relaxed">
            <span class="hidden lg:inline">Comprehensive system health and performance monitoring</span>
            <span class="lg:hidden">System health monitoring</span>
          </p>
          <div class="system-stats grid grid-cols-2 sm:grid-cols-4 gap-1 sm:gap-2 md:gap-4 max-w-xs sm:max-w-sm md:max-w-lg mx-auto">
            <div class="stat-item text-center p-1 sm:p-2 rounded-lg bg-[var(--quantum-bg-elevated)]">
              <div class="stat-value text-xs sm:text-sm md:text-lg font-bold cyber-text-glow">99.9%</div>
              <div class="stat-label text-xs text-[var(--quantum-fg-secondary)]">UPTIME</div>
            </div>
            <div class="stat-item text-center p-1 sm:p-2 rounded-lg bg-[var(--quantum-bg-elevated)]">
              <div class="stat-value text-xs sm:text-sm md:text-lg font-bold cyber-text-glow">67%</div>
              <div class="stat-label text-xs text-[var(--quantum-fg-secondary)]">CPU</div>
            </div>
            <div class="stat-item text-center p-1 sm:p-2 rounded-lg bg-[var(--quantum-bg-elevated)]">
              <div class="stat-value text-xs sm:text-sm md:text-lg font-bold cyber-text-glow">8.2GB</div>
              <div class="stat-label text-xs text-[var(--quantum-fg-secondary)]">MEMORY</div>
            </div>
            <div class="stat-item text-center p-1 sm:p-2 rounded-lg bg-[var(--quantum-bg-elevated)]">
              <div class="stat-value text-xs sm:text-sm md:text-lg font-bold cyber-text-glow">45ms</div>
              <div class="stat-label text-xs text-[var(--quantum-fg-secondary)]">LATENCY</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 🔧 量子操作面板 -->
    <div class="quantum-actions-section p-4 sm:p-6 rounded-xl mb-4 md:mb-6 lg:mb-8">
      <h3 class="quantum-section-title quantum-text-neon mb-4 md:mb-6 text-sm sm:text-base md:text-lg lg:text-xl font-bold leading-tight">
        <span class="hidden md:inline">QUANTUM OPERATION PANEL</span>
        <span class="md:hidden">QUANTUM OPERATIONS</span>
      </h3>
      <div class="quantum-actions-grid grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-2 sm:gap-3 md:gap-4">
        <button v-for="action in quantumActions" :key="action.id"
                @click="handleQuantumAction(action)"
                class="quantum-action-btn quantum-border-energy quantum-sound-effect quantum-ripple-effect p-2 sm:p-3 md:p-4 flex flex-col items-center gap-1 sm:gap-2 md:gap-3 rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg">
          <div class="quantum-action-icon w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 flex items-center justify-center rounded-full" :class="action.color">
            <i :class="action.icon" class="text-xs sm:text-sm md:text-lg"></i>
          </div>
          <span class="quantum-action-label text-xs md:text-sm font-medium text-center leading-tight">{{ action.label }}</span>
          <div class="quantum-action-status w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full" :class="action.status"></div>
        </button>
      </div>
    </div>

    <!-- 🚀 量子快速导航面板 -->
    <div class="quantum-navigation-section p-4 sm:p-6 rounded-xl">
      <h3 class="quantum-section-title quantum-text-neon mb-4 md:mb-6 text-sm sm:text-base md:text-lg lg:text-xl font-bold leading-tight">
        <span class="hidden md:inline">QUANTUM NAVIGATION MATRIX</span>
        <span class="md:hidden">QUANTUM NAVIGATION</span>
      </h3>
      <div class="quantum-navigation-grid grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3 sm:gap-4 md:gap-6">
        <NuxtLink v-for="nav in quantumNavigation" :key="nav.id"
                  :to="nav.path"
                  class="quantum-nav-card quantum-card-hologram p-4 sm:p-6 rounded-xl text-center transition-all hover:scale-105 block">
          <div class="nav-icon quantum-energy-ring w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4">
            <i :class="nav.icon" class="text-lg sm:text-2xl"></i>
          </div>
          <h4 class="nav-title quantum-text-neon text-xs sm:text-sm font-bold mb-1 sm:mb-2">{{ nav.title }}</h4>
          <p class="nav-description text-xs quantum-text-muted mb-2 sm:mb-3">{{ nav.description }}</p>
          <span v-if="nav.badge" class="nav-badge quantum-hud-element text-xs px-2 py-1 rounded">
            {{ nav.badge }}
          </span>
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'

// 页面元数据
definePageMeta({
  middleware: 'auth'
})

useHead({
  title: '系统概览 - AR-System Dashboard',
  meta: [
    { name: 'description', content: 'AR-System Dashboard 系统概览页面，实时监控系统状态和关键指标。' }
  ]
})

// API调用
const { analytics, users } = useApi()

// 响应式数据
const performanceTimeRange = ref('24h')
const isLoading = ref(true)
const error = ref('')

// 科技感指标数据
const keyMetrics = ref([])
const dashboardData = ref(null)
const systemOverview = ref(null)

// 设备状态数据
const deviceStatus = ref([])

// 最新订单数据
const recentOrders = ref([])

// 系统通知数据
const systemNotifications = ref([])

// 全球访客统计
const visitorStats = ref([])

// 用户统计数据
const userStats = ref(null)

// 性能数据
const performanceData = ref(null)

// 量子操作面板
const quantumActions = ref([
  { id: 1, label: 'QUANTUM NEURAL LINK', icon: 'i-carbon-3d-cursor', color: 'quantum-primary', status: 'active' },
  { id: 2, label: 'QUANTUM USER MATRIX', icon: 'i-carbon-user-multiple', color: 'quantum-accent', status: 'active' },
  { id: 3, label: 'QUANTUM DATA STREAM', icon: 'i-carbon-analytics', color: 'quantum-secondary', status: 'processing' },
  { id: 4, label: 'QUANTUM CORE', icon: 'i-carbon-3d-curve-auto-colon', color: 'quantum-energy', status: 'standby' },
  { id: 5, label: 'QUANTUM SYSTEM GRID', icon: 'i-carbon-settings', color: 'quantum-primary', status: 'active' },
  { id: 6, label: 'QUANTUM HELP MATRIX', icon: 'i-carbon-help', color: 'quantum-accent', status: 'ready' },
  { id: 7, label: 'QUANTUM COMMAND CENTER', icon: 'i-carbon-terminal', color: 'quantum-matrix', status: 'active' },
  { id: 8, label: 'QUANTUM CONTROL PANEL', icon: 'i-carbon-dashboard', color: 'quantum-energy', status: 'ready' }
])

// 量子导航面板
const quantumNavigation = ref([
  {
    id: 1,
    title: 'DEVICES',
    description: 'AR设备管理',
    icon: 'i-carbon-devices',
    path: '/devices',
    badge: '24'
  },
  {
    id: 2,
    title: 'APPLICATIONS',
    description: '应用管理',
    icon: 'i-carbon-application',
    path: '/applications',
    badge: '156'
  },
  {
    id: 3,
    title: 'USERS',
    description: '用户管理',
    icon: 'i-carbon-user-multiple',
    path: '/users',
    badge: '2.4K'
  },
  {
    id: 4,
    title: 'ANALYTICS',
    description: '数据分析',
    icon: 'i-carbon-analytics',
    path: '/analytics',
    badge: null
  },
  {
    id: 5,
    title: 'GLOBAL MAP',
    description: '全球访客',
    icon: 'i-carbon-earth-filled',
    path: '/analytics/global-visitors',
    badge: '156国'
  },
  {
    id: 6,
    title: 'COMMUNITY',
    description: '社区管理',
    icon: 'i-carbon-user-speaker',
    path: '/community',
    badge: '24.5K'
  },
  {
    id: 7,
    title: 'ECOMMERCE',
    description: '电商管理',
    icon: 'i-carbon-shopping-cart',
    path: '/ecommerce',
    badge: '$2.4M'
  },
  {
    id: 8,
    title: 'DEV TOOLS',
    description: '开发工具',
    icon: 'i-carbon-code',
    path: '/developer-tools',
    badge: null
  },
  {
    id: 9,
    title: 'NOTIFICATIONS',
    description: '通知中心',
    icon: 'i-carbon-notification',
    path: '/notifications',
    badge: '12'
  },
  {
    id: 10,
    title: 'SETTINGS',
    description: '系统设置',
    icon: 'i-carbon-settings',
    path: '/settings',
    badge: null
  },
  {
    id: 11,
    title: 'THEME PREVIEW',
    description: '主题预览',
    icon: 'i-carbon-color-palette',
    path: '/theme-preview',
    badge: 'NEW'
  }
])

// API数据加载方法
const loadDashboardData = async () => {
  try {
    isLoading.value = true
    error.value = ''

    // 并行加载所有数据
    const [dashboardResponse, userStatsResponse, performanceResponse, overviewResponse] = await Promise.all([
      analytics.dashboardData(),
      users.stats(),
      analytics.performanceMetrics(performanceTimeRange.value),
      analytics.overview()
    ])

    // 处理仪表盘数据
    if (dashboardResponse.success) {
      dashboardData.value = dashboardResponse.data

      // 构建关键指标
      keyMetrics.value = [
        {
          id: 1,
          label: 'ACTIVE NODES',
          value: dashboardResponse.data.active_nodes || 0,
          change: dashboardResponse.data.nodes_growth || 0,
          changeType: (dashboardResponse.data.nodes_growth || 0) >= 0 ? 'positive' : 'negative',
          icon: 'i-carbon-devices',
          status: 'ONLINE',
          progress: Math.min((dashboardResponse.data.active_nodes / 200) * 100, 100)
        },
        {
          id: 2,
          label: 'NEURAL LINKS',
          value: dashboardResponse.data.active_sessions || 0,
          change: dashboardResponse.data.session_growth || 0,
          changeType: (dashboardResponse.data.session_growth || 0) >= 0 ? 'positive' : 'negative',
          icon: 'i-carbon-user-multiple',
          status: 'ACTIVE',
          progress: Math.min((dashboardResponse.data.active_sessions / 5000) * 100, 100)
        },
        {
          id: 3,
          label: 'DATA STREAMS',
          value: Math.round(dashboardResponse.data.data_throughput || 0),
          change: dashboardResponse.data.throughput_growth || 0,
          changeType: (dashboardResponse.data.throughput_growth || 0) >= 0 ? 'positive' : 'negative',
          icon: 'i-carbon-analytics',
          status: 'PROCESSING',
          progress: Math.min((dashboardResponse.data.data_throughput / 100) * 100, 100)
        },
        {
          id: 4,
          label: 'SYSTEM LOAD',
          value: Math.round(dashboardResponse.data.system_load || 0),
          change: dashboardResponse.data.load_change || 0,
          changeType: (dashboardResponse.data.load_change || 0) <= 0 ? 'positive' : 'negative',
          icon: 'i-carbon-dashboard',
          status: dashboardResponse.data.system_load < 80 ? 'STABLE' : 'HIGH',
          progress: dashboardResponse.data.system_load || 0
        }
      ]
    }

    // 处理用户统计数据
    if (userStatsResponse.success) {
      userStats.value = userStatsResponse.data

      // 更新访客统计
      visitorStats.value = [
        { label: '在线访客', value: formatNumber(userStatsResponse.data.active_users || 0) },
        { label: '今日访问', value: formatNumber(userStatsResponse.data.daily_visits || 0) },
        { label: '本月访问', value: formatNumber(userStatsResponse.data.monthly_visits || 0) },
        { label: '总访问量', value: formatNumber(userStatsResponse.data.total_visits || 0) }
      ]
    }

    // 处理性能数据
    if (performanceResponse.success) {
      performanceData.value = performanceResponse.data
    }

    // 处理系统概览数据
    if (overviewResponse.success) {
      systemOverview.value = overviewResponse.data

      // 生成系统通知（基于真实数据）
      systemNotifications.value = [
        {
          id: 1,
          type: 'info',
          message: `系统运行时间: ${overviewResponse.data.uptime || '99.9%'}`,
          time: new Date(Date.now() - 5 * 60 * 1000),
          read: false
        },
        {
          id: 2,
          type: overviewResponse.data.system_load > 80 ? 'warning' : 'success',
          message: `系统负载: ${overviewResponse.data.system_load || 0}%`,
          time: new Date(Date.now() - 15 * 60 * 1000),
          read: false
        },
        {
          id: 3,
          type: 'success',
          message: `活跃用户: ${overviewResponse.data.active_users || 0}`,
          time: new Date(Date.now() - 30 * 60 * 1000),
          read: true
        },
        {
          id: 4,
          type: overviewResponse.data.error_rate > 1 ? 'error' : 'info',
          message: `错误率: ${(overviewResponse.data.error_rate || 0).toFixed(2)}%`,
          time: new Date(Date.now() - 45 * 60 * 1000),
          read: false
        }
      ]
    }

  } catch (err) {
    console.error('Failed to load dashboard data:', err)
    error.value = '加载仪表盘数据失败，请稍后重试'
  } finally {
    isLoading.value = false
  }
}

// 方法
const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const getChangeIcon = (type: string) => {
  switch (type) {
    case 'positive': return 'i-carbon-trending-up'
    case 'negative': return 'i-carbon-trending-down'
    default: return 'i-carbon-minus'
  }
}

const getStatusText = (status: string) => {
  const statusMap = {
    online: '在线',
    offline: '离线',
    warning: '警告'
  }
  return statusMap[status] || status
}

const getNotificationIcon = (type: string) => {
  const icons = {
    info: 'i-carbon-information',
    warning: 'i-carbon-warning',
    error: 'i-carbon-error',
    success: 'i-carbon-checkmark'
  }
  return icons[type] || 'i-carbon-information'
}

const formatTime = (time: Date) => {
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  const minutes = Math.floor(diff / 60000)

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`

  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}小时前`

  const days = Math.floor(hours / 24)
  return `${days}天前`
}

const refreshData = async () => {
  await loadDashboardData()
}

const exportUserData = () => {
  console.log('导出用户数据')
  // TODO: 实现数据导出逻辑
}

const markAllNotificationsRead = () => {
  systemNotifications.value.forEach(n => n.read = true)
}

const handleQuantumAction = (action: any) => {
  console.log('量子操作:', action.label)
  // 这里实现量子操作逻辑

  // 模拟状态变化
  action.status = action.status === 'active' ? 'processing' : 'active'
}

// 监听时间范围变化
watch(performanceTimeRange, () => {
  loadDashboardData()
})

// 生命周期
onMounted(() => {
  console.log('Dashboard 概览页面已加载')
  loadDashboardData()
})
</script>

<style scoped>
/* 🚀 科技感页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  position: relative;
  /* 移除固定样式，让quantum-card-hologram类控制主题 */
}

.header-content h1 {
  margin: 0;
  font-family: 'Courier New', monospace;
  letter-spacing: 2px;
}

/* header-actions 样式已在 quantum-variables.css 中定义 */

.system-status-bar {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

/* 🔮 科技感指标卡片 */
.metric-card-cyber {
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
}

.metric-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.metric-ring {
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.metric-icon-cyber {
  font-size: 1.5rem;
  color: var(--quantum-primary);
  z-index: 2;
  position: relative;
}

.metric-status {
  font-size: 0.75rem;
}

.metric-content-cyber {
  text-align: center;
}

.metric-value-cyber {
  font-size: 2.5rem;
  font-weight: 900;
  font-family: 'Courier New', monospace;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.metric-label-cyber {
  font-size: 0.875rem;
  color: var(--quantum-primary);
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
  margin-bottom: 1rem;
  text-transform: uppercase;
}

.metric-progress {
  margin: 1rem 0;
}

.metric-change-cyber {
  font-size: 0.75rem;
  font-weight: 600;
  font-family: 'Courier New', monospace;
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.metric-change-cyber.positive {
  color: var(--quantum-accent);
}

.metric-change-cyber.negative {
  color: var(--quantum-error);
}

.metric-change-cyber.neutral {
  color: var(--quantum-warning);
}

/* 📊 科技感图表面板 */
.chart-panel {
  padding: 1.5rem;
  position: relative;
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  padding: 1rem;
  border-radius: 8px;
}

.chart-title {
  font-size: 1.125rem;
  font-weight: 700;
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
}

.chart-controls {
  display: flex;
  gap: 0.75rem;
}

.cyber-select {
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 4px;
  padding: 0.5rem 1rem;
  color: var(--quantum-primary);
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.cyber-select:focus {
  outline: none;
  border-color: var(--quantum-primary);
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.chart-container-cyber {
  background: rgba(0, 17, 34, 0.6);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

/* 📊 占位符样式 */
.chart-placeholder,
.globe-placeholder,
.matrix-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: radial-gradient(circle at center, rgba(0, 255, 255, 0.05) 0%, transparent 70%);
  border-radius: 8px;
  position: relative;
}

.chart-placeholder::before,
.globe-placeholder::before,
.matrix-placeholder::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg,
    rgba(0, 255, 255, 0.05) 0%,
    rgba(255, 0, 128, 0.05) 25%,
    rgba(0, 255, 65, 0.05) 50%,
    rgba(255, 170, 0, 0.05) 75%,
    rgba(0, 255, 255, 0.05) 100%);
  background-size: 400% 400%;
  animation: gradientShift 8s ease-in-out infinite;
  border-radius: 8px;
}

.placeholder-content {
  position: relative;
  z-index: 2;
  padding: 2rem;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* 🎛️ 控制面板 */
.view-all-link {
  font-size: 0.875rem;
  color: var(--quantum-primary);
  text-decoration: none;
  display: flex;
  align-items: center;
  transition: color var(--dashboard-transition-fast);
}

.view-all-link:hover {
  color: var(--quantum-bg-elevated);
}

/* 🚀 量子导航面板 */
.quantum-navigation-section {
  background: var(--quantum-matrix-bg);
  border: 1px solid var(--quantum-border-color);
  backdrop-filter: blur(10px);
}

.quantum-nav-card {
  transition: all var(--quantum-transition-normal);
  text-decoration: none;
  color: inherit;
}

.quantum-nav-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--quantum-glow-primary);
  border-color: var(--quantum-color-primary);
}

.nav-badge {
  background: var(--quantum-color-primary);
  color: #000;
  font-weight: 600;
}

/* 设备状态 */
.device-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  background: var(--quantum-bg-elevated);
  border-radius: 0.5rem;
  border: 1px solid var(--quantum-border-color);
  transition: all var(--dashboard-transition-fast);
}

.device-item:hover {
  background: var(--quantum-bg-surface);
  border-color: var(--quantum-border-emphasis);
}

/* 订单列表 */
.order-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  background: var(--quantum-bg-elevated);
  border-radius: 0.5rem;
  border: 1px solid var(--quantum-border-color);
  transition: all var(--dashboard-transition-fast);
}

.order-item:hover {
  background: var(--quantum-bg-surface);
  border-color: var(--quantum-border-emphasis);
}

/* 通知列表 */
.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  transition: all var(--dashboard-transition-fast);
}

.notification-item.unread {
  background: rgba(0, 212, 255, 0.05);
  border: 1px solid rgba(0, 212, 255, 0.1);
}

.notification-icon {
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  flex-shrink: 0;
}

.notification-icon.info {
  background: rgba(var(--quantum-info-rgb), 0.1);
  color: var(--quantum-info);
}

.notification-icon.warning {
  background: rgba(var(--quantum-warning-rgb), 0.1);
  color: var(--quantum-warning);
}

.notification-icon.error {
  background: rgba(var(--quantum-error-rgb), 0.1);
  color: var(--quantum-error);
}

.notification-icon.success {
  background: rgba(46, 213, 115, 0.1);
  color: var(--quantum-success);
}

.notification-content {
  flex: 1;
  min-width: 0;
}

/* 🌍 全球网络监控中心 */
.global-network-center {
  padding: 1.5rem;
  position: relative;
}

.network-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  padding: 1rem;
  border-radius: 8px;
}

.network-title {
  font-size: 1.25rem;
  font-weight: 700;
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
}

.network-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.network-visualization-container {
  background: rgba(0, 17, 34, 0.6);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

/* 📊 数据可视化区域 */
.data-visualization-section {
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  background: rgba(0, 212, 255, 0.05);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 12px;
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  font-family: 'Courier New', monospace;
  letter-spacing: 2px;
}

.section-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* 🎯 系统监控区域 */
.system-monitoring-section {
  margin-bottom: 2rem;
}

/* 📊 占位符样式增强 */
.data-visualization-placeholder,
.system-status-placeholder {
  padding: 3rem 2rem;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.data-visualization-placeholder .placeholder-content,
.system-status-placeholder .placeholder-content {
  max-width: 600px;
  width: 100%;
}

.visualization-stats,
.system-stats {
  margin-top: 2rem;
}

.visualization-stats .stat-item,
.system-stats .stat-item {
  padding: 1rem;
  background: rgba(0, 212, 255, 0.05);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.visualization-stats .stat-item:hover,
.system-stats .stat-item:hover {
  background: rgba(0, 212, 255, 0.1);
  transform: translateY(-2px);
}

/* 🔧 量子操作面板 */
.quantum-actions-section {
  margin-bottom: 2rem;
}

.quantum-actions-grid {
  gap: 1rem;
}

.quantum-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 1.5rem 1rem;
  background: rgba(0, 17, 34, 0.8);
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.quantum-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s;
}

.quantum-action-btn:hover::before {
  left: 100%;
}

.quantum-action-btn:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: var(--glow-cyber-primary);
}

.action-icon-quantum {
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  position: relative;
  background: rgba(0, 255, 255, 0.1);
  border: 2px solid rgba(0, 255, 255, 0.3);
}

.action-icon-quantum.cyber-primary {
  color: var(--quantum-primary);
  border-color: var(--quantum-primary);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

.action-icon-quantum.cyber-accent {
  color: var(--quantum-accent);
  border-color: var(--quantum-accent);
  box-shadow: 0 0 15px rgba(0, 255, 65, 0.3);
}

.action-icon-quantum.cyber-secondary {
  color: var(--quantum-secondary);
  border-color: var(--quantum-secondary);
  box-shadow: 0 0 15px rgba(255, 0, 128, 0.3);
}

.action-icon-quantum.cyber-warning {
  color: var(--quantum-warning);
  border-color: var(--quantum-warning);
  box-shadow: 0 0 15px rgba(255, 170, 0, 0.3);
}

.action-label-quantum {
  color: var(--quantum-primary);
  text-align: center;
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.action-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: statusPulse 2s ease-in-out infinite;
}

.action-status.active {
  background: var(--quantum-accent);
  box-shadow: 0 0 8px var(--quantum-accent);
}

.action-status.processing {
  background: var(--quantum-warning);
  box-shadow: 0 0 8px var(--quantum-warning);
  animation: processingPulse 1s ease-in-out infinite;
}

.action-status.standby {
  background: var(--quantum-secondary);
  box-shadow: 0 0 8px var(--quantum-secondary);
}

.action-status.ready {
  background: var(--quantum-primary);
  box-shadow: 0 0 8px var(--quantum-primary);
}

/* 🎬 动画 */
@keyframes statusPulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

@keyframes processingPulse {
  0%, 100% { opacity: 0.4; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

/* 🌌 量子页面样式系统 */
.dashboard-overview {
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

.page-header {
  margin-bottom: var(--space-8);
  padding: var(--space-8);
  position: relative;
  overflow: hidden;
}

.header-content {
  position: relative;
  z-index: 2;
}

.page-title {
  font-size: var(--text-4xl);
  font-weight: 900;
  margin-bottom: var(--space-4);
  text-align: center;
  transition: all var(--transition-normal);
}

.page-title:hover {
  transform: translateY(-2px);
}

.page-subtitle {
  font-size: var(--text-xl);
  margin-bottom: var(--space-6);
  text-align: center;
  font-family: var(--font-mono);
  letter-spacing: 0.1em;
}

.quantum-status-bar {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.header-actions {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
}

.quantum-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-8);
  margin-bottom: var(--space-8);
}

.quantum-metric-card {
  position: relative;
  text-align: center;
  padding: var(--space-8);
}

.quantum-metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
}

.quantum-metric-ring {
  margin: 0 auto var(--space-4);
}

.quantum-metric-icon {
  font-size: 1.5rem;
  color: var(--quantum-primary);
}

.quantum-metric-status {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
}

.quantum-metric-content {
  position: relative;
}

.quantum-metric-value {
  font-size: var(--text-4xl);
  font-weight: 900;
  margin-bottom: var(--space-3);
  font-family: var(--font-mono);
  letter-spacing: 0.05em;
}

.quantum-metric-label {
  font-size: var(--text-sm);
  margin-bottom: var(--space-4);
  font-family: var(--font-mono);
  letter-spacing: 0.1em;
}

.quantum-metric-progress {
  margin-bottom: var(--space-4);
  position: relative;
}

.quantum-progress-bar {
  height: 0.75rem;
  background: rgba(0, 212, 255, 0.1);
  border-radius: 0.5rem;
  overflow: hidden;
  border: 1px solid var(--quantum-border-color);
}

.quantum-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--quantum-primary), var(--quantum-secondary), var(--quantum-accent));
  transition: width var(--transition-normal);
  position: relative;
}

.quantum-progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.quantum-metric-card:hover .quantum-progress-fill::after {
  opacity: 1;
  animation: quantumScan 1.5s ease-out;
}

.quantum-metric-change {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  font-weight: 600;
  font-family: var(--font-mono);
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.quantum-metric-change.positive {
  color: var(--quantum-success);
  text-shadow: var(--quantum-glow-accent);
}
.quantum-metric-change.negative {
  color: var(--quantum-error);
  text-shadow: 0 0 10px var(--quantum-error);
}
.quantum-metric-change.neutral {
  color: var(--quantum-fg-muted);
}

/* 🌌 全面量子响应式设计 */

/* 超大屏幕 (1920px+) */
@media (min-width: 1920px) {
  .dashboard-overview {
    max-width: 1600px;
  }

  .quantum-metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--space-10);
  }

  .page-header {
    padding: var(--space-10);
  }

  .page-title {
    font-size: 3.5rem;
  }

  .page-subtitle {
    font-size: var(--text-2xl);
  }
}

/* 大屏幕 (1440px - 1919px) */
@media (max-width: 1919px) and (min-width: 1440px) {
  .dashboard-overview {
    max-width: 1400px;
  }

  .quantum-metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-8);
  }

  .page-header {
    padding: var(--space-8);
  }
}

/* 标准桌面 (1200px - 1439px) */
@media (max-width: 1439px) and (min-width: 1200px) {
  .quantum-metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-6);
  }
}

/* 小桌面/大平板 (1024px - 1199px) */
@media (max-width: 1199px) and (min-width: 1024px) {
  .dashboard-overview {
    max-width: 100%;
    padding: var(--space-4);
  }

  .quantum-metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
  }

  .page-header {
    padding: var(--space-6);
  }

  .page-title {
    font-size: var(--text-3xl);
  }
}

/* 平板竖屏 (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
  .dashboard-overview {
    padding: var(--space-4);
  }

  .page-header {
    padding: var(--space-6);
  }

  .page-title {
    font-size: var(--text-3xl);
  }

  .page-subtitle {
    font-size: var(--text-lg);
  }

  .quantum-metrics-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-4);
  }

  .quantum-status-bar {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-3);
  }

  .quantum-metric-card {
    padding: var(--space-5);
  }

  .quantum-metric-value {
    font-size: var(--text-3xl);
  }
}

/* 手机横屏 (640px - 767px) */
@media (max-width: 767px) and (min-width: 640px) {
  .dashboard-overview {
    padding: var(--space-3);
  }

  .page-header {
    padding: var(--space-5);
  }

  .page-title {
    font-size: var(--text-2xl);
    line-height: 1.1;
  }

  .page-subtitle {
    font-size: var(--text-base);
  }

  .quantum-metrics-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-3);
  }

  .quantum-status-bar {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-2);
  }

  .quantum-metric-card {
    padding: var(--space-4);
  }

  .quantum-metric-value {
    font-size: var(--text-2xl);
  }

  .quantum-hud-element {
    font-size: 0.7rem;
    padding: var(--space-1) var(--space-2);
  }
}

/* 手机竖屏 (480px - 639px) */
@media (max-width: 639px) and (min-width: 480px) {
  .dashboard-overview {
    padding: var(--space-2);
  }

  .page-header {
    padding: var(--space-4);
  }

  .page-title {
    font-size: var(--text-xl);
    line-height: 1.1;
  }

  .page-subtitle {
    font-size: var(--text-sm);
    margin-bottom: var(--space-4);
  }

  .quantum-metrics-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .quantum-status-bar {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
  }

  .quantum-metric-card {
    padding: var(--space-4);
  }

  .quantum-metric-value {
    font-size: var(--text-2xl);
  }

  .quantum-hud-element {
    font-size: 0.65rem;
    padding: var(--space-1);
  }

  .header-actions {
    margin-top: var(--space-4);
  }
}

/* 小手机 (320px - 479px) */
@media (max-width: 479px) {
  .dashboard-overview {
    padding: var(--space-2);
  }

  .page-header {
    padding: var(--space-3);
  }

  .page-title {
    font-size: var(--text-lg);
    line-height: 1.1;
    margin-bottom: var(--space-2);
  }

  .page-subtitle {
    font-size: var(--text-xs);
    margin-bottom: var(--space-3);
  }

  .quantum-metrics-grid {
    grid-template-columns: 1fr;
    gap: var(--space-2);
  }

  .quantum-status-bar {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
  }

  .quantum-metric-card {
    padding: var(--space-3);
  }

  .quantum-metric-value {
    font-size: var(--text-xl);
  }

  .quantum-metric-label {
    font-size: var(--text-xs);
  }

  .quantum-hud-element {
    font-size: 0.6rem;
    padding: 2px 6px;
  }

  .quantum-btn-pulse {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
  }

  .header-actions {
    margin-top: var(--space-3);
  }
}
</style>
