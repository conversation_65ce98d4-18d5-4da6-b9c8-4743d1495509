<template>
  <div class="quantum-app-store-page">
    <!-- 🌌 量子页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="breadcrumb mb-2">
          <NuxtLink to="/applications" class="breadcrumb-link text-[var(--quantum-fg-secondary)] hover:text-[var(--quantum-primary)]">
            应用管理
          </NuxtLink>
          <span class="breadcrumb-separator mx-2 text-[var(--quantum-fg-muted)]">/</span>
          <span class="breadcrumb-current text-[var(--quantum-fg-primary)]">应用商店</span>
        </div>
        <h1 class="page-title quantum-text-neon quantum-glow-effect">
          🏪 应用商店
        </h1>
        <p class="page-subtitle quantum-text-glow">
          >> AR应用生态系统与插件市场 <<
        </p>
        <div class="quantum-status-bar">
          <span class="quantum-hud-element">📱 应用: {{ totalApps }}</span>
          <span class="quantum-hud-element">⭐ 精选: {{ featuredApps }}</span>
          <span class="quantum-hud-element">🔄 更新: {{ updatesAvailable }}</span>
          <span class="quantum-hud-element">⚡ 同步: {{ lastSyncTime }}</span>
        </div>
      </div>
      <div class="header-actions">
        <button @click="refreshStore" class="action-btn refresh-btn">
          <i class="i-carbon-refresh"></i>
          <span>刷新</span>
        </button>
        <button @click="checkUpdates" class="action-btn secondary-btn">
          <i class="i-carbon-update-now"></i>
          <span>检查更新</span>
        </button>
        <button @click="uploadApp" class="action-btn create-btn">
          <i class="i-carbon-cloud-upload"></i>
          <span>上传应用</span>
        </button>
      </div>
    </div>

    <div class="store-layout grid grid-cols-1 lg:grid-cols-4 gap-6">
      <!-- 左侧：分类和筛选 -->
      <div class="categories-section space-y-6">
        <!-- 应用分类 -->
        <div class="quantum-card-hologram p-4 rounded-xl">
          <h3 class="section-title quantum-text-matrix text-lg font-semibold mb-4">应用分类</h3>
          <div class="categories-list space-y-2">
            <div 
              v-for="category in appCategories" 
              :key="category.id"
              @click="selectCategory(category)"
              class="category-item flex items-center justify-between p-3 rounded cursor-pointer hover:bg-[var(--quantum-bg-elevated)] transition-all"
              :class="{ 'bg-[var(--quantum-bg-elevated)] border-l-2 border-[var(--quantum-primary)]': selectedCategory?.id === category.id }"
            >
              <div class="category-info flex items-center gap-3">
                <i :class="category.icon" class="text-[var(--quantum-primary)]"></i>
                <span class="category-name text-sm">{{ category.name }}</span>
              </div>
              <div class="category-count">
                <span class="count-badge text-xs px-2 py-1 rounded bg-[var(--quantum-bg-elevated)] text-[var(--quantum-fg-muted)]">
                  {{ category.count }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 筛选器 -->
        <div class="quantum-card-hologram p-4 rounded-xl">
          <h3 class="section-title quantum-text-matrix text-lg font-semibold mb-4">筛选器</h3>
          <div class="filters space-y-4">
            <div class="filter-group">
              <label class="filter-label text-sm text-[var(--quantum-fg-secondary)] mb-2 block">价格</label>
              <div class="price-filters space-y-2">
                <label class="filter-option flex items-center gap-2 cursor-pointer">
                  <input type="radio" v-model="priceFilter" value="" class="radio-input" />
                  <span class="option-text text-sm">全部</span>
                </label>
                <label class="filter-option flex items-center gap-2 cursor-pointer">
                  <input type="radio" v-model="priceFilter" value="free" class="radio-input" />
                  <span class="option-text text-sm">免费</span>
                </label>
                <label class="filter-option flex items-center gap-2 cursor-pointer">
                  <input type="radio" v-model="priceFilter" value="paid" class="radio-input" />
                  <span class="option-text text-sm">付费</span>
                </label>
              </div>
            </div>

            <div class="filter-group">
              <label class="filter-label text-sm text-[var(--quantum-fg-secondary)] mb-2 block">评分</label>
              <div class="rating-filters space-y-2">
                <label class="filter-option flex items-center gap-2 cursor-pointer">
                  <input type="radio" v-model="ratingFilter" value="" class="radio-input" />
                  <span class="option-text text-sm">全部评分</span>
                </label>
                <label class="filter-option flex items-center gap-2 cursor-pointer">
                  <input type="radio" v-model="ratingFilter" value="4+" class="radio-input" />
                  <span class="option-text text-sm">4星以上</span>
                </label>
                <label class="filter-option flex items-center gap-2 cursor-pointer">
                  <input type="radio" v-model="ratingFilter" value="3+" class="radio-input" />
                  <span class="option-text text-sm">3星以上</span>
                </label>
              </div>
            </div>

            <div class="filter-group">
              <label class="filter-label text-sm text-[var(--quantum-fg-secondary)] mb-2 block">兼容性</label>
              <div class="compatibility-filters space-y-2">
                <label class="filter-option flex items-center gap-2 cursor-pointer">
                  <input type="checkbox" v-model="compatibilityFilter" value="ar-glasses" class="checkbox-input" />
                  <span class="option-text text-sm">AR眼镜</span>
                </label>
                <label class="filter-option flex items-center gap-2 cursor-pointer">
                  <input type="checkbox" v-model="compatibilityFilter" value="mobile" class="checkbox-input" />
                  <span class="option-text text-sm">移动设备</span>
                </label>
                <label class="filter-option flex items-center gap-2 cursor-pointer">
                  <input type="checkbox" v-model="compatibilityFilter" value="desktop" class="checkbox-input" />
                  <span class="option-text text-sm">桌面端</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间：应用列表 -->
      <div class="apps-section lg:col-span-2 space-y-6">
        <!-- 搜索和排序 -->
        <div class="quantum-card-hologram p-4 rounded-xl">
          <div class="search-controls grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="search-input-group md:col-span-2">
              <input 
                v-model="searchQuery"
                type="text"
                placeholder="搜索应用名称、开发者或关键词..."
                class="search-input w-full px-3 py-2 bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)] rounded-lg"
              />
            </div>
            <div class="sort-group">
              <select v-model="sortBy" class="sort-select w-full px-3 py-2 bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)] rounded-lg">
                <option value="featured">精选推荐</option>
                <option value="rating">评分排序</option>
                <option value="downloads">下载量</option>
                <option value="newest">最新发布</option>
                <option value="updated">最近更新</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 精选应用横幅 -->
        <div v-if="!searchQuery && !selectedCategory" class="quantum-card-hologram p-6 rounded-xl">
          <h2 class="section-title quantum-text-matrix text-xl font-semibold mb-4">精选应用</h2>
          <div class="featured-apps grid grid-cols-1 md:grid-cols-2 gap-4">
            <div 
              v-for="app in featuredAppsList" 
              :key="app.id"
              class="featured-app quantum-card-hologram p-4 rounded-lg hover:border-[var(--quantum-border-glow)] transition-all cursor-pointer"
              @click="openAppDetails(app)"
            >
              <div class="app-header flex items-start gap-3 mb-3">
                <div class="app-icon quantum-energy-ring w-12 h-12 flex items-center justify-center">
                  <img :src="app.icon" :alt="app.name" class="w-full h-full rounded-lg object-cover" />
                </div>
                <div class="app-info flex-1">
                  <h3 class="app-name text-lg font-semibold quantum-text-matrix mb-1">{{ app.name }}</h3>
                  <p class="app-developer text-sm text-[var(--quantum-fg-secondary)]">{{ app.developer }}</p>
                  <div class="app-rating flex items-center gap-1 mt-1">
                    <div class="stars flex">
                      <i v-for="i in 5" :key="i" :class="i <= app.rating ? 'i-carbon-star-filled text-[var(--quantum-warning)]' : 'i-carbon-star text-[var(--quantum-fg-muted)]'" class="text-xs"></i>
                    </div>
                    <span class="rating-text text-xs text-[var(--quantum-fg-muted)]">({{ app.reviews }})</span>
                  </div>
                </div>
                <div class="app-price">
                  <span class="price-tag px-2 py-1 rounded text-sm font-semibold" :class="app.price === 'Free' ? 'bg-[var(--quantum-success)] text-white' : 'bg-[var(--quantum-primary)] text-white'">
                    {{ app.price }}
                  </span>
                </div>
              </div>
              <p class="app-description text-sm text-[var(--quantum-fg-secondary)] line-clamp-2 mb-3">{{ app.description }}</p>
              <div class="app-tags flex flex-wrap gap-1">
                <span v-for="tag in app.tags.slice(0, 3)" :key="tag" class="tag-badge px-2 py-1 rounded text-xs bg-[var(--quantum-bg-elevated)] text-[var(--quantum-fg-muted)]">
                  {{ tag }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 应用网格 -->
        <div class="quantum-card-hologram p-6 rounded-xl">
          <div class="apps-header flex justify-between items-center mb-4">
            <h2 class="section-title quantum-text-matrix text-xl font-semibold">
              {{ selectedCategory ? selectedCategory.name : '所有应用' }}
            </h2>
            <div class="view-toggle flex gap-1 bg-[var(--quantum-bg-elevated)] rounded p-1">
              <button 
                @click="viewMode = 'grid'"
                class="view-btn px-3 py-1 rounded text-sm transition-all"
                :class="viewMode === 'grid' ? 'bg-[var(--quantum-primary)] text-white' : 'text-[var(--quantum-fg-secondary)]'"
              >
                <i class="i-carbon-grid"></i>
              </button>
              <button 
                @click="viewMode = 'list'"
                class="view-btn px-3 py-1 rounded text-sm transition-all"
                :class="viewMode === 'list' ? 'bg-[var(--quantum-primary)] text-white' : 'text-[var(--quantum-fg-secondary)]'"
              >
                <i class="i-carbon-list"></i>
              </button>
            </div>
          </div>

          <div class="apps-grid" :class="viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 gap-4' : 'space-y-4'">
            <div 
              v-for="app in filteredApps" 
              :key="app.id"
              class="app-card quantum-card-hologram p-4 rounded-lg hover:border-[var(--quantum-border-glow)] transition-all cursor-pointer"
              :class="viewMode === 'list' ? 'flex items-center gap-4' : ''"
              @click="openAppDetails(app)"
            >
              <div class="app-icon quantum-energy-ring flex items-center justify-center" :class="viewMode === 'list' ? 'w-16 h-16 flex-shrink-0' : 'w-12 h-12 mb-3'">
                <img :src="app.icon" :alt="app.name" class="w-full h-full rounded-lg object-cover" />
              </div>
              
              <div class="app-content flex-1">
                <div class="app-header flex justify-between items-start mb-2">
                  <div class="app-info">
                    <h3 class="app-name text-lg font-semibold quantum-text-matrix mb-1">{{ app.name }}</h3>
                    <p class="app-developer text-sm text-[var(--quantum-fg-secondary)]">{{ app.developer }}</p>
                  </div>
                  <div class="app-price">
                    <span class="price-tag px-2 py-1 rounded text-sm font-semibold" :class="app.price === 'Free' ? 'bg-[var(--quantum-success)] text-white' : 'bg-[var(--quantum-primary)] text-white'">
                      {{ app.price }}
                    </span>
                  </div>
                </div>

                <div class="app-rating flex items-center gap-2 mb-2">
                  <div class="stars flex">
                    <i v-for="i in 5" :key="i" :class="i <= app.rating ? 'i-carbon-star-filled text-[var(--quantum-warning)]' : 'i-carbon-star text-[var(--quantum-fg-muted)]'" class="text-xs"></i>
                  </div>
                  <span class="rating-text text-xs text-[var(--quantum-fg-muted)]">{{ app.rating }} ({{ app.reviews }})</span>
                  <span class="downloads-text text-xs text-[var(--quantum-fg-muted)]">• {{ app.downloads }} 下载</span>
                </div>

                <p class="app-description text-sm text-[var(--quantum-fg-secondary)] line-clamp-2 mb-3">{{ app.description }}</p>

                <div class="app-footer flex justify-between items-center">
                  <div class="app-tags flex flex-wrap gap-1">
                    <span v-for="tag in app.tags.slice(0, 2)" :key="tag" class="tag-badge px-2 py-1 rounded text-xs bg-[var(--quantum-bg-elevated)] text-[var(--quantum-fg-muted)]">
                      {{ tag }}
                    </span>
                  </div>
                  <div class="app-actions flex gap-2">
                    <button @click.stop="installApp(app)" class="action-btn primary-btn text-sm py-1 px-3">
                      <i class="i-carbon-download"></i>
                      <span>{{ app.installed ? '已安装' : '安装' }}</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div class="pagination-section flex justify-center mt-6">
            <div class="pagination-controls flex items-center gap-2">
              <button class="pagination-btn action-btn secondary-btn text-sm" :disabled="currentPage === 1">
                <i class="i-carbon-chevron-left"></i>
              </button>
              <span class="pagination-info text-sm text-[var(--quantum-fg-secondary)]">
                第 {{ currentPage }} 页，共 {{ totalPages }} 页
              </span>
              <button class="pagination-btn action-btn secondary-btn text-sm" :disabled="currentPage === totalPages">
                <i class="i-carbon-chevron-right"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：推荐和统计 -->
      <div class="sidebar-section space-y-6">
        <!-- 热门应用 -->
        <div class="quantum-card-hologram p-4 rounded-xl">
          <h3 class="section-title quantum-text-matrix text-lg font-semibold mb-4">热门应用</h3>
          <div class="popular-apps space-y-3">
            <div 
              v-for="(app, index) in popularApps" 
              :key="app.id"
              class="popular-app flex items-center gap-3 p-2 rounded hover:bg-[var(--quantum-bg-elevated)] transition-all cursor-pointer"
              @click="openAppDetails(app)"
            >
              <div class="app-rank w-6 h-6 flex items-center justify-center bg-[var(--quantum-primary)] text-white rounded text-xs font-bold">
                {{ index + 1 }}
              </div>
              <div class="app-icon w-8 h-8 flex-shrink-0">
                <img :src="app.icon" :alt="app.name" class="w-full h-full rounded object-cover" />
              </div>
              <div class="app-info flex-1 min-w-0">
                <div class="app-name text-sm font-semibold truncate">{{ app.name }}</div>
                <div class="app-downloads text-xs text-[var(--quantum-fg-muted)]">{{ app.downloads }} 下载</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 最新更新 -->
        <div class="quantum-card-hologram p-4 rounded-xl">
          <h3 class="section-title quantum-text-matrix text-lg font-semibold mb-4">最新更新</h3>
          <div class="recent-updates space-y-3">
            <div 
              v-for="update in recentUpdates" 
              :key="update.id"
              class="update-item p-3 bg-[var(--quantum-bg-elevated)] rounded"
            >
              <div class="update-header flex items-center gap-2 mb-2">
                <div class="app-icon w-6 h-6">
                  <img :src="update.icon" :alt="update.name" class="w-full h-full rounded object-cover" />
                </div>
                <span class="app-name text-sm font-semibold">{{ update.name }}</span>
              </div>
              <div class="update-info text-xs text-[var(--quantum-fg-muted)]">
                <div>版本 {{ update.version }}</div>
                <div>{{ formatDate(update.updatedAt) }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 开发者工具 -->
        <div class="quantum-card-hologram p-4 rounded-xl">
          <h3 class="section-title quantum-text-matrix text-lg font-semibold mb-4">开发者工具</h3>
          <div class="developer-tools space-y-2">
            <button @click="openDeveloperConsole" class="action-btn secondary-btn w-full justify-center text-sm">
              <i class="i-carbon-code"></i>
              <span>开发者控制台</span>
            </button>
            <button @click="openAppBuilder" class="action-btn secondary-btn w-full justify-center text-sm">
              <i class="i-carbon-application"></i>
              <span>应用构建器</span>
            </button>
            <button @click="openDocumentation" class="action-btn secondary-btn w-full justify-center text-sm">
              <i class="i-carbon-document"></i>
              <span>开发文档</span>
            </button>
            <button @click="submitApp" class="action-btn primary-btn w-full justify-center text-sm">
              <i class="i-carbon-cloud-upload"></i>
              <span>提交应用</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 页面数据
const totalApps = ref(1247)
const featuredApps = ref(24)
const updatesAvailable = ref(8)
const lastSyncTime = ref(new Date().toLocaleTimeString())

// 筛选和搜索
const searchQuery = ref('')
const selectedCategory = ref(null)
const priceFilter = ref('')
const ratingFilter = ref('')
const compatibilityFilter = ref([])
const sortBy = ref('featured')
const viewMode = ref('grid')

// 分页
const currentPage = ref(1)
const pageSize = ref(12)

// 应用分类
const appCategories = ref([
  { id: 1, name: '全部应用', icon: 'i-carbon-application', count: 1247 },
  { id: 2, name: 'AR游戏', icon: 'i-carbon-game-console', count: 324 },
  { id: 3, name: '生产力工具', icon: 'i-carbon-tools', count: 189 },
  { id: 4, name: '教育学习', icon: 'i-carbon-education', count: 156 },
  { id: 5, name: '社交通讯', icon: 'i-carbon-chat', count: 98 },
  { id: 6, name: '娱乐媒体', icon: 'i-carbon-video', count: 234 },
  { id: 7, name: '健康医疗', icon: 'i-carbon-health-cross', count: 67 },
  { id: 8, name: '商务办公', icon: 'i-carbon-business', count: 123 },
  { id: 9, name: '开发工具', icon: 'i-carbon-development', count: 56 }
])

// 精选应用
const featuredAppsList = ref([
  {
    id: 1,
    name: 'AR Builder Pro',
    developer: 'TechCorp',
    description: '专业的AR场景构建工具，支持实时协作和云端同步，让AR内容创作变得简单高效。',
    icon: '/apps/ar-builder.jpg',
    price: '$29.99',
    rating: 5,
    reviews: 1234,
    downloads: '50K+',
    tags: ['生产力', 'AR开发', '协作'],
    installed: false
  },
  {
    id: 2,
    name: 'Quantum Visualizer',
    developer: 'DataViz Inc',
    description: '量子数据可视化平台，将复杂数据转换为直观的3D AR展示，支持实时数据流。',
    icon: '/apps/quantum-viz.jpg',
    price: 'Free',
    rating: 4,
    reviews: 892,
    downloads: '100K+',
    tags: ['数据可视化', '免费', '企业'],
    installed: true
  }
])

// 应用列表
const applications = ref([
  {
    id: 1,
    name: 'AR Builder Pro',
    developer: 'TechCorp',
    description: '专业的AR场景构建工具，支持实时协作和云端同步。',
    icon: '/apps/ar-builder.jpg',
    price: '$29.99',
    rating: 5,
    reviews: 1234,
    downloads: '50K+',
    category: 'productivity',
    tags: ['生产力', 'AR开发', '协作'],
    installed: false,
    compatibility: ['ar-glasses', 'mobile']
  },
  {
    id: 2,
    name: 'Quantum Visualizer',
    developer: 'DataViz Inc',
    description: '量子数据可视化平台，将复杂数据转换为直观的3D AR展示。',
    icon: '/apps/quantum-viz.jpg',
    price: 'Free',
    rating: 4,
    reviews: 892,
    downloads: '100K+',
    category: 'productivity',
    tags: ['数据可视化', '免费', '企业'],
    installed: true,
    compatibility: ['ar-glasses', 'desktop']
  },
  {
    id: 3,
    name: 'AR Chess Master',
    developer: 'GameStudio',
    description: '沉浸式AR国际象棋游戏，支持AI对战和在线多人模式。',
    icon: '/apps/ar-chess.jpg',
    price: '$9.99',
    rating: 4,
    reviews: 567,
    downloads: '25K+',
    category: 'games',
    tags: ['游戏', '策略', '多人'],
    installed: false,
    compatibility: ['ar-glasses', 'mobile']
  },
  {
    id: 4,
    name: 'Language Immersion',
    developer: 'EduTech',
    description: 'AR语言学习应用，通过虚拟环境提供沉浸式语言学习体验。',
    icon: '/apps/language.jpg',
    price: '$19.99',
    rating: 5,
    reviews: 2341,
    downloads: '75K+',
    category: 'education',
    tags: ['教育', '语言学习', 'AI'],
    installed: false,
    compatibility: ['ar-glasses', 'mobile', 'desktop']
  }
])

// 热门应用
const popularApps = ref([
  { id: 1, name: 'AR Builder Pro', icon: '/apps/ar-builder.jpg', downloads: '50K+' },
  { id: 2, name: 'Quantum Visualizer', icon: '/apps/quantum-viz.jpg', downloads: '100K+' },
  { id: 3, name: 'Language Immersion', icon: '/apps/language.jpg', downloads: '75K+' },
  { id: 4, name: 'AR Chess Master', icon: '/apps/ar-chess.jpg', downloads: '25K+' },
  { id: 5, name: 'Virtual Workspace', icon: '/apps/workspace.jpg', downloads: '30K+' }
])

// 最新更新
const recentUpdates = ref([
  {
    id: 1,
    name: 'AR Builder Pro',
    icon: '/apps/ar-builder.jpg',
    version: '2.1.0',
    updatedAt: new Date('2024-06-14T10:30:00')
  },
  {
    id: 2,
    name: 'Quantum Visualizer',
    icon: '/apps/quantum-viz.jpg',
    version: '1.5.2',
    updatedAt: new Date('2024-06-13T15:20:00')
  },
  {
    id: 3,
    name: 'Language Immersion',
    icon: '/apps/language.jpg',
    version: '3.0.1',
    updatedAt: new Date('2024-06-12T09:15:00')
  }
])

// 计算属性
const filteredApps = computed(() => {
  let filtered = applications.value

  // 分类筛选
  if (selectedCategory.value && selectedCategory.value.id !== 1) {
    const categoryMap = {
      2: 'games',
      3: 'productivity',
      4: 'education',
      5: 'social',
      6: 'entertainment',
      7: 'health',
      8: 'business',
      9: 'development'
    }
    const categoryKey = categoryMap[selectedCategory.value.id]
    if (categoryKey) {
      filtered = filtered.filter(app => app.category === categoryKey)
    }
  }

  // 价格筛选
  if (priceFilter.value) {
    if (priceFilter.value === 'free') {
      filtered = filtered.filter(app => app.price === 'Free')
    } else if (priceFilter.value === 'paid') {
      filtered = filtered.filter(app => app.price !== 'Free')
    }
  }

  // 评分筛选
  if (ratingFilter.value) {
    const minRating = parseInt(ratingFilter.value.replace('+', ''))
    filtered = filtered.filter(app => app.rating >= minRating)
  }

  // 兼容性筛选
  if (compatibilityFilter.value.length > 0) {
    filtered = filtered.filter(app =>
      compatibilityFilter.value.some(filter => app.compatibility.includes(filter))
    )
  }

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(app =>
      app.name.toLowerCase().includes(query) ||
      app.developer.toLowerCase().includes(query) ||
      app.description.toLowerCase().includes(query) ||
      app.tags.some(tag => tag.toLowerCase().includes(query))
    )
  }

  // 排序
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'rating':
        return b.rating - a.rating
      case 'downloads':
        return parseInt(b.downloads.replace(/[^\d]/g, '')) - parseInt(a.downloads.replace(/[^\d]/g, ''))
      case 'newest':
        return new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime()
      case 'updated':
        return new Date(b.updatedAt || 0).getTime() - new Date(a.updatedAt || 0).getTime()
      default:
        return 0
    }
  })

  return filtered
})

const totalPages = computed(() => Math.ceil(filteredApps.value.length / pageSize.value))

// 方法
const selectCategory = (category: any) => {
  selectedCategory.value = selectedCategory.value?.id === category.id ? null : category
}

const formatDate = (date: Date) => {
  return date.toLocaleDateString('zh-CN')
}

const refreshStore = () => {
  console.log('刷新应用商店')
}

const checkUpdates = () => {
  console.log('检查应用更新')
}

const uploadApp = () => {
  console.log('上传新应用')
}

const openAppDetails = (app: any) => {
  console.log('打开应用详情', app.name)
}

const installApp = (app: any) => {
  if (app.installed) {
    console.log('应用已安装', app.name)
  } else {
    console.log('安装应用', app.name)
    app.installed = true
  }
}

const openDeveloperConsole = () => {
  console.log('打开开发者控制台')
}

const openAppBuilder = () => {
  console.log('打开应用构建器')
}

const openDocumentation = () => {
  console.log('打开开发文档')
}

const submitApp = () => {
  console.log('提交应用到商店')
}

onMounted(() => {
  // 页面初始化
})
</script>

<style scoped>
/* 面包屑导航 */
.breadcrumb-link {
  transition: color var(--quantum-transition-fast);
}

/* 分类项 */
.category-item {
  transition: all var(--quantum-transition-fast);
  border-left: 3px solid transparent;
}

.category-item:hover {
  border-left-color: var(--quantum-primary);
  transform: translateX(2px);
}

/* 应用卡片 */
.app-card,
.featured-app {
  position: relative;
  overflow: hidden;
  transition: all var(--quantum-transition-fast);
}

.app-card::before,
.featured-app::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--quantum-primary), var(--quantum-accent));
  opacity: 0;
  transition: opacity var(--quantum-transition-fast);
}

.app-card:hover::before,
.featured-app:hover::before {
  opacity: 1;
}

.app-card:hover,
.featured-app:hover {
  transform: translateY(-2px);
  box-shadow: var(--quantum-glow-primary);
}

/* 热门应用项 */
.popular-app {
  transition: all var(--quantum-transition-fast);
  border-left: 3px solid transparent;
}

.popular-app:hover {
  border-left-color: var(--quantum-accent);
  transform: translateX(2px);
}

/* 更新项 */
.update-item {
  transition: all var(--quantum-transition-fast);
  border-left: 3px solid transparent;
}

.update-item:hover {
  border-left-color: var(--quantum-primary);
  transform: translateX(2px);
}

/* 搜索输入框 */
.search-input,
.sort-select {
  transition: border-color var(--quantum-transition-fast);
}

.search-input:focus,
.sort-select:focus {
  outline: none;
  border-color: var(--quantum-primary);
  box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.1);
}

/* 视图切换按钮 */
.view-btn {
  transition: all var(--quantum-transition-fast);
}

/* 价格标签 */
.price-tag {
  font-weight: 500;
}

/* 标签徽章 */
.tag-badge {
  transition: all var(--quantum-transition-fast);
}

.tag-badge:hover {
  background: var(--quantum-primary);
  color: white;
}

/* 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 分页按钮 */
.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .store-layout {
    grid-template-columns: 1fr;
  }

  .search-controls {
    grid-template-columns: 1fr;
  }

  .featured-apps {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .apps-grid.grid {
    grid-template-columns: 1fr;
  }

  .app-card.flex {
    flex-direction: column;
    align-items: flex-start;
  }

  .app-icon {
    width: 3rem !important;
    height: 3rem !important;
    margin-bottom: 0.75rem;
  }
}
</style>
