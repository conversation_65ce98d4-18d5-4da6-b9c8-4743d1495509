<template>
  <div class="quantum-applications-page">
    <!-- 🌌 量子应用管理页面标题 -->
    <div class="page-header quantum-data-stream quantum-matrix-bg">
      <div class="header-content">
        <h1 class="page-title quantum-text-neon quantum-glow-effect">
          📱 QUANTUM APPLICATION MANAGEMENT HUB
        </h1>
        <p class="page-subtitle quantum-text-glow">
          >> AR APPLICATION DEPLOYMENT & LIFECYCLE CONTROL MATRIX <<
        </p>
        <div class="quantum-status-bar">
          <span class="quantum-hud-element">📦 APPS: {{ totalApps }}</span>
          <span class="quantum-hud-element">🚀 DEPLOYED: {{ deployedApps }}</span>
          <span class="quantum-hud-element">🔄 UPDATING: {{ updatingApps }}</span>
          <span class="quantum-hud-element">⚡ SYNC: {{ lastSyncTime }}</span>
        </div>
      </div>
      <div class="header-actions">
        <button @click="refreshApps" class="quantum-btn-pulse quantum-glow-effect">
          <i class="i-carbon-refresh"></i>
          <span>QUANTUM REFRESH</span>
        </button>
        <button @click="deployApp" class="quantum-btn-pulse quantum-border-energy">
          <i class="i-carbon-cloud-upload"></i>
          <span>DEPLOY APP</span>
        </button>
      </div>
    </div>

    <!-- 📊 量子应用统计面板 -->
    <div class="quantum-metrics-grid mb-6">
      <div v-for="metric in appMetrics" :key="metric.id"
           class="quantum-card-hologram quantum-metric-card p-4 rounded-xl">
        <div class="quantum-metric-header flex justify-between items-start mb-4">
          <div class="quantum-energy-ring quantum-metric-ring w-12 h-12">
            <i :class="metric.icon" class="quantum-metric-icon text-lg"></i>
          </div>
          <div class="quantum-metric-status">
            <span class="quantum-hud-element text-xs px-2 py-1 rounded">{{ metric.status }}</span>
          </div>
        </div>
        <div class="quantum-metric-content text-center">
          <div class="quantum-metric-value quantum-text-glow text-2xl font-bold mb-2">
            {{ metric.value }}
          </div>
          <div class="quantum-metric-label text-sm text-[var(--quantum-fg-secondary)] mb-3">
            {{ metric.label }}
          </div>
          <div class="quantum-metric-progress mb-2">
            <div class="quantum-progress-bar h-2 rounded-full overflow-hidden">
              <div class="quantum-progress-fill h-full transition-all duration-500"
                   :style="{ width: `${metric.progress}%` }"></div>
            </div>
          </div>
          <div class="quantum-metric-change text-sm flex items-center justify-center gap-1" 
               :class="metric.changeType">
            <i :class="getChangeIcon(metric.changeType)" class="text-xs"></i>
            <span>{{ Math.abs(metric.change) }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 🔍 量子应用搜索与筛选 -->
    <div class="quantum-card-hologram quantum-search-panel p-4 mb-6 rounded-xl">
      <div class="search-controls flex flex-wrap gap-4 items-center">
        <div class="search-input-group flex-1 min-w-64">
          <input 
            v-model="searchQuery"
            type="text"
            placeholder="Search applications by name, category, or developer..."
            class="quantum-input w-full"
          />
        </div>
        <div class="filter-controls flex gap-2">
          <select v-model="statusFilter" class="quantum-select">
            <option value="">All Status</option>
            <option value="deployed">Deployed</option>
            <option value="updating">Updating</option>
            <option value="stopped">Stopped</option>
            <option value="error">Error</option>
          </select>
          <select v-model="categoryFilter" class="quantum-select">
            <option value="">All Categories</option>
            <option value="gaming">Gaming</option>
            <option value="education">Education</option>
            <option value="productivity">Productivity</option>
            <option value="entertainment">Entertainment</option>
            <option value="utility">Utility</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 📱 量子应用网格 -->
    <div class="quantum-apps-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-6">
      <div v-for="app in filteredApps" :key="app.id"
           class="quantum-card-hologram quantum-app-card p-4 rounded-xl cursor-pointer"
           @click="selectApp(app)">
        <div class="app-header flex justify-between items-start mb-3">
          <div class="app-icon quantum-energy-ring w-12 h-12 p-2">
            <img :src="app.icon" :alt="app.name" class="w-full h-full object-cover rounded" />
          </div>
          <div class="app-status">
            <span class="quantum-hud-element text-xs px-2 py-1 rounded" 
                  :class="getStatusClass(app.status)">
              {{ app.status.toUpperCase() }}
            </span>
          </div>
        </div>
        
        <div class="app-info">
          <h3 class="app-name quantum-text-matrix text-lg font-semibold mb-1">
            {{ app.name }}
          </h3>
          <p class="app-developer text-sm text-[var(--quantum-fg-muted)] mb-2">
            by {{ app.developer }}
          </p>
          <p class="app-category text-sm text-[var(--quantum-fg-secondary)] mb-3">
            {{ app.category }} • v{{ app.version }}
          </p>
        </div>

        <div class="app-metrics grid grid-cols-2 gap-2 mb-3">
          <div class="metric-item text-center p-2 rounded bg-[var(--quantum-bg-elevated)]">
            <div class="metric-value text-sm font-bold quantum-text-glow">{{ app.downloads }}</div>
            <div class="metric-label text-xs text-[var(--quantum-fg-muted)]">DOWNLOADS</div>
          </div>
          <div class="metric-item text-center p-2 rounded bg-[var(--quantum-bg-elevated)]">
            <div class="metric-value text-sm font-bold quantum-text-glow">{{ app.rating }}★</div>
            <div class="metric-label text-xs text-[var(--quantum-fg-muted)]">RATING</div>
          </div>
        </div>

        <div class="app-actions flex gap-2">
          <button @click.stop="manageApp(app)" 
                  class="quantum-btn-pulse flex-1 text-xs py-1">
            <i class="i-carbon-settings mr-1"></i>
            MANAGE
          </button>
          <button @click.stop="viewAnalytics(app)" 
                  class="quantum-btn-secondary flex-1 text-xs py-1">
            <i class="i-carbon-analytics mr-1"></i>
            ANALYTICS
          </button>
        </div>
      </div>
    </div>

    <!-- 📊 量子应用详情面板 -->
    <div v-if="selectedApp" class="quantum-card-hologram quantum-app-details p-6 rounded-xl">
      <div class="details-header flex justify-between items-center mb-6">
        <h2 class="quantum-text-neon text-xl font-bold">
          APPLICATION DETAILS: {{ selectedApp.name }}
        </h2>
        <button @click="selectedApp = null" class="quantum-btn-secondary">
          <i class="i-carbon-close"></i>
          CLOSE
        </button>
      </div>
      
      <div class="details-content grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="app-info-panel">
          <h3 class="quantum-text-matrix text-lg font-semibold mb-4">APPLICATION INFORMATION</h3>
          <div class="info-grid space-y-3">
            <div class="info-item flex justify-between">
              <span class="label text-[var(--quantum-fg-secondary)]">App ID:</span>
              <span class="value quantum-text-glow">{{ selectedApp.id }}</span>
            </div>
            <div class="info-item flex justify-between">
              <span class="label text-[var(--quantum-fg-secondary)]">Version:</span>
              <span class="value quantum-text-glow">{{ selectedApp.version }}</span>
            </div>
            <div class="info-item flex justify-between">
              <span class="label text-[var(--quantum-fg-secondary)]">Size:</span>
              <span class="value quantum-text-glow">{{ selectedApp.size }}</span>
            </div>
            <div class="info-item flex justify-between">
              <span class="label text-[var(--quantum-fg-secondary)]">Last Updated:</span>
              <span class="value quantum-text-glow">{{ selectedApp.lastUpdated }}</span>
            </div>
            <div class="info-item flex justify-between">
              <span class="label text-[var(--quantum-fg-secondary)]">Active Users:</span>
              <span class="value quantum-text-glow">{{ selectedApp.activeUsers }}</span>
            </div>
          </div>
        </div>
        
        <div class="app-controls-panel">
          <h3 class="quantum-text-matrix text-lg font-semibold mb-4">APPLICATION CONTROLS</h3>
          <div class="controls-grid space-y-3">
            <button class="quantum-btn-pulse w-full justify-center">
              <i class="i-carbon-play mr-2"></i>
              START APPLICATION
            </button>
            <button class="quantum-btn-secondary w-full justify-center">
              <i class="i-carbon-stop mr-2"></i>
              STOP APPLICATION
            </button>
            <button class="quantum-btn-secondary w-full justify-center">
              <i class="i-carbon-restart mr-2"></i>
              RESTART APPLICATION
            </button>
            <button class="quantum-btn-secondary w-full justify-center">
              <i class="i-carbon-update-now mr-2"></i>
              UPDATE APPLICATION
            </button>
            <button class="quantum-btn-secondary w-full justify-center">
              <i class="i-carbon-trash-can mr-2"></i>
              UNINSTALL APPLICATION
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 页面元数据
useHead({
  title: 'Quantum Application Management - AR System Dashboard',
  meta: [
    { name: 'description', content: 'Quantum AR application management and deployment center' }
  ]
})

// 应用数据
const applications = ref([
  {
    id: 'APP-001',
    name: 'AR Explorer',
    developer: 'QuantumSoft',
    category: 'education',
    version: '2.1.0',
    status: 'deployed',
    downloads: '12.5K',
    rating: 4.8,
    size: '245 MB',
    lastUpdated: '2 days ago',
    activeUsers: 1250,
    icon: '/avatar-placeholder.svg'
  },
  {
    id: 'APP-002',
    name: 'Quantum Games',
    developer: 'AR Studios',
    category: 'gaming',
    version: '1.5.2',
    status: 'updating',
    downloads: '8.2K',
    rating: 4.6,
    size: '512 MB',
    lastUpdated: '1 week ago',
    activeUsers: 820,
    icon: '/avatar-placeholder.svg'
  },
  {
    id: 'APP-003',
    name: 'AR Workspace',
    developer: 'ProductiveTech',
    category: 'productivity',
    version: '3.0.1',
    status: 'deployed',
    downloads: '15.7K',
    rating: 4.9,
    size: '180 MB',
    lastUpdated: '3 days ago',
    activeUsers: 1570,
    icon: '/avatar-placeholder.svg'
  },
  {
    id: 'APP-004',
    name: 'AR Cinema',
    developer: 'MediaVision',
    category: 'entertainment',
    version: '2.3.0',
    status: 'stopped',
    downloads: '6.1K',
    rating: 4.4,
    size: '890 MB',
    lastUpdated: '1 month ago',
    activeUsers: 610,
    icon: '/avatar-placeholder.svg'
  }
])

// 搜索和筛选
const searchQuery = ref('')
const statusFilter = ref('')
const categoryFilter = ref('')
const selectedApp = ref(null)

// 应用统计指标
const appMetrics = ref([
  {
    id: 1,
    label: 'Total Applications',
    value: 156,
    icon: 'i-carbon-application',
    status: 'ACTIVE',
    progress: 92,
    change: 15,
    changeType: 'positive'
  },
  {
    id: 2,
    label: 'Deployed Apps',
    value: 142,
    icon: 'i-carbon-cloud-upload',
    status: 'RUNNING',
    progress: 91,
    change: 8,
    changeType: 'positive'
  },
  {
    id: 3,
    label: 'Total Downloads',
    value: '2.4M',
    icon: 'i-carbon-download',
    status: 'GROWING',
    progress: 85,
    change: 23,
    changeType: 'positive'
  },
  {
    id: 4,
    label: 'Average Rating',
    value: '4.7★',
    icon: 'i-carbon-star-filled',
    status: 'EXCELLENT',
    progress: 94,
    change: 3,
    changeType: 'positive'
  }
])

// 计算属性
const totalApps = computed(() => applications.value.length)
const deployedApps = computed(() => applications.value.filter(a => a.status === 'deployed').length)
const updatingApps = computed(() => applications.value.filter(a => a.status === 'updating').length)
const lastSyncTime = computed(() => new Date().toLocaleTimeString())

const filteredApps = computed(() => {
  return applications.value.filter(app => {
    const matchesSearch = !searchQuery.value || 
      app.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      app.developer.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      app.category.toLowerCase().includes(searchQuery.value.toLowerCase())
    
    const matchesStatus = !statusFilter.value || app.status === statusFilter.value
    const matchesCategory = !categoryFilter.value || app.category === categoryFilter.value
    
    return matchesSearch && matchesStatus && matchesCategory
  })
})

// 方法
const getStatusClass = (status: string) => {
  const classes = {
    'deployed': 'quantum-status-deployed',
    'updating': 'quantum-status-updating',
    'stopped': 'quantum-status-stopped',
    'error': 'quantum-status-error'
  }
  return classes[status] || ''
}

const getChangeIcon = (changeType: string) => {
  return changeType === 'positive' ? 'i-carbon-arrow-up' : 'i-carbon-arrow-down'
}

const refreshApps = () => {
  console.log('Refreshing applications...')
}

const deployApp = () => {
  console.log('Deploying new application...')
}

const selectApp = (app: any) => {
  selectedApp.value = app
}

const manageApp = (app: any) => {
  console.log('Managing application:', app.id)
}

const viewAnalytics = (app: any) => {
  console.log('Viewing analytics for:', app.id)
}

onMounted(() => {
  console.log('Quantum Application Management Hub initialized')
})
</script>

<style scoped>
.quantum-applications-page {
  padding: var(--space-6);
  min-height: 100vh;
}

.quantum-apps-grid {
  animation: quantumFadeIn 0.6s ease-out;
}

.quantum-app-card {
  transition: all var(--transition-normal);
  border: 1px solid var(--quantum-border-color);
}

.quantum-app-card:hover {
  transform: translateY(-4px);
  border-color: var(--quantum-primary);
  box-shadow: var(--quantum-glow-primary);
}

.quantum-status-deployed {
  background: rgba(0, 255, 65, 0.2);
  border-color: var(--quantum-accent);
  color: var(--quantum-accent);
}

.quantum-status-updating {
  background: rgba(255, 170, 0, 0.2);
  border-color: var(--quantum-warning);
  color: var(--quantum-warning);
}

.quantum-status-stopped {
  background: rgba(255, 71, 87, 0.2);
  border-color: var(--quantum-error);
  color: var(--quantum-error);
}

.quantum-status-error {
  background: rgba(255, 71, 87, 0.3);
  border-color: var(--quantum-error);
  color: var(--quantum-error);
}

@keyframes quantumFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
