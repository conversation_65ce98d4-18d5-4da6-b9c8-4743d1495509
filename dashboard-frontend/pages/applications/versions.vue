<template>
  <div class="quantum-versions-page">
    <!-- 🌌 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title quantum-text-neon">
          🔄 QUANTUM VERSION MANAGEMENT
        </h1>
        <p class="page-subtitle quantum-text-glow">
          >> APPLICATION VERSION CONTROL & RELEASE MANAGEMENT <<
        </p>
        <div class="quantum-status-bar">
          <span class="quantum-hud-element">
            <i class="i-carbon-version"></i>
            VERSIONS: {{ totalVersions }}
          </span>
          <span class="quantum-hud-element">
            <i class="i-carbon-deployment-unit-technical"></i>
            DEPLOYED: {{ deployedVersions }}
          </span>
          <span class="quantum-hud-element">
            <i class="i-carbon-in-progress"></i>
            PENDING: {{ pendingVersions }}
          </span>
          <span class="quantum-hud-element">
            <i class="i-carbon-time"></i>
            SYNC: {{ lastSyncTime }}
          </span>
        </div>
      </div>
      <div class="header-actions">
        <button @click="rollbackVersion" class="quantum-btn-secondary quantum-ripple">
          <i class="i-carbon-undo"></i>
          <span>版本回滚</span>
        </button>
        <button @click="createNewVersion" class="quantum-btn-primary quantum-ripple">
          <i class="i-carbon-add"></i>
          <span>创建版本</span>
        </button>
      </div>
    </div>

    <!-- 📊 版本统计概览 -->
    <div class="quantum-metrics-grid">
      <div v-for="metric in versionMetrics" :key="metric.id"
           class="quantum-card-hologram quantum-metric-card">
        <div class="quantum-metric-header">
          <div class="quantum-metric-ring">
            <i :class="metric.icon" class="quantum-metric-icon"></i>
          </div>
          <div class="quantum-metric-status">
            <span class="quantum-hud-element">{{ metric.status }}</span>
          </div>
        </div>
        <div class="quantum-metric-content">
          <div class="quantum-metric-value quantum-text-glow">
            {{ metric.value }}
          </div>
          <div class="quantum-metric-label">
            {{ metric.label }}
          </div>
          <div class="quantum-metric-progress">
            <div class="quantum-progress-bar">
              <div class="quantum-progress-fill" :style="{ width: `${metric.progress}%` }"></div>
            </div>
          </div>
          <div class="quantum-metric-change" :class="metric.changeType">
            <i :class="getChangeIcon(metric.changeType)"></i>
            <span>{{ Math.abs(metric.change) }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 🔍 版本筛选和搜索 -->
    <div class="quantum-card-hologram filters-panel">
      <div class="filters-header">
        <h2 class="quantum-text-matrix">版本筛选器</h2>
        <button @click="resetFilters" class="quantum-btn-ghost">
          <i class="i-carbon-reset"></i>
          重置筛选
        </button>
      </div>
      
      <div class="filters-grid">
        <div class="filter-group">
          <label class="filter-label">应用名称</label>
          <select v-model="selectedApp" class="quantum-select">
            <option value="">所有应用</option>
            <option v-for="app in applications" :key="app.id" :value="app.id">
              {{ app.name }}
            </option>
          </select>
        </div>
        
        <div class="filter-group">
          <label class="filter-label">版本状态</label>
          <select v-model="statusFilter" class="quantum-select">
            <option value="">所有状态</option>
            <option value="development">开发中</option>
            <option value="testing">测试中</option>
            <option value="staging">预发布</option>
            <option value="production">生产环境</option>
            <option value="deprecated">已废弃</option>
          </select>
        </div>
        
        <div class="filter-group">
          <label class="filter-label">版本类型</label>
          <select v-model="typeFilter" class="quantum-select">
            <option value="">所有类型</option>
            <option value="major">主版本</option>
            <option value="minor">次版本</option>
            <option value="patch">补丁版本</option>
            <option value="hotfix">热修复</option>
          </select>
        </div>
        
        <div class="filter-group">
          <label class="filter-label">搜索版本</label>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索版本号或描述..."
            class="quantum-input"
          />
        </div>
      </div>
    </div>

    <!-- 📋 版本管理区域 -->
    <div class="versions-layout">
      <!-- 版本列表 -->
      <div class="versions-section">
        <div class="section-header">
          <h2 class="quantum-text-neon">版本列表</h2>
          <div class="view-controls">
            <select v-model="sortBy" class="quantum-select">
              <option value="newest">最新版本</option>
              <option value="oldest">最旧版本</option>
              <option value="name">版本号</option>
              <option value="status">状态</option>
            </select>
          </div>
        </div>
        
        <div class="versions-timeline">
          <div v-for="version in filteredVersions" :key="version.id"
               class="version-item"
               @click="selectVersion(version)">
            <div class="version-marker" :class="version.status">
              <i :class="getStatusIcon(version.status)"></i>
            </div>
            
            <div class="version-content">
              <div class="version-header">
                <div class="version-info">
                  <h3 class="version-number quantum-text-matrix">{{ version.number }}</h3>
                  <p class="version-app">{{ version.appName }}</p>
                  <div class="version-meta">
                    <span class="version-type" :class="version.type">{{ getTypeLabel(version.type) }}</span>
                    <span class="version-date">{{ formatDate(version.createdAt) }}</span>
                    <span class="version-author">{{ version.author }}</span>
                  </div>
                </div>
                <div class="version-status">
                  <span class="quantum-hud-element" :class="getStatusClass(version.status)">
                    {{ version.status.toUpperCase() }}
                  </span>
                </div>
              </div>
              
              <div class="version-description">
                <p>{{ version.description }}</p>
              </div>
              
              <div class="version-changes">
                <h4 class="changes-title">主要变更</h4>
                <ul class="changes-list">
                  <li v-for="change in version.changes.slice(0, 3)" :key="change">
                    <i class="i-carbon-checkmark"></i>
                    {{ change }}
                  </li>
                  <li v-if="version.changes.length > 3" class="more-changes">
                    +{{ version.changes.length - 3 }} 更多变更...
                  </li>
                </ul>
              </div>
              
              <div class="version-actions">
                <button v-if="version.status === 'development'" 
                        @click.stop="promoteVersion(version)" 
                        class="quantum-btn-ghost">
                  <i class="i-carbon-arrow-up"></i>
                  推进到测试
                </button>
                <button v-if="version.status === 'testing'" 
                        @click.stop="promoteVersion(version)" 
                        class="quantum-btn-ghost">
                  <i class="i-carbon-arrow-up"></i>
                  推进到预发布
                </button>
                <button v-if="version.status === 'staging'" 
                        @click.stop="deployVersion(version)" 
                        class="quantum-btn-primary">
                  <i class="i-carbon-deployment-unit-technical"></i>
                  部署到生产
                </button>
                <button v-if="version.status === 'production'" 
                        @click.stop="rollbackToVersion(version)" 
                        class="quantum-btn-secondary">
                  <i class="i-carbon-undo"></i>
                  回滚到此版本
                </button>
                <button @click.stop="downloadVersion(version)" class="quantum-btn-ghost">
                  <i class="i-carbon-download"></i>
                  下载
                </button>
                <button @click.stop="deleteVersion(version)" class="quantum-btn-ghost text-red-400">
                  <i class="i-carbon-trash-can"></i>
                  删除
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 版本详情侧边栏 -->
      <div v-if="selectedVersion" class="version-details-sidebar">
        <div class="quantum-card-hologram version-details">
          <div class="details-header">
            <button @click="closeDetails" class="close-btn">
              <i class="i-carbon-close"></i>
            </button>
            <div class="version-title-section">
              <h2 class="version-title quantum-text-neon">{{ selectedVersion.number }}</h2>
              <p class="version-app-name">{{ selectedVersion.appName }}</p>
              <div class="version-status-section">
                <span class="quantum-hud-element" :class="getStatusClass(selectedVersion.status)">
                  {{ selectedVersion.status.toUpperCase() }}
                </span>
                <span class="version-type-badge" :class="selectedVersion.type">
                  {{ getTypeLabel(selectedVersion.type) }}
                </span>
              </div>
            </div>
          </div>
          
          <div class="details-content">
            <div class="version-overview">
              <h3 class="section-title">版本概览</h3>
              <div class="overview-grid">
                <div class="overview-item">
                  <span class="item-label">创建时间:</span>
                  <span class="item-value">{{ formatDateTime(selectedVersion.createdAt) }}</span>
                </div>
                <div class="overview-item">
                  <span class="item-label">作者:</span>
                  <span class="item-value">{{ selectedVersion.author }}</span>
                </div>
                <div class="overview-item">
                  <span class="item-label">文件大小:</span>
                  <span class="item-value">{{ selectedVersion.fileSize }}</span>
                </div>
                <div class="overview-item">
                  <span class="item-label">下载次数:</span>
                  <span class="item-value">{{ selectedVersion.downloads }}</span>
                </div>
              </div>
            </div>
            
            <div class="version-description-section">
              <h3 class="section-title">版本描述</h3>
              <p class="version-full-description">{{ selectedVersion.fullDescription }}</p>
            </div>
            
            <div class="version-changes-section">
              <h3 class="section-title">完整变更日志</h3>
              <div class="changes-categories">
                <div v-if="selectedVersion.features?.length" class="change-category">
                  <h4 class="category-title">新功能</h4>
                  <ul class="category-list">
                    <li v-for="feature in selectedVersion.features" :key="feature">
                      <i class="i-carbon-add"></i>
                      {{ feature }}
                    </li>
                  </ul>
                </div>
                
                <div v-if="selectedVersion.improvements?.length" class="change-category">
                  <h4 class="category-title">改进</h4>
                  <ul class="category-list">
                    <li v-for="improvement in selectedVersion.improvements" :key="improvement">
                      <i class="i-carbon-upgrade"></i>
                      {{ improvement }}
                    </li>
                  </ul>
                </div>
                
                <div v-if="selectedVersion.bugfixes?.length" class="change-category">
                  <h4 class="category-title">修复</h4>
                  <ul class="category-list">
                    <li v-for="bugfix in selectedVersion.bugfixes" :key="bugfix">
                      <i class="i-carbon-checkmark"></i>
                      {{ bugfix }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            
            <div class="version-dependencies">
              <h3 class="section-title">依赖关系</h3>
              <div class="dependencies-list">
                <div v-for="dep in selectedVersion.dependencies" :key="dep.name"
                     class="dependency-item">
                  <span class="dependency-name">{{ dep.name }}</span>
                  <span class="dependency-version">{{ dep.version }}</span>
                </div>
              </div>
            </div>
            
            <div class="version-deployment">
              <h3 class="section-title">部署信息</h3>
              <div class="deployment-environments">
                <div v-for="env in selectedVersion.deployments" :key="env.name"
                     class="environment-item">
                  <div class="env-header">
                    <span class="env-name">{{ env.name }}</span>
                    <span class="env-status" :class="env.status">{{ env.status }}</span>
                  </div>
                  <div class="env-details">
                    <span class="env-url">{{ env.url }}</span>
                    <span class="env-date">{{ formatDate(env.deployedAt) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="details-footer">
            <div class="version-actions-full">
              <button @click="downloadVersion(selectedVersion)" class="quantum-btn-secondary">
                <i class="i-carbon-download"></i>
                下载版本
              </button>
              <button v-if="canPromote(selectedVersion)" 
                      @click="promoteVersion(selectedVersion)" 
                      class="quantum-btn-primary">
                <i class="i-carbon-arrow-up"></i>
                推进版本
              </button>
              <button v-if="canDeploy(selectedVersion)" 
                      @click="deployVersion(selectedVersion)" 
                      class="quantum-btn-primary">
                <i class="i-carbon-deployment-unit-technical"></i>
                部署版本
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 📊 版本创建向导 -->
    <div v-if="showCreateWizard" class="quantum-card-hologram create-wizard">
      <div class="wizard-header">
        <h2 class="quantum-text-neon">创建新版本</h2>
        <button @click="closeCreateWizard" class="close-btn">
          <i class="i-carbon-close"></i>
        </button>
      </div>
      
      <div class="wizard-content">
        <div class="wizard-form">
          <div class="form-group">
            <label class="form-label">选择应用</label>
            <select v-model="newVersion.appId" class="quantum-select">
              <option value="">请选择应用</option>
              <option v-for="app in applications" :key="app.id" :value="app.id">
                {{ app.name }}
              </option>
            </select>
          </div>
          
          <div class="form-group">
            <label class="form-label">版本号</label>
            <input v-model="newVersion.number" type="text" class="quantum-input" 
                   placeholder="例如: 1.2.3" />
          </div>
          
          <div class="form-group">
            <label class="form-label">版本类型</label>
            <select v-model="newVersion.type" class="quantum-select">
              <option value="major">主版本 (重大更新)</option>
              <option value="minor">次版本 (新功能)</option>
              <option value="patch">补丁版本 (修复)</option>
              <option value="hotfix">热修复 (紧急修复)</option>
            </select>
          </div>
          
          <div class="form-group">
            <label class="form-label">版本描述</label>
            <textarea v-model="newVersion.description" class="quantum-textarea" 
                      rows="4" placeholder="描述此版本的主要变更..."></textarea>
          </div>
          
          <div class="form-group">
            <label class="form-label">变更日志</label>
            <div class="changelog-editor">
              <div class="changelog-section">
                <h4>新功能</h4>
                <textarea v-model="newVersion.features" class="quantum-textarea" 
                          rows="3" placeholder="每行一个新功能..."></textarea>
              </div>
              <div class="changelog-section">
                <h4>改进</h4>
                <textarea v-model="newVersion.improvements" class="quantum-textarea" 
                          rows="3" placeholder="每行一个改进..."></textarea>
              </div>
              <div class="changelog-section">
                <h4>修复</h4>
                <textarea v-model="newVersion.bugfixes" class="quantum-textarea" 
                          rows="3" placeholder="每行一个修复..."></textarea>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="wizard-footer">
        <button @click="closeCreateWizard" class="quantum-btn-secondary">
          取消
        </button>
        <button @click="createVersion" class="quantum-btn-primary" :disabled="!canCreateVersion">
          创建版本
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 页面元数据
definePageMeta({
  title: 'Version Management - AR System Dashboard',
  description: 'Quantum application version control and release management'
})

// 响应式数据
const selectedApp = ref('')
const statusFilter = ref('')
const typeFilter = ref('')
const searchQuery = ref('')
const sortBy = ref('newest')
const selectedVersion = ref(null)
const showCreateWizard = ref(false)

// 新版本数据
const newVersion = ref({
  appId: '',
  number: '',
  type: 'minor',
  description: '',
  features: '',
  improvements: '',
  bugfixes: ''
})

// 版本统计指标
const versionMetrics = ref([
  {
    id: 1,
    label: 'Total Versions',
    value: 156,
    icon: 'i-carbon-version',
    status: 'ACTIVE',
    progress: 85,
    change: 12,
    changeType: 'positive'
  },
  {
    id: 2,
    label: 'Production',
    value: 24,
    icon: 'i-carbon-deployment-unit-technical',
    status: 'DEPLOYED',
    progress: 92,
    change: 8,
    changeType: 'positive'
  },
  {
    id: 3,
    label: 'In Testing',
    value: 8,
    icon: 'i-carbon-test-tool',
    status: 'TESTING',
    progress: 67,
    change: 15,
    changeType: 'positive'
  },
  {
    id: 4,
    label: 'Success Rate',
    value: '94.5%',
    icon: 'i-carbon-checkmark',
    status: 'EXCELLENT',
    progress: 94,
    change: 3,
    changeType: 'positive'
  }
])

// 应用列表
const applications = ref([
  { id: 'app-001', name: 'DataFlow Studio' },
  { id: 'app-002', name: 'CodeSync Pro' },
  { id: 'app-003', name: 'AR Builder' },
  { id: 'app-004', name: 'Quantum Analytics' }
])

// 版本列表
const versions = ref([
  {
    id: 'ver-001',
    number: 'v2.1.0',
    appName: 'DataFlow Studio',
    appId: 'app-001',
    description: '重大功能更新，新增实时协作和云端同步功能',
    fullDescription: '此版本包含重大功能更新，新增了实时协作功能，支持多用户同时编辑数据流。同时优化了云端同步机制，提升了数据传输效率。',
    status: 'production',
    type: 'minor',
    author: 'Alex Chen',
    createdAt: new Date('2024-01-15'),
    fileSize: '45.2MB',
    downloads: 1247,
    changes: [
      '新增实时协作功能',
      '优化云端同步机制',
      '修复数据导出问题',
      '提升界面响应速度'
    ],
    features: [
      '实时多用户协作编辑',
      '智能冲突解决机制',
      '云端自动备份'
    ],
    improvements: [
      '优化数据传输效率',
      '提升界面响应速度',
      '增强错误处理机制'
    ],
    bugfixes: [
      '修复数据导出格式问题',
      '解决内存泄漏问题',
      '修复登录状态异常'
    ],
    dependencies: [
      { name: 'React', version: '18.2.0' },
      { name: 'Node.js', version: '18.17.0' },
      { name: 'MongoDB', version: '6.0.0' }
    ],
    deployments: [
      {
        name: 'Production',
        status: 'active',
        url: 'https://app.dataflow.com',
        deployedAt: new Date('2024-01-15')
      },
      {
        name: 'Staging',
        status: 'inactive',
        url: 'https://staging.dataflow.com',
        deployedAt: new Date('2024-01-14')
      }
    ]
  },
  {
    id: 'ver-002',
    number: 'v2.0.5',
    appName: 'CodeSync Pro',
    appId: 'app-002',
    description: '安全性修复和性能优化',
    fullDescription: '此补丁版本主要修复了安全漏洞，优化了代码同步性能，提升了系统稳定性。',
    status: 'staging',
    type: 'patch',
    author: 'Sarah Johnson',
    createdAt: new Date('2024-01-14'),
    fileSize: '32.8MB',
    downloads: 892,
    changes: [
      '修复安全漏洞',
      '优化同步性能',
      '提升系统稳定性'
    ],
    features: [],
    improvements: [
      '优化代码同步算法',
      '提升网络传输效率'
    ],
    bugfixes: [
      '修复权限验证漏洞',
      '解决同步冲突问题',
      '修复界面显示异常'
    ],
    dependencies: [
      { name: 'Vue.js', version: '3.3.0' },
      { name: 'Express', version: '4.18.0' },
      { name: 'Redis', version: '7.0.0' }
    ],
    deployments: [
      {
        name: 'Staging',
        status: 'active',
        url: 'https://staging.codesync.com',
        deployedAt: new Date('2024-01-14')
      }
    ]
  }
])

// 计算属性
const totalVersions = computed(() => versions.value.length)
const deployedVersions = computed(() => versions.value.filter(v => v.status === 'production').length)
const pendingVersions = computed(() => versions.value.filter(v => ['development', 'testing'].includes(v.status)).length)
const lastSyncTime = computed(() => new Date().toLocaleTimeString())

const filteredVersions = computed(() => {
  let filtered = versions.value

  if (selectedApp.value) {
    filtered = filtered.filter(version => version.appId === selectedApp.value)
  }

  if (statusFilter.value) {
    filtered = filtered.filter(version => version.status === statusFilter.value)
  }

  if (typeFilter.value) {
    filtered = filtered.filter(version => version.type === typeFilter.value)
  }

  if (searchQuery.value) {
    filtered = filtered.filter(version =>
      version.number.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      version.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  // 排序
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'oldest':
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      case 'name':
        return a.number.localeCompare(b.number)
      case 'status':
        return a.status.localeCompare(b.status)
      default: // newest
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    }
  })

  return filtered
})

const canCreateVersion = computed(() => {
  return newVersion.value.appId && newVersion.value.number && newVersion.value.description
})

// 方法
const getChangeIcon = (changeType: string) => {
  return changeType === 'positive' ? 'i-carbon-arrow-up' : 'i-carbon-arrow-down'
}

const getStatusIcon = (status: string) => {
  const icons = {
    development: 'i-carbon-development',
    testing: 'i-carbon-test-tool',
    staging: 'i-carbon-deployment-unit-data',
    production: 'i-carbon-deployment-unit-technical',
    deprecated: 'i-carbon-warning'
  }
  return icons[status] || 'i-carbon-information'
}

const getStatusClass = (status: string) => {
  const classes = {
    development: 'info',
    testing: 'warning',
    staging: 'secondary',
    production: 'success',
    deprecated: 'error'
  }
  return classes[status] || 'info'
}

const getTypeLabel = (type: string) => {
  const labels = {
    major: '主版本',
    minor: '次版本',
    patch: '补丁',
    hotfix: '热修复'
  }
  return labels[type] || type
}

const formatDate = (date: Date) => {
  return date.toLocaleDateString()
}

const formatDateTime = (date: Date) => {
  return date.toLocaleString()
}

const resetFilters = () => {
  selectedApp.value = ''
  statusFilter.value = ''
  typeFilter.value = ''
  searchQuery.value = ''
}

const selectVersion = (version: any) => {
  selectedVersion.value = version
}

const closeDetails = () => {
  selectedVersion.value = null
}

const canPromote = (version: any) => {
  return ['development', 'testing'].includes(version.status)
}

const canDeploy = (version: any) => {
  return version.status === 'staging'
}

const promoteVersion = (version: any) => {
  console.log('Promoting version:', version.number)
}

const deployVersion = (version: any) => {
  console.log('Deploying version:', version.number)
}

const rollbackToVersion = (version: any) => {
  console.log('Rolling back to version:', version.number)
}

const downloadVersion = (version: any) => {
  console.log('Downloading version:', version.number)
}

const deleteVersion = (version: any) => {
  const index = versions.value.findIndex(v => v.id === version.id)
  if (index > -1) {
    versions.value.splice(index, 1)
  }
  if (selectedVersion.value?.id === version.id) {
    selectedVersion.value = null
  }
}

const createNewVersion = () => {
  showCreateWizard.value = true
}

const closeCreateWizard = () => {
  showCreateWizard.value = false
  newVersion.value = {
    appId: '',
    number: '',
    type: 'minor',
    description: '',
    features: '',
    improvements: '',
    bugfixes: ''
  }
}

const createVersion = () => {
  console.log('Creating new version:', newVersion.value)
  closeCreateWizard()
}

const rollbackVersion = () => {
  console.log('Initiating version rollback...')
}

onMounted(() => {
  console.log('Quantum Version Management initialized')
})
</script>

<style scoped>
.quantum-versions-page {
  padding: var(--space-6);
  min-height: 100vh;
}

.quantum-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.filters-panel {
  padding: var(--space-4);
  margin-bottom: var(--space-6);
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.filter-label {
  color: var(--quantum-fg-secondary);
  font-size: var(--text-sm);
  font-weight: 500;
}

.versions-layout {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: var(--space-6);
}

.versions-section {
  background: var(--quantum-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--quantum-border-color);
  border-radius: 1rem;
  padding: var(--space-6);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--quantum-border-color);
}

.view-controls {
  display: flex;
  gap: var(--space-2);
}

.versions-timeline {
  position: relative;
  display: grid;
  gap: var(--space-6);
}

.versions-timeline::before {
  content: '';
  position: absolute;
  left: 20px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--quantum-border-color);
}

.version-item {
  display: flex;
  gap: var(--space-4);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
}

.version-item:hover {
  transform: translateX(var(--space-2));
}

.version-marker {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--quantum-bg-elevated);
  border: 2px solid var(--quantum-border-color);
  z-index: 1;
  flex-shrink: 0;
}

.version-marker.development {
  border-color: var(--quantum-primary);
  color: var(--quantum-primary);
  background: rgba(0, 212, 255, 0.1);
}

.version-marker.testing {
  border-color: var(--quantum-warning);
  color: var(--quantum-warning);
  background: rgba(255, 170, 0, 0.1);
}

.version-marker.staging {
  border-color: var(--quantum-secondary);
  color: var(--quantum-secondary);
  background: rgba(255, 107, 157, 0.1);
}

.version-marker.production {
  border-color: var(--quantum-success);
  color: var(--quantum-success);
  background: rgba(46, 213, 115, 0.1);
}

.version-marker.deprecated {
  border-color: var(--quantum-error);
  color: var(--quantum-error);
  background: rgba(255, 71, 87, 0.1);
}

.version-content {
  flex: 1;
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  padding: var(--space-4);
  transition: all var(--transition-normal);
}

.version-content:hover {
  border-color: var(--quantum-primary);
  box-shadow: var(--quantum-glow-primary);
}

.version-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-3);
}

.version-number {
  font-size: var(--text-xl);
  font-weight: 700;
  margin-bottom: var(--space-1);
}

.version-app {
  color: var(--quantum-fg-secondary);
  font-size: var(--text-sm);
  margin-bottom: var(--space-2);
}

.version-meta {
  display: flex;
  gap: var(--space-3);
  font-size: var(--text-xs);
  color: var(--quantum-fg-muted);
}

.version-type {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 600;
  text-transform: uppercase;
}

.version-type.major {
  background: rgba(255, 71, 87, 0.2);
  color: var(--quantum-error);
}

.version-type.minor {
  background: rgba(0, 212, 255, 0.2);
  color: var(--quantum-primary);
}

.version-type.patch {
  background: rgba(46, 213, 115, 0.2);
  color: var(--quantum-success);
}

.version-type.hotfix {
  background: rgba(255, 170, 0, 0.2);
  color: var(--quantum-warning);
}

.version-description {
  color: var(--quantum-fg-secondary);
  margin-bottom: var(--space-3);
  line-height: 1.5;
}

.version-changes {
  margin-bottom: var(--space-4);
}

.changes-title {
  color: var(--quantum-primary);
  font-weight: 600;
  margin-bottom: var(--space-2);
  font-size: var(--text-sm);
}

.changes-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.changes-list li {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-1) 0;
  font-size: var(--text-sm);
  color: var(--quantum-fg-secondary);
}

.changes-list li i {
  color: var(--quantum-success);
  font-size: var(--text-xs);
}

.more-changes {
  color: var(--quantum-fg-muted);
  font-style: italic;
}

.version-actions {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.version-details-sidebar {
  position: sticky;
  top: var(--space-6);
  height: fit-content;
}

.version-details {
  padding: var(--space-6);
  max-height: 80vh;
  overflow-y: auto;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--quantum-border-color);
}

.close-btn {
  background: none;
  border: none;
  color: var(--quantum-fg-muted);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: 0.25rem;
  transition: all var(--transition-fast);
}

.close-btn:hover {
  color: var(--quantum-fg-primary);
  background: var(--quantum-bg-hover);
}

.version-title {
  font-size: var(--text-2xl);
  font-weight: 700;
  margin-bottom: var(--space-1);
}

.version-app-name {
  color: var(--quantum-fg-secondary);
  margin-bottom: var(--space-3);
}

.version-status-section {
  display: flex;
  gap: var(--space-2);
  align-items: center;
}

.version-type-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
}

.details-content {
  display: grid;
  gap: var(--space-6);
}

.section-title {
  color: var(--quantum-primary);
  font-weight: 600;
  margin-bottom: var(--space-3);
  padding-bottom: var(--space-2);
  border-bottom: 1px solid var(--quantum-border-color);
}

.overview-grid {
  display: grid;
  gap: var(--space-3);
}

.overview-item {
  display: flex;
  justify-content: space-between;
  padding: var(--space-2);
  background: var(--quantum-bg-primary);
  border-radius: 0.25rem;
}

.item-label {
  color: var(--quantum-fg-muted);
  font-size: var(--text-sm);
}

.item-value {
  color: var(--quantum-fg-primary);
  font-weight: 500;
  font-size: var(--text-sm);
}

.version-full-description {
  color: var(--quantum-fg-secondary);
  line-height: 1.6;
}

.changes-categories {
  display: grid;
  gap: var(--space-4);
}

.change-category {
  background: var(--quantum-bg-elevated);
  border-radius: 0.5rem;
  padding: var(--space-3);
}

.category-title {
  color: var(--quantum-primary);
  font-weight: 600;
  margin-bottom: var(--space-2);
  font-size: var(--text-sm);
}

.category-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.category-list li {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-1) 0;
  font-size: var(--text-sm);
  color: var(--quantum-fg-secondary);
}

.dependencies-list {
  display: grid;
  gap: var(--space-2);
}

.dependency-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2);
  background: var(--quantum-bg-elevated);
  border-radius: 0.25rem;
}

.dependency-name {
  font-weight: 500;
}

.dependency-version {
  color: var(--quantum-fg-muted);
  font-family: var(--font-mono);
  font-size: var(--text-sm);
}

.deployment-environments {
  display: grid;
  gap: var(--space-3);
}

.environment-item {
  background: var(--quantum-bg-elevated);
  border-radius: 0.5rem;
  padding: var(--space-3);
}

.env-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
}

.env-name {
  font-weight: 600;
}

.env-status {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
}

.env-status.active {
  background: rgba(46, 213, 115, 0.2);
  color: var(--quantum-success);
}

.env-status.inactive {
  background: rgba(255, 71, 87, 0.2);
  color: var(--quantum-error);
}

.env-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
  font-size: var(--text-sm);
  color: var(--quantum-fg-muted);
}

.details-footer {
  margin-top: var(--space-6);
  padding-top: var(--space-4);
  border-top: 1px solid var(--quantum-border-color);
}

.version-actions-full {
  display: flex;
  gap: var(--space-3);
  flex-wrap: wrap;
}

.create-wizard {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  z-index: 1000;
  padding: var(--space-6);
}

.wizard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--quantum-border-color);
}

.wizard-content {
  margin-bottom: var(--space-6);
}

.wizard-form {
  display: grid;
  gap: var(--space-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-label {
  color: var(--quantum-fg-secondary);
  font-size: var(--text-sm);
  font-weight: 500;
}

.changelog-editor {
  display: grid;
  gap: var(--space-4);
}

.changelog-section h4 {
  color: var(--quantum-primary);
  font-weight: 600;
  margin-bottom: var(--space-2);
  font-size: var(--text-sm);
}

.wizard-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-3);
  padding-top: var(--space-4);
  border-top: 1px solid var(--quantum-border-color);
}

@media (max-width: 1024px) {
  .versions-layout {
    grid-template-columns: 1fr;
  }

  .version-details-sidebar {
    position: static;
  }

  .filters-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .version-header {
    flex-direction: column;
    gap: var(--space-2);
    align-items: flex-start;
  }

  .version-meta {
    flex-direction: column;
    gap: var(--space-1);
  }

  .version-actions {
    flex-direction: column;
  }

  .create-wizard {
    width: 95%;
    max-height: 90vh;
  }

  .wizard-footer {
    flex-direction: column;
  }
}
</style>
