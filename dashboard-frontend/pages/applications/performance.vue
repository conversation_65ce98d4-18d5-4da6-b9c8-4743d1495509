<template>
  <div class="quantum-performance-page">
    <!-- 🌌 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title quantum-text-neon">
          📈 QUANTUM PERFORMANCE MONITOR
        </h1>
        <p class="page-subtitle quantum-text-glow">
          >> APPLICATION PERFORMANCE ANALYTICS & OPTIMIZATION <<
        </p>
        <div class="quantum-status-bar">
          <span class="quantum-hud-element">
            <i class="i-carbon-analytics"></i>
            APPS: {{ monitoredApps }}
          </span>
          <span class="quantum-hud-element">
            <i class="i-carbon-time"></i>
            AVG RESPONSE: {{ averageResponseTime }}ms
          </span>
          <span class="quantum-hud-element">
            <i class="i-carbon-checkmark"></i>
            UPTIME: {{ systemUptime }}%
          </span>
          <span class="quantum-hud-element">
            <i class="i-carbon-warning"></i>
            ALERTS: {{ activeAlerts }}
          </span>
        </div>
      </div>
      <div class="header-actions">
        <button @click="exportReport" class="quantum-btn-secondary quantum-ripple">
          <i class="i-carbon-download"></i>
          <span>导出报告</span>
        </button>
        <button @click="configureMonitoring" class="quantum-btn-primary quantum-ripple">
          <i class="i-carbon-settings"></i>
          <span>配置监控</span>
        </button>
      </div>
    </div>

    <!-- 📊 性能指标概览 -->
    <div class="quantum-metrics-grid">
      <div v-for="metric in performanceMetrics" :key="metric.id"
           class="quantum-card-hologram quantum-metric-card">
        <div class="quantum-metric-header">
          <div class="quantum-metric-ring">
            <i :class="metric.icon" class="quantum-metric-icon"></i>
          </div>
          <div class="quantum-metric-status">
            <span class="quantum-hud-element" :class="metric.statusClass">{{ metric.status }}</span>
          </div>
        </div>
        <div class="quantum-metric-content">
          <div class="quantum-metric-value quantum-text-glow">
            {{ metric.value }}
          </div>
          <div class="quantum-metric-label">
            {{ metric.label }}
          </div>
          <div class="quantum-metric-progress">
            <div class="quantum-progress-bar">
              <div class="quantum-progress-fill" 
                   :style="{ width: `${metric.progress}%` }"
                   :class="getProgressClass(metric.progress)"></div>
            </div>
          </div>
          <div class="quantum-metric-change" :class="metric.changeType">
            <i :class="getChangeIcon(metric.changeType)"></i>
            <span>{{ Math.abs(metric.change) }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 🔍 应用选择和筛选 -->
    <div class="quantum-card-hologram filters-panel">
      <div class="filters-header">
        <h2 class="quantum-text-matrix">性能监控筛选</h2>
        <div class="time-range-selector">
          <select v-model="selectedTimeRange" class="quantum-select">
            <option value="1h">最近1小时</option>
            <option value="6h">最近6小时</option>
            <option value="24h">最近24小时</option>
            <option value="7d">最近7天</option>
            <option value="30d">最近30天</option>
          </select>
        </div>
      </div>
      
      <div class="filters-grid">
        <div class="filter-group">
          <label class="filter-label">监控应用</label>
          <select v-model="selectedApp" class="quantum-select">
            <option value="">所有应用</option>
            <option v-for="app in applications" :key="app.id" :value="app.id">
              {{ app.name }}
            </option>
          </select>
        </div>
        
        <div class="filter-group">
          <label class="filter-label">性能指标</label>
          <select v-model="selectedMetric" class="quantum-select">
            <option value="response_time">响应时间</option>
            <option value="throughput">吞吐量</option>
            <option value="error_rate">错误率</option>
            <option value="cpu_usage">CPU使用率</option>
            <option value="memory_usage">内存使用率</option>
          </select>
        </div>
        
        <div class="filter-group">
          <label class="filter-label">环境</label>
          <select v-model="selectedEnvironment" class="quantum-select">
            <option value="">所有环境</option>
            <option value="production">生产环境</option>
            <option value="staging">预发布</option>
            <option value="development">开发环境</option>
          </select>
        </div>
        
        <div class="filter-group">
          <label class="filter-label">刷新间隔</label>
          <select v-model="refreshInterval" class="quantum-select">
            <option :value="5">5秒</option>
            <option :value="10">10秒</option>
            <option :value="30">30秒</option>
            <option :value="60">1分钟</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 📈 性能图表区域 -->
    <div class="performance-layout">
      <!-- 主要性能图表 -->
      <div class="quantum-card-hologram charts-section">
        <div class="charts-header">
          <h2 class="quantum-text-neon">性能趋势分析</h2>
          <div class="chart-controls">
            <button @click="toggleRealTime" 
                    :class="{ 'active': isRealTime }"
                    class="quantum-btn-ghost">
              <i class="i-carbon-radio-button-checked"></i>
              实时监控
            </button>
            <button @click="resetCharts" class="quantum-btn-ghost">
              <i class="i-carbon-reset"></i>
              重置
            </button>
          </div>
        </div>
        
        <div class="charts-grid">
          <div class="chart-item">
            <h3 class="chart-title">响应时间趋势</h3>
            <div class="chart-container">
              <div class="chart-placeholder">
                <i class="i-carbon-chart-line text-4xl text-gray-400"></i>
                <p>响应时间变化趋势</p>
                <div class="current-value">
                  <span class="value quantum-text-glow">{{ currentResponseTime }}ms</span>
                  <span class="trend" :class="responseTrend">{{ responseTrend === 'up' ? '↗' : '↘' }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="chart-item">
            <h3 class="chart-title">吞吐量监控</h3>
            <div class="chart-container">
              <div class="chart-placeholder">
                <i class="i-carbon-chart-area text-4xl text-gray-400"></i>
                <p>请求处理吞吐量</p>
                <div class="current-value">
                  <span class="value quantum-text-glow">{{ currentThroughput }}/s</span>
                  <span class="trend" :class="throughputTrend">{{ throughputTrend === 'up' ? '↗' : '↘' }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="chart-item">
            <h3 class="chart-title">错误率统计</h3>
            <div class="chart-container">
              <div class="chart-placeholder">
                <i class="i-carbon-chart-bar text-4xl text-gray-400"></i>
                <p>系统错误率监控</p>
                <div class="current-value">
                  <span class="value quantum-text-glow">{{ currentErrorRate }}%</span>
                  <span class="trend" :class="errorTrend">{{ errorTrend === 'up' ? '↗' : '↘' }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="chart-item">
            <h3 class="chart-title">资源使用率</h3>
            <div class="chart-container">
              <div class="chart-placeholder">
                <i class="i-carbon-chart-bubble text-4xl text-gray-400"></i>
                <p>CPU和内存使用情况</p>
                <div class="current-value">
                  <span class="value quantum-text-glow">{{ currentResourceUsage }}%</span>
                  <span class="trend" :class="resourceTrend">{{ resourceTrend === 'up' ? '↗' : '↘' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 应用性能详情 -->
      <div class="performance-details">
        <!-- 应用列表 -->
        <div class="quantum-card-hologram apps-performance">
          <div class="section-header">
            <h2 class="quantum-text-neon">应用性能排行</h2>
            <select v-model="sortBy" class="quantum-select">
              <option value="response_time">响应时间</option>
              <option value="throughput">吞吐量</option>
              <option value="error_rate">错误率</option>
              <option value="uptime">可用性</option>
            </select>
          </div>
          
          <div class="apps-list">
            <div v-for="app in sortedApps" :key="app.id"
                 class="app-performance-item"
                 @click="selectApp(app)">
              <div class="app-header">
                <div class="app-icon">
                  <img :src="app.icon" :alt="app.name" class="icon-image" />
                </div>
                <div class="app-info">
                  <h3 class="app-name">{{ app.name }}</h3>
                  <p class="app-environment">{{ app.environment }}</p>
                </div>
                <div class="app-health">
                  <span class="health-indicator" :class="app.healthStatus">
                    <i :class="getHealthIcon(app.healthStatus)"></i>
                  </span>
                </div>
              </div>
              
              <div class="app-metrics">
                <div class="metric-row">
                  <div class="metric-item">
                    <span class="metric-label">响应时间</span>
                    <span class="metric-value" :class="getMetricClass(app.responseTime, 'response')">
                      {{ app.responseTime }}ms
                    </span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">吞吐量</span>
                    <span class="metric-value">{{ app.throughput }}/s</span>
                  </div>
                </div>
                <div class="metric-row">
                  <div class="metric-item">
                    <span class="metric-label">错误率</span>
                    <span class="metric-value" :class="getMetricClass(app.errorRate, 'error')">
                      {{ app.errorRate }}%
                    </span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">可用性</span>
                    <span class="metric-value">{{ app.uptime }}%</span>
                  </div>
                </div>
              </div>
              
              <div class="app-actions">
                <button @click.stop="viewAppDetails(app)" class="quantum-btn-ghost">
                  <i class="i-carbon-view"></i>
                  详情
                </button>
                <button @click.stop="optimizeApp(app)" class="quantum-btn-ghost">
                  <i class="i-carbon-upgrade"></i>
                  优化
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 性能告警 -->
        <div class="quantum-card-hologram alerts-section">
          <div class="section-header">
            <h2 class="quantum-text-neon">性能告警</h2>
            <span class="alerts-count quantum-hud-element">{{ performanceAlerts.length }} 个告警</span>
          </div>
          
          <div class="alerts-list">
            <div v-for="alert in performanceAlerts" :key="alert.id"
                 class="alert-item" :class="alert.severity">
              <div class="alert-icon">
                <i :class="getAlertIcon(alert.severity)"></i>
              </div>
              <div class="alert-content">
                <h4 class="alert-title">{{ alert.title }}</h4>
                <p class="alert-message">{{ alert.message }}</p>
                <div class="alert-meta">
                  <span class="alert-app">{{ alert.appName }}</span>
                  <span class="alert-time">{{ formatTime(alert.timestamp) }}</span>
                </div>
              </div>
              <div class="alert-actions">
                <button @click="acknowledgeAlert(alert)" class="quantum-btn-ghost">
                  <i class="i-carbon-checkmark"></i>
                </button>
                <button @click="dismissAlert(alert)" class="quantum-btn-ghost">
                  <i class="i-carbon-close"></i>
                </button>
              </div>
            </div>
            
            <div v-if="performanceAlerts.length === 0" class="no-alerts">
              <i class="i-carbon-checkmark-filled text-2xl text-green-400"></i>
              <p>当前没有性能告警</p>
            </div>
          </div>
        </div>

        <!-- 性能建议 -->
        <div class="quantum-card-hologram recommendations-section">
          <div class="section-header">
            <h2 class="quantum-text-neon">优化建议</h2>
            <button @click="refreshRecommendations" class="quantum-btn-ghost">
              <i class="i-carbon-refresh"></i>
              刷新
            </button>
          </div>
          
          <div class="recommendations-list">
            <div v-for="recommendation in performanceRecommendations" :key="recommendation.id"
                 class="recommendation-item">
              <div class="recommendation-header">
                <div class="recommendation-icon">
                  <i :class="recommendation.icon" class="text-xl"></i>
                </div>
                <div class="recommendation-info">
                  <h4 class="recommendation-title">{{ recommendation.title }}</h4>
                  <p class="recommendation-impact">预期提升: {{ recommendation.impact }}</p>
                </div>
                <div class="recommendation-priority">
                  <span class="priority-badge" :class="recommendation.priority">
                    {{ getPriorityLabel(recommendation.priority) }}
                  </span>
                </div>
              </div>
              
              <div class="recommendation-description">
                <p>{{ recommendation.description }}</p>
              </div>
              
              <div class="recommendation-actions">
                <button @click="applyRecommendation(recommendation)" class="quantum-btn-primary">
                  <i class="i-carbon-play"></i>
                  应用建议
                </button>
                <button @click="learnMore(recommendation)" class="quantum-btn-ghost">
                  <i class="i-carbon-information"></i>
                  了解更多
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 页面元数据
definePageMeta({
  title: 'Performance Monitor - AR System Dashboard',
  description: 'Quantum application performance analytics and optimization'
})

// 响应式数据
const selectedTimeRange = ref('24h')
const selectedApp = ref('')
const selectedMetric = ref('response_time')
const selectedEnvironment = ref('')
const refreshInterval = ref(10)
const isRealTime = ref(true)
const sortBy = ref('response_time')

// 当前性能数据
const currentResponseTime = ref(156)
const currentThroughput = ref(2340)
const currentErrorRate = ref(0.12)
const currentResourceUsage = ref(68)

// 趋势数据
const responseTrend = ref('down')
const throughputTrend = ref('up')
const errorTrend = ref('down')
const resourceTrend = ref('up')

// 性能统计指标
const performanceMetrics = ref([
  {
    id: 1,
    label: 'Average Response Time',
    value: '156ms',
    icon: 'i-carbon-time',
    status: 'FAST',
    statusClass: 'success',
    progress: 75,
    change: 8,
    changeType: 'negative'
  },
  {
    id: 2,
    label: 'Throughput',
    value: '2.34K/s',
    icon: 'i-carbon-analytics',
    status: 'HIGH',
    statusClass: 'success',
    progress: 92,
    change: 15,
    changeType: 'positive'
  },
  {
    id: 3,
    label: 'Error Rate',
    value: '0.12%',
    icon: 'i-carbon-warning',
    status: 'LOW',
    statusClass: 'success',
    progress: 12,
    change: 3,
    changeType: 'negative'
  },
  {
    id: 4,
    label: 'System Uptime',
    value: '99.8%',
    icon: 'i-carbon-checkmark',
    status: 'EXCELLENT',
    statusClass: 'success',
    progress: 99,
    change: 1,
    changeType: 'positive'
  }
])

// 应用列表
const applications = ref([
  {
    id: 'app-001',
    name: 'DataFlow Studio',
    environment: 'Production',
    icon: '/api/placeholder/32/32',
    healthStatus: 'healthy',
    responseTime: 145,
    throughput: 1250,
    errorRate: 0.08,
    uptime: 99.9
  },
  {
    id: 'app-002',
    name: 'CodeSync Pro',
    environment: 'Production',
    icon: '/api/placeholder/32/32',
    healthStatus: 'warning',
    responseTime: 234,
    throughput: 890,
    errorRate: 0.25,
    uptime: 98.5
  },
  {
    id: 'app-003',
    name: 'AR Builder',
    environment: 'Staging',
    icon: '/api/placeholder/32/32',
    healthStatus: 'healthy',
    responseTime: 98,
    throughput: 567,
    errorRate: 0.03,
    uptime: 99.2
  }
])

// 性能告警
const performanceAlerts = ref([
  {
    id: 'alert-001',
    title: 'High Response Time',
    message: 'CodeSync Pro响应时间超过200ms阈值',
    severity: 'warning',
    appName: 'CodeSync Pro',
    timestamp: new Date('2024-01-15T14:30:00')
  },
  {
    id: 'alert-002',
    title: 'Memory Usage Spike',
    message: 'DataFlow Studio内存使用率达到85%',
    severity: 'critical',
    appName: 'DataFlow Studio',
    timestamp: new Date('2024-01-15T14:25:00')
  }
])

// 性能建议
const performanceRecommendations = ref([
  {
    id: 'rec-001',
    title: '启用数据库连接池',
    description: '通过配置数据库连接池可以显著减少连接建立时间，提升响应速度。',
    impact: '响应时间减少30%',
    priority: 'high',
    icon: 'i-carbon-data-base'
  },
  {
    id: 'rec-002',
    title: '优化静态资源缓存',
    description: '配置CDN和浏览器缓存策略，减少重复资源加载时间。',
    impact: '页面加载速度提升50%',
    priority: 'medium',
    icon: 'i-carbon-cloud-upload'
  },
  {
    id: 'rec-003',
    title: '实施代码分割',
    description: '将大型JavaScript包分割为更小的块，实现按需加载。',
    impact: '首屏加载时间减少40%',
    priority: 'medium',
    icon: 'i-carbon-code'
  }
])

// 计算属性
const monitoredApps = computed(() => applications.value.length)
const averageResponseTime = computed(() => {
  const total = applications.value.reduce((sum, app) => sum + app.responseTime, 0)
  return Math.round(total / applications.value.length)
})
const systemUptime = computed(() => {
  const total = applications.value.reduce((sum, app) => sum + app.uptime, 0)
  return (total / applications.value.length).toFixed(1)
})
const activeAlerts = computed(() => performanceAlerts.value.length)

const sortedApps = computed(() => {
  const sorted = [...applications.value]
  sorted.sort((a, b) => {
    switch (sortBy.value) {
      case 'throughput':
        return b.throughput - a.throughput
      case 'error_rate':
        return a.errorRate - b.errorRate
      case 'uptime':
        return b.uptime - a.uptime
      default: // response_time
        return a.responseTime - b.responseTime
    }
  })
  return sorted
})

// 方法
const getChangeIcon = (changeType: string) => {
  return changeType === 'positive' ? 'i-carbon-arrow-up' : 'i-carbon-arrow-down'
}

const getProgressClass = (progress: number) => {
  if (progress >= 90) return 'critical'
  if (progress >= 70) return 'warning'
  return 'normal'
}

const getHealthIcon = (status: string) => {
  const icons = {
    healthy: 'i-carbon-checkmark-filled',
    warning: 'i-carbon-warning-filled',
    critical: 'i-carbon-error-filled'
  }
  return icons[status] || 'i-carbon-information'
}

const getMetricClass = (value: number, type: string) => {
  if (type === 'response') {
    if (value > 200) return 'text-red-400'
    if (value > 100) return 'text-yellow-400'
    return 'text-green-400'
  }
  if (type === 'error') {
    if (value > 1) return 'text-red-400'
    if (value > 0.5) return 'text-yellow-400'
    return 'text-green-400'
  }
  return ''
}

const getAlertIcon = (severity: string) => {
  const icons = {
    critical: 'i-carbon-warning-filled',
    warning: 'i-carbon-warning',
    info: 'i-carbon-information'
  }
  return icons[severity] || 'i-carbon-information'
}

const getPriorityLabel = (priority: string) => {
  const labels = {
    high: '高优先级',
    medium: '中优先级',
    low: '低优先级'
  }
  return labels[priority] || priority
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString()
}

const toggleRealTime = () => {
  isRealTime.value = !isRealTime.value
  if (isRealTime.value) {
    startRealTimeMonitoring()
  } else {
    stopRealTimeMonitoring()
  }
}

const resetCharts = () => {
  console.log('Resetting performance charts...')
}

const selectApp = (app: any) => {
  selectedApp.value = app.id
  console.log('Selected app:', app.name)
}

const viewAppDetails = (app: any) => {
  console.log('Viewing details for app:', app.name)
}

const optimizeApp = (app: any) => {
  console.log('Optimizing app:', app.name)
}

const acknowledgeAlert = (alert: any) => {
  console.log('Acknowledging alert:', alert.id)
}

const dismissAlert = (alert: any) => {
  const index = performanceAlerts.value.findIndex(a => a.id === alert.id)
  if (index > -1) {
    performanceAlerts.value.splice(index, 1)
  }
}

const refreshRecommendations = () => {
  console.log('Refreshing performance recommendations...')
}

const applyRecommendation = (recommendation: any) => {
  console.log('Applying recommendation:', recommendation.id)
}

const learnMore = (recommendation: any) => {
  console.log('Learning more about recommendation:', recommendation.id)
}

const exportReport = () => {
  console.log('Exporting performance report...')
}

const configureMonitoring = () => {
  console.log('Configuring performance monitoring...')
}

let monitoringInterval: NodeJS.Timeout | null = null

const startRealTimeMonitoring = () => {
  monitoringInterval = setInterval(() => {
    // 模拟实时数据更新
    updatePerformanceData()
  }, refreshInterval.value * 1000)
}

const stopRealTimeMonitoring = () => {
  if (monitoringInterval) {
    clearInterval(monitoringInterval)
    monitoringInterval = null
  }
}

const updatePerformanceData = () => {
  // 模拟性能数据变化
  const responseChange = (Math.random() - 0.5) * 20
  currentResponseTime.value = Math.max(50, Math.min(500, currentResponseTime.value + responseChange))
  
  const throughputChange = (Math.random() - 0.5) * 200
  currentThroughput.value = Math.max(1000, Math.min(5000, currentThroughput.value + throughputChange))
  
  const errorChange = (Math.random() - 0.5) * 0.1
  currentErrorRate.value = Math.max(0, Math.min(5, currentErrorRate.value + errorChange))
  
  const resourceChange = (Math.random() - 0.5) * 10
  currentResourceUsage.value = Math.max(30, Math.min(100, currentResourceUsage.value + resourceChange))
}

onMounted(() => {
  console.log('Quantum Performance Monitor initialized')
  if (isRealTime.value) {
    startRealTimeMonitoring()
  }
})

onUnmounted(() => {
  stopRealTimeMonitoring()
})
</script>

<style scoped>
.quantum-performance-page {
  padding: var(--space-6);
  min-height: 100vh;
}

.quantum-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.quantum-progress-fill.critical {
  background: linear-gradient(90deg, var(--quantum-error), #ff4757);
}

.quantum-progress-fill.warning {
  background: linear-gradient(90deg, var(--quantum-warning), #ffa502);
}

.quantum-progress-fill.normal {
  background: linear-gradient(90deg, var(--quantum-primary), var(--quantum-accent));
}

.filters-panel {
  padding: var(--space-4);
  margin-bottom: var(--space-6);
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.filter-label {
  color: var(--quantum-fg-secondary);
  font-size: var(--text-sm);
  font-weight: 500;
}

.performance-layout {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-6);
}

.charts-section {
  padding: var(--space-6);
}

.charts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
}

.chart-controls {
  display: flex;
  gap: var(--space-2);
  align-items: center;
}

.chart-controls .quantum-btn-ghost.active {
  background: var(--quantum-primary);
  color: white;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-4);
}

.chart-item {
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  padding: var(--space-4);
}

.chart-title {
  color: var(--quantum-primary);
  font-weight: 600;
  margin-bottom: var(--space-3);
  font-size: var(--text-base);
}

.chart-container {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed var(--quantum-border-color);
  border-radius: 0.25rem;
  position: relative;
}

.chart-placeholder {
  text-align: center;
  color: var(--quantum-fg-muted);
}

.current-value {
  position: absolute;
  top: var(--space-2);
  right: var(--space-2);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.value {
  font-weight: 700;
  font-size: var(--text-lg);
}

.trend {
  font-size: var(--text-xl);
}

.trend.up {
  color: var(--quantum-success);
}

.trend.down {
  color: var(--quantum-error);
}

.performance-details {
  display: grid;
  gap: var(--space-6);
}

.apps-performance,
.alerts-section,
.recommendations-section {
  padding: var(--space-4);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--quantum-border-color);
}

.alerts-count {
  font-size: var(--text-xs);
}

.apps-list {
  display: grid;
  gap: var(--space-3);
  max-height: 400px;
  overflow-y: auto;
}

.app-performance-item {
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  padding: var(--space-3);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.app-performance-item:hover {
  border-color: var(--quantum-primary);
  box-shadow: var(--quantum-glow-primary);
}

.app-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-3);
}

.icon-image {
  width: 32px;
  height: 32px;
  border-radius: 0.25rem;
}

.app-info {
  flex: 1;
}

.app-name {
  font-weight: 600;
  margin-bottom: var(--space-1);
}

.app-environment {
  color: var(--quantum-fg-secondary);
  font-size: var(--text-sm);
}

.health-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.health-indicator.healthy {
  color: var(--quantum-success);
}

.health-indicator.warning {
  color: var(--quantum-warning);
}

.health-indicator.critical {
  color: var(--quantum-error);
}

.app-metrics {
  display: grid;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
}

.metric-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3);
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2);
  background: var(--quantum-bg-primary);
  border-radius: 0.25rem;
}

.metric-label {
  color: var(--quantum-fg-muted);
  font-size: var(--text-xs);
}

.metric-value {
  font-weight: 600;
  font-size: var(--text-sm);
}

.app-actions {
  display: flex;
  gap: var(--space-2);
}

.alerts-list {
  display: grid;
  gap: var(--space-3);
  max-height: 300px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  padding: var(--space-3);
  border-radius: 0.5rem;
  border-left: 4px solid;
}

.alert-item.critical {
  background: rgba(255, 71, 87, 0.1);
  border-left-color: var(--quantum-error);
}

.alert-item.warning {
  background: rgba(255, 170, 0, 0.1);
  border-left-color: var(--quantum-warning);
}

.alert-item.info {
  background: rgba(0, 212, 255, 0.1);
  border-left-color: var(--quantum-primary);
}

.alert-icon {
  color: inherit;
  font-size: var(--text-lg);
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  margin-bottom: var(--space-1);
}

.alert-message {
  color: var(--quantum-fg-secondary);
  font-size: var(--text-sm);
  margin-bottom: var(--space-2);
}

.alert-meta {
  display: flex;
  gap: var(--space-3);
  font-size: var(--text-xs);
  color: var(--quantum-fg-muted);
}

.alert-actions {
  display: flex;
  gap: var(--space-1);
}

.no-alerts {
  text-align: center;
  padding: var(--space-6);
  color: var(--quantum-fg-muted);
}

.recommendations-list {
  display: grid;
  gap: var(--space-4);
}

.recommendation-item {
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  padding: var(--space-4);
  transition: all var(--transition-normal);
}

.recommendation-item:hover {
  border-color: var(--quantum-primary);
  box-shadow: var(--quantum-glow-primary);
}

.recommendation-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-3);
}

.recommendation-icon {
  color: var(--quantum-primary);
}

.recommendation-info {
  flex: 1;
}

.recommendation-title {
  font-weight: 600;
  margin-bottom: var(--space-1);
}

.recommendation-impact {
  color: var(--quantum-success);
  font-size: var(--text-sm);
  font-weight: 500;
}

.priority-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
}

.priority-badge.high {
  background: rgba(255, 71, 87, 0.2);
  color: var(--quantum-error);
}

.priority-badge.medium {
  background: rgba(255, 170, 0, 0.2);
  color: var(--quantum-warning);
}

.priority-badge.low {
  background: rgba(0, 212, 255, 0.2);
  color: var(--quantum-primary);
}

.recommendation-description {
  color: var(--quantum-fg-secondary);
  margin-bottom: var(--space-3);
  line-height: 1.5;
}

.recommendation-actions {
  display: flex;
  gap: var(--space-2);
}

@media (max-width: 1024px) {
  .performance-layout {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .filters-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}

@media (max-width: 768px) {
  .charts-header {
    flex-direction: column;
    gap: var(--space-3);
    align-items: stretch;
  }

  .app-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }

  .metric-row {
    grid-template-columns: 1fr;
  }

  .alert-item {
    flex-direction: column;
    gap: var(--space-2);
  }

  .recommendation-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }

  .recommendation-actions {
    flex-direction: column;
  }
}
</style>
