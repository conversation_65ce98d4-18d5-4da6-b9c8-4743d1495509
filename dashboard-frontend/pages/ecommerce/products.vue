<template>
  <div class="quantum-products-page">
    <!-- 🌌 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title quantum-text-neon">
          📦 QUANTUM PRODUCT CENTER
        </h1>
        <p class="page-subtitle quantum-text-glow">
          >> ADVANCED PRODUCT MANAGEMENT & INVENTORY CONTROL SYSTEM <<
        </p>
        <div class="quantum-status-bar">
          <span class="quantum-hud-element">
            <i class="i-carbon-shopping-bag"></i>
            PRODUCTS: {{ totalProducts }}
          </span>
          <span class="quantum-hud-element">
            <i class="i-carbon-checkmark"></i>
            ACTIVE: {{ activeProducts }}
          </span>
          <span class="quantum-hud-element">
            <i class="i-carbon-warning"></i>
            LOW STOCK: {{ lowStockProducts }}
          </span>
          <span class="quantum-hud-element">
            <i class="i-carbon-time"></i>
            SYNC: {{ lastSyncTime }}
          </span>
        </div>
      </div>
      <div class="header-actions">
        <button @click="refreshProducts" class="quantum-btn-pulse quantum-ripple">
          <i class="i-carbon-refresh"></i>
          <span>⚡ REFRESH</span>
        </button>
        <button @click="toggleBatchMode" class="quantum-btn-secondary quantum-ripple" :class="{ active: batchMode }">
          <i class="i-carbon-checkbox"></i>
          <span>{{ batchMode ? 'EXIT BATCH' : 'BATCH MODE' }}</span>
        </button>
        <button @click="openInventoryManager" class="quantum-btn-secondary quantum-ripple">
          <i class="i-carbon-warehouse"></i>
          <span>INVENTORY</span>
        </button>
        <button @click="addProduct" class="quantum-btn-primary quantum-ripple">
          <i class="i-carbon-add"></i>
          <span>ADD PRODUCT</span>
        </button>
      </div>
    </div>

    <!-- 商品筛选与搜索 -->
    <div class="products-filters quantum-card-hologram">
      <div class="filters-grid">
        <div class="filter-group">
          <label class="filter-label">商品分类</label>
          <select v-model="categoryFilter" class="quantum-select">
            <option value="">全部分类</option>
            <option value="ar-glasses">AR眼镜</option>
            <option value="ar-headset">AR头显</option>
            <option value="dev-kit">开发套件</option>
            <option value="accessories">配件</option>
          </select>
        </div>
        <div class="filter-group">
          <label class="filter-label">商品状态</label>
          <select v-model="statusFilter" class="quantum-select">
            <option value="">全部状态</option>
            <option value="active">已上架</option>
            <option value="draft">草稿</option>
            <option value="inactive">已下架</option>
          </select>
        </div>
        <div class="filter-group">
          <label class="filter-label">库存状态</label>
          <select v-model="stockFilter" class="quantum-select">
            <option value="">全部库存</option>
            <option value="in-stock">有库存</option>
            <option value="low-stock">库存不足</option>
            <option value="out-of-stock">缺货</option>
          </select>
        </div>
        <div class="filter-group">
          <label class="filter-label">价格范围</label>
          <select v-model="priceFilter" class="quantum-select">
            <option value="">全部价格</option>
            <option value="0-1000">¥0 - ¥1,000</option>
            <option value="1000-5000">¥1,000 - ¥5,000</option>
            <option value="5000-10000">¥5,000 - ¥10,000</option>
            <option value="10000+">¥10,000+</option>
          </select>
        </div>
        <div class="filter-group">
          <label class="filter-label">搜索商品</label>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索商品名称或SKU..."
            class="quantum-input"
            @focus="$event.target.classList.add('quantum-focus')"
            @blur="$event.target.classList.remove('quantum-focus')"
          />
        </div>
      </div>
    </div>

    <!-- 批量操作工具栏 -->
    <div v-if="batchMode" class="batch-toolbar quantum-card-hologram">
      <div class="batch-header">
        <div class="batch-selection">
          <label class="batch-checkbox">
            <input
              type="checkbox"
              :checked="isAllSelected"
              @change="toggleSelectAll"
              class="checkbox-input"
            />
            <span class="checkbox-custom"></span>
            <span class="checkbox-label">已选择 {{ selectedProducts.length }} 个商品</span>
          </label>
        </div>
        <div class="batch-actions" v-if="selectedProducts.length > 0">
          <button @click="batchUpdateStatus('active')" class="quantum-btn-ghost">
            <i class="i-carbon-play"></i>
            <span>批量上架</span>
          </button>
          <button @click="batchUpdateStatus('inactive')" class="quantum-btn-ghost">
            <i class="i-carbon-pause"></i>
            <span>批量下架</span>
          </button>
          <button @click="batchUpdateInventory" class="quantum-btn-ghost">
            <i class="i-carbon-warehouse"></i>
            <span>批量调库存</span>
          </button>
          <button @click="batchDelete" class="quantum-btn-ghost text-red-400">
            <i class="i-carbon-trash-can"></i>
            <span>批量删除</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 商品网格 -->
    <div class="products-grid">
      <div
        v-for="product in filteredProducts"
        :key="product.id"
        class="product-card quantum-card-hologram quantum-interactive quantum-hover-glow"
        :class="{ 'selected': selectedProducts.includes(product.id), 'quantum-highlight': selectedProducts.includes(product.id) }"
      >
        <!-- 批量选择复选框 -->
        <div v-if="batchMode" class="product-checkbox">
          <label class="batch-checkbox">
            <input
              type="checkbox"
              :checked="selectedProducts.includes(product.id)"
              @change="toggleProductSelection(product.id)"
              class="checkbox-input"
            />
            <span class="checkbox-custom"></span>
          </label>
        </div>

        <!-- 商品图片 -->
        <div class="product-image">
          <img
            :src="product.image"
            :alt="product.name"
            class="product-img"
          />

          <!-- 3D预览按钮 -->
          <button
            v-if="product.has3DModel"
            @click="open3DPreview(product)"
            class="model-3d-btn"
            title="3D预览"
          >
            <i class="i-carbon-3d-curve-auto-colon"></i>
          </button>

          <div class="product-badges">
            <span v-if="product.isNew" class="badge new-badge">新品</span>
            <span v-if="product.isHot" class="badge hot-badge">热销</span>
            <span v-if="product.stock <= product.lowStockThreshold" class="badge stock-badge">库存不足</span>
          </div>

          <div class="product-status">
            <span
              class="status-indicator"
              :class="getStatusIndicatorClass(product.status)"
              :title="getStatusLabel(product.status)"
            ></span>
          </div>

          <div class="product-overlay">
            <div class="overlay-actions">
              <button @click="viewProduct(product)" class="overlay-btn" title="查看详情">
                <i class="i-carbon-view"></i>
              </button>
              <button @click="editProduct(product)" class="overlay-btn" title="编辑商品">
                <i class="i-carbon-edit"></i>
              </button>
              <button @click="duplicateProduct(product)" class="overlay-btn" title="复制商品">
                <i class="i-carbon-copy"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- 商品信息 -->
        <div class="product-info">
          <div class="product-header">
            <h3 class="product-name quantum-text-neon">{{ product.name }}</h3>
            <p class="product-sku">SKU: {{ product.sku }}</p>
          </div>

          <div class="product-category">
            <span class="category-badge">
              {{ getCategoryLabel(product.category) }}
            </span>
          </div>

          <div class="product-pricing">
            <div class="price-current quantum-text-glow">¥{{ product.price.toLocaleString() }}</div>
            <div v-if="product.originalPrice && product.originalPrice > product.price" class="price-original">
              ¥{{ product.originalPrice.toLocaleString() }}
            </div>
          </div>

          <!-- 增强的库存管理 -->
          <div class="product-stock">
            <div class="stock-header">
              <span class="stock-label">库存</span>
              <div class="stock-actions">
                <button @click="quickAdjustStock(product, -1)" class="stock-btn" :disabled="product.stock <= 0">
                  <i class="i-carbon-subtract"></i>
                </button>
                <span class="stock-value" :class="getStockClass(product.stock, product.lowStockThreshold)">
                  {{ product.stock }}
                </span>
                <button @click="quickAdjustStock(product, 1)" class="stock-btn">
                  <i class="i-carbon-add"></i>
                </button>
              </div>
            </div>
            <div class="stock-bar">
              <div
                class="stock-fill"
                :class="getStockBarClass(product.stock, product.lowStockThreshold)"
                :style="{ width: `${Math.min(100, (product.stock / product.maxStock) * 100)}%` }"
              ></div>
            </div>
            <div class="stock-info">
              <span class="stock-threshold">预警: {{ product.lowStockThreshold }}</span>
              <span class="stock-max">最大: {{ product.maxStock }}</span>
            </div>
          </div>

          <div class="product-stats">
            <div class="stat-item">
              <div class="stat-value quantum-text-glow">{{ product.sales }}</div>
              <div class="stat-label">销量</div>
            </div>
            <div class="stat-item">
              <div class="stat-value quantum-text-glow">{{ product.rating }}</div>
              <div class="stat-label">评分</div>
            </div>
            <div class="stat-item">
              <div class="stat-value quantum-text-glow">{{ product.views || 0 }}</div>
              <div class="stat-label">浏览</div>
            </div>
          </div>

          <div class="product-actions">
            <button @click="editProduct(product)" class="quantum-btn-ghost">
              <i class="i-carbon-edit"></i>
              <span>编辑</span>
            </button>
            <button @click="manageInventory(product)" class="quantum-btn-ghost">
              <i class="i-carbon-warehouse"></i>
              <span>库存</span>
            </button>
            <button @click="toggleProductStatus(product)" class="quantum-btn-primary">
              <i :class="product.status === 'active' ? 'i-carbon-pause' : 'i-carbon-play'"></i>
              <span>{{ product.status === 'active' ? '下架' : '上架' }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredProducts.length === 0" class="empty-state text-center py-12">
      <div class="empty-icon quantum-energy-ring w-20 h-20 mx-auto mb-4 flex items-center justify-center">
        <i class="i-carbon-shopping-bag text-3xl text-[var(--quantum-fg-muted)]"></i>
      </div>
      <h3 class="empty-title text-xl font-semibold quantum-text-matrix mb-2">暂无商品</h3>
      <p class="empty-description text-[var(--quantum-fg-secondary)] mb-4">没有找到符合条件的商品，请调整筛选条件或添加新商品。</p>
      <button @click="addProduct" class="quantum-btn-primary">
        <i class="i-carbon-add"></i>
        <span>添加商品</span>
      </button>
    </div>

    <!-- 分页 -->
    <div v-if="filteredProducts.length > 0" class="pagination-section">
      <div class="pagination-controls">
        <button class="quantum-btn-ghost" :disabled="currentPage === 1" @click="currentPage--">
          <i class="i-carbon-chevron-left"></i>
        </button>
        <span class="pagination-info">
          第 {{ currentPage }} 页，共 {{ totalPages }} 页
        </span>
        <button class="quantum-btn-ghost" :disabled="currentPage === totalPages" @click="currentPage++">
          <i class="i-carbon-chevron-right"></i>
        </button>
      </div>
    </div>

    <!-- 3D预览模态框 -->
    <div v-if="show3DPreview" class="modal-overlay" @click="close3DPreview">
      <div class="modal-content quantum-card-hologram" @click.stop>
        <div class="modal-header">
          <h2 class="modal-title quantum-text-neon">3D商品预览</h2>
          <button @click="close3DPreview" class="modal-close">
            <i class="i-carbon-close"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="model-3d-viewer" ref="model3DViewer">
            <div class="model-placeholder quantum-text-glow">
              🎮 3D模型加载中...
            </div>
          </div>
          <div class="model-controls">
            <button @click="reset3DView" class="quantum-btn-ghost">
              <i class="i-carbon-reset"></i>
              <span>重置视角</span>
            </button>
            <button @click="toggle3DAnimation" class="quantum-btn-ghost">
              <i :class="is3DAnimating ? 'i-carbon-pause' : 'i-carbon-play'"></i>
              <span>{{ is3DAnimating ? '暂停' : '播放' }}</span>
            </button>
            <button @click="fullscreen3D" class="quantum-btn-ghost">
              <i class="i-carbon-fit-to-screen"></i>
              <span>全屏</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 库存管理模态框 -->
    <div v-if="showInventoryModal" class="modal-overlay" @click="closeInventoryModal">
      <div class="modal-content quantum-card-hologram" @click.stop>
        <div class="modal-header">
          <h2 class="modal-title quantum-text-neon">库存管理</h2>
          <button @click="closeInventoryModal" class="modal-close">
            <i class="i-carbon-close"></i>
          </button>
        </div>
        <div class="modal-body">
          <div v-if="selectedInventoryProduct" class="inventory-form">
            <div class="form-group">
              <label class="form-label">商品名称</label>
              <div class="form-value">{{ selectedInventoryProduct.name }}</div>
            </div>
            <div class="form-group">
              <label class="form-label">当前库存</label>
              <div class="form-value quantum-text-glow">{{ selectedInventoryProduct.stock }} 件</div>
            </div>
            <div class="form-group">
              <label class="form-label">调整数量</label>
              <div class="stock-adjustment">
                <button @click="adjustmentAmount--" class="adjustment-btn">
                  <i class="i-carbon-subtract"></i>
                </button>
                <input
                  v-model.number="adjustmentAmount"
                  type="number"
                  class="adjustment-input quantum-input"
                  placeholder="输入调整数量"
                />
                <button @click="adjustmentAmount++" class="adjustment-btn">
                  <i class="i-carbon-add"></i>
                </button>
              </div>
            </div>
            <div class="form-group">
              <label class="form-label">调整原因</label>
              <select v-model="adjustmentReason" class="quantum-select">
                <option value="">请选择原因</option>
                <option value="purchase">采购入库</option>
                <option value="return">退货入库</option>
                <option value="damage">损坏出库</option>
                <option value="sale">销售出库</option>
                <option value="transfer">调拨</option>
                <option value="other">其他</option>
              </select>
            </div>
            <div class="form-group">
              <label class="form-label">备注</label>
              <textarea
                v-model="adjustmentNote"
                class="quantum-input"
                placeholder="请输入备注信息..."
                rows="3"
              ></textarea>
            </div>
            <div class="form-group">
              <label class="form-label">预计库存</label>
              <div class="form-value" :class="getStockClass(selectedInventoryProduct.stock + adjustmentAmount, selectedInventoryProduct.lowStockThreshold)">
                {{ selectedInventoryProduct.stock + adjustmentAmount }} 件
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button @click="closeInventoryModal" class="quantum-btn-ghost">
            <span>取消</span>
          </button>
          <button @click="confirmInventoryAdjustment" class="quantum-btn-primary">
            <i class="i-carbon-checkmark"></i>
            <span>确认调整</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 📝 编辑商品模态框 -->
    <div v-if="showProductModal" class="modal-overlay" @click="closeProductModal">
      <div class="modal-content quantum-card-hologram" @click.stop>
        <div class="modal-header">
          <h2 class="modal-title quantum-text-neon">
            {{ isEditMode ? '编辑商品' : '添加商品' }}
          </h2>
          <button @click="closeProductModal" class="modal-close">
            <i class="i-carbon-close"></i>
          </button>
        </div>

        <div class="modal-body">
          <div v-if="selectedProduct" class="product-form">
            <div class="form-grid">
              <div class="form-group">
                <label class="form-label">商品名称</label>
                <input
                  v-model="selectedProduct.name"
                  type="text"
                  class="quantum-input"
                  placeholder="请输入商品名称"
                />
              </div>

              <div class="form-group">
                <label class="form-label">商品SKU</label>
                <input
                  v-model="selectedProduct.sku"
                  type="text"
                  class="quantum-input"
                  placeholder="请输入商品SKU"
                />
              </div>

              <div class="form-group">
                <label class="form-label">商品分类</label>
                <select v-model="selectedProduct.category" class="quantum-select">
                  <option value="ar-glasses">AR眼镜</option>
                  <option value="ar-headset">AR头显</option>
                  <option value="dev-kit">开发套件</option>
                  <option value="accessories">配件</option>
                </select>
              </div>

              <div class="form-group">
                <label class="form-label">商品状态</label>
                <select v-model="selectedProduct.status" class="quantum-select">
                  <option value="active">已上架</option>
                  <option value="draft">草稿</option>
                  <option value="inactive">已下架</option>
                </select>
              </div>

              <div class="form-group">
                <label class="form-label">销售价格 (¥)</label>
                <input
                  v-model.number="selectedProduct.price"
                  type="number"
                  class="quantum-input"
                  placeholder="请输入销售价格"
                />
              </div>

              <div class="form-group">
                <label class="form-label">原价 (¥)</label>
                <input
                  v-model.number="selectedProduct.originalPrice"
                  type="number"
                  class="quantum-input"
                  placeholder="请输入原价（可选）"
                />
              </div>

              <div class="form-group">
                <label class="form-label">库存数量</label>
                <input
                  v-model.number="selectedProduct.stock"
                  type="number"
                  class="quantum-input"
                  placeholder="请输入库存数量"
                />
              </div>

              <div class="form-group">
                <label class="form-label">库存预警阈值</label>
                <input
                  v-model.number="selectedProduct.lowStockThreshold"
                  type="number"
                  class="quantum-input"
                  placeholder="请输入预警阈值"
                />
              </div>

              <div class="form-group">
                <label class="form-label">最大库存</label>
                <input
                  v-model.number="selectedProduct.maxStock"
                  type="number"
                  class="quantum-input"
                  placeholder="请输入最大库存"
                />
              </div>

              <div class="form-group">
                <label class="form-label">商品图片URL</label>
                <input
                  v-model="selectedProduct.image"
                  type="text"
                  class="quantum-input"
                  placeholder="请输入商品图片URL"
                />
              </div>

              <div class="form-group form-group-full">
                <label class="form-label">商品描述</label>
                <textarea
                  v-model="selectedProduct.description"
                  class="quantum-input"
                  rows="3"
                  placeholder="请输入商品描述..."
                ></textarea>
              </div>

              <div class="form-group">
                <label class="form-checkbox">
                  <input
                    v-model="selectedProduct.has3DModel"
                    type="checkbox"
                    class="checkbox-input"
                  />
                  <span class="checkbox-custom"></span>
                  <span class="checkbox-label">支持3D预览</span>
                </label>
              </div>

              <div class="form-group">
                <label class="form-checkbox">
                  <input
                    v-model="selectedProduct.isNew"
                    type="checkbox"
                    class="checkbox-input"
                  />
                  <span class="checkbox-custom"></span>
                  <span class="checkbox-label">新品标识</span>
                </label>
              </div>

              <div class="form-group">
                <label class="form-checkbox">
                  <input
                    v-model="selectedProduct.isHot"
                    type="checkbox"
                    class="checkbox-input"
                  />
                  <span class="checkbox-custom"></span>
                  <span class="checkbox-label">热销标识</span>
                </label>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button @click="closeProductModal" class="quantum-btn-ghost">
            <span>取消</span>
          </button>
          <button @click="saveProduct" class="quantum-btn-primary">
            <i class="i-carbon-checkmark"></i>
            <span>{{ isEditMode ? '保存修改' : '添加商品' }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 页面元数据
definePageMeta({
  title: 'Product Management - AR System Dashboard',
  description: 'Quantum advanced product management and inventory control system'
})

// 页面数据
const totalProducts = ref(156)
const activeProducts = ref(134)
const lowStockProducts = ref(12)
const lastSyncTime = ref(new Date().toLocaleTimeString())

// 筛选器
const categoryFilter = ref('')
const statusFilter = ref('')
const stockFilter = ref('')
const priceFilter = ref('')
const searchQuery = ref('')

// 分页
const currentPage = ref(1)
const pageSize = ref(12)

// 批量操作
const batchMode = ref(false)
const selectedProducts = ref<number[]>([])

// 3D预览
const show3DPreview = ref(false)
const selected3DProduct = ref(null)
const is3DAnimating = ref(true)

// 库存管理
const showInventoryModal = ref(false)
const selectedInventoryProduct = ref<any>(null)
const adjustmentAmount = ref(0)
const adjustmentReason = ref('')
const adjustmentNote = ref('')

// 编辑商品
const selectedProduct = ref<any>(null)
const isEditMode = ref(false)
const showProductModal = ref(false)

// 商品数据
const products = ref([
  {
    id: 1,
    name: 'AR-Vision Pro 企业版',
    sku: 'ARV-PRO-001',
    category: 'ar-glasses',
    status: 'active',
    price: 15999,
    originalPrice: 17999,
    stock: 45,
    lowStockThreshold: 10,
    maxStock: 200,
    sales: 234,
    rating: 4.8,
    views: 1247,
    image: '/products/ar-vision-pro.jpg',
    has3DModel: true,
    isNew: true,
    isHot: true
  },
  {
    id: 2,
    name: 'AR开发者套件 v2.0',
    sku: 'ARD-KIT-002',
    category: 'dev-kit',
    status: 'active',
    price: 8999,
    originalPrice: null,
    stock: 8,
    lowStockThreshold: 10,
    maxStock: 100,
    sales: 156,
    rating: 4.6,
    views: 892,
    image: '/products/ar-dev-kit.jpg',
    has3DModel: true,
    isNew: false,
    isHot: true
  },
  {
    id: 3,
    name: 'AR-Lite 消费者版',
    sku: 'ARL-CON-003',
    category: 'ar-glasses',
    status: 'active',
    price: 3999,
    originalPrice: 4999,
    stock: 120,
    lowStockThreshold: 20,
    maxStock: 300,
    sales: 567,
    rating: 4.4,
    views: 2134,
    image: '/products/ar-lite.jpg',
    has3DModel: false,
    isNew: false,
    isHot: false
  }
])

// 计算属性
const filteredProducts = computed(() => {
  return products.value.filter(product => {
    const matchesCategory = !categoryFilter.value || product.category === categoryFilter.value
    const matchesStatus = !statusFilter.value || product.status === statusFilter.value
    const matchesStock = !stockFilter.value || getStockStatus(product) === stockFilter.value
    const matchesPrice = !priceFilter.value || isPriceInRange(product.price, priceFilter.value)
    const matchesSearch = !searchQuery.value || 
      product.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      product.sku.toLowerCase().includes(searchQuery.value.toLowerCase())
    
    return matchesCategory && matchesStatus && matchesStock && matchesPrice && matchesSearch
  })
})

const totalPages = computed(() => Math.ceil(filteredProducts.value.length / pageSize.value))

// 批量操作计算属性
const isAllSelected = computed(() => {
  return filteredProducts.value.length > 0 &&
         filteredProducts.value.every(product => selectedProducts.value.includes(product.id))
})

// 方法
const getCategoryLabel = (category: string) => {
  const labels = {
    'ar-glasses': 'AR眼镜',
    'ar-headset': 'AR头显',
    'dev-kit': '开发套件',
    'accessories': '配件'
  }
  return labels[category as keyof typeof labels] || category
}

const getStatusLabel = (status: string) => {
  const labels = {
    active: '已上架',
    draft: '草稿',
    inactive: '已下架'
  }
  return labels[status as keyof typeof labels] || status
}

const getStatusIndicatorClass = (status: string) => {
  const classes = {
    active: 'status-active quantum-status-success',
    draft: 'status-draft quantum-status-warning',
    inactive: 'status-inactive quantum-status-error'
  }
  return classes[status as keyof typeof classes] || 'status-default'
}

const getStockClass = (stock: number, threshold: number) => {
  if (stock === 0) return 'critical quantum-status-error'
  if (stock <= threshold) return 'warning quantum-status-warning'
  return 'normal quantum-status-success'
}

const getStockBarClass = (stock: number, threshold: number) => {
  if (stock === 0) return 'critical'
  if (stock <= threshold) return 'warning'
  return 'normal'
}

const getStockStatus = (product: any) => {
  if (product.stock === 0) return 'out-of-stock'
  if (product.stock <= product.lowStockThreshold) return 'low-stock'
  return 'in-stock'
}

const isPriceInRange = (price: number, range: string) => {
  const [min, max] = range.split('-').map(p => p.replace('+', ''))
  const minPrice = parseInt(min)
  const maxPrice = max ? parseInt(max) : Infinity
  return price >= minPrice && price <= maxPrice
}

const refreshProducts = () => {
  console.log('刷新商品列表')
}

const addProduct = () => {
  selectedProduct.value = {
    id: Date.now(),
    name: '',
    sku: '',
    category: 'ar-glasses',
    status: 'draft',
    price: 0,
    originalPrice: null,
    stock: 0,
    lowStockThreshold: 10,
    maxStock: 100,
    sales: 0,
    rating: 0,
    views: 0,
    image: '',
    description: '',
    has3DModel: false,
    isNew: false,
    isHot: false
  }
  isEditMode.value = false
  showProductModal.value = true
}

const viewProduct = (product: any) => {
  console.log('查看商品', product)
}

const editProduct = (product: any) => {
  selectedProduct.value = { ...product }
  isEditMode.value = true
  showProductModal.value = true
}

const toggleProductStatus = (product: any) => {
  product.status = product.status === 'active' ? 'inactive' : 'active'
  console.log('切换商品状态', product)
}

// 批量操作方法
const toggleBatchMode = () => {
  batchMode.value = !batchMode.value
  if (!batchMode.value) {
    selectedProducts.value = []
  }
}

const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedProducts.value = []
  } else {
    selectedProducts.value = filteredProducts.value.map(product => product.id)
  }
}

const toggleProductSelection = (productId: number) => {
  const index = selectedProducts.value.indexOf(productId)
  if (index > -1) {
    selectedProducts.value.splice(index, 1)
  } else {
    selectedProducts.value.push(productId)
  }
}

const batchUpdateStatus = (status: string) => {
  selectedProducts.value.forEach(productId => {
    const product = products.value.find(p => p.id === productId)
    if (product) {
      product.status = status
    }
  })
  console.log(`批量${status === 'active' ? '上架' : '下架'}商品`, selectedProducts.value)
}

const batchUpdateInventory = () => {
  console.log('批量调整库存', selectedProducts.value)
}

const batchDelete = () => {
  if (confirm('确定要删除选中的商品吗？')) {
    products.value = products.value.filter(product => !selectedProducts.value.includes(product.id))
    selectedProducts.value = []
    console.log('批量删除商品')
  }
}

// 3D预览方法
const open3DPreview = (product: any) => {
  selected3DProduct.value = product
  show3DPreview.value = true
  console.log('打开3D预览', product)
}

const close3DPreview = () => {
  show3DPreview.value = false
  selected3DProduct.value = null
}

const reset3DView = () => {
  console.log('重置3D视角')
}

const toggle3DAnimation = () => {
  is3DAnimating.value = !is3DAnimating.value
  console.log('切换3D动画', is3DAnimating.value)
}

const fullscreen3D = () => {
  console.log('3D全屏模式')
}

// 库存管理方法
const quickAdjustStock = (product: any, amount: number) => {
  const newStock = product.stock + amount
  if (newStock >= 0) {
    product.stock = newStock
    console.log('快速调整库存', product.name, amount)
  }
}

const manageInventory = (product: any) => {
  selectedInventoryProduct.value = product
  adjustmentAmount.value = 0
  adjustmentReason.value = ''
  adjustmentNote.value = ''
  showInventoryModal.value = true
}

const openInventoryManager = () => {
  console.log('打开库存管理器')
}

const closeInventoryModal = () => {
  showInventoryModal.value = false
  selectedInventoryProduct.value = null
}

const confirmInventoryAdjustment = () => {
  if (selectedInventoryProduct.value && adjustmentReason.value) {
    selectedInventoryProduct.value.stock += adjustmentAmount.value
    console.log('确认库存调整', {
      product: selectedInventoryProduct.value.name,
      amount: adjustmentAmount.value,
      reason: adjustmentReason.value,
      note: adjustmentNote.value
    })
    closeInventoryModal()
  } else {
    alert('请填写调整原因')
  }
}

const duplicateProduct = (product: any) => {
  const newProduct = {
    ...product,
    id: Date.now(),
    name: product.name + ' (副本)',
    sku: product.sku + '-COPY',
    status: 'draft'
  }
  products.value.push(newProduct)
  console.log('复制商品', newProduct)
}

// 编辑商品相关方法
const closeProductModal = () => {
  showProductModal.value = false
  selectedProduct.value = null
  isEditMode.value = false
}

const saveProduct = () => {
  if (!selectedProduct.value) return

  if (isEditMode.value) {
    // 更新现有商品
    const index = products.value.findIndex(p => p.id === selectedProduct.value.id)
    if (index > -1) {
      products.value[index] = { ...selectedProduct.value }
      console.log('商品更新成功', selectedProduct.value)
    }
  } else {
    // 添加新商品
    products.value.push({ ...selectedProduct.value })
    console.log('商品添加成功', selectedProduct.value)
  }

  closeProductModal()
}

onMounted(() => {
  console.log('Product Management initialized')
})
</script>

<style scoped>
/* 🎨 页面基础样式 - 使用统一变量 */
.quantum-products-page {
  padding: var(--space-6);
  min-height: 100vh;
}

/* 📋 筛选器样式 - 复用量子卡片 */
.products-filters {
  /* 继承 quantum-card-hologram 样式 */
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-label {
  font-size: var(--text-sm);
  color: var(--quantum-fg-secondary);
  margin-bottom: var(--space-2);
}

/* 🔲 批量操作工具栏 - 使用统一样式 */
.batch-toolbar {
  /* 继承 quantum-card-hologram 样式 */
}

.batch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4);
}

.batch-selection {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.batch-checkbox {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  cursor: pointer;
}

.checkbox-input {
  display: none;
}

.checkbox-custom {
  width: 18px;
  height: 18px;
  border: 2px solid var(--quantum-border-color);
  border-radius: 4px;
  position: relative;
  transition: all var(--transition-normal);
}

.checkbox-input:checked + .checkbox-custom {
  background: var(--quantum-primary);
  border-color: var(--quantum-primary);
}

.checkbox-input:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.checkbox-label {
  font-size: var(--text-sm);
  color: var(--quantum-fg-primary);
}

.batch-actions {
  display: flex;
  gap: var(--space-2);
}

/* 📦 商品网格 */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.product-card {
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--quantum-glow-primary);
}

.product-card.selected {
  border-color: var(--quantum-primary);
  box-shadow: var(--quantum-glow-primary);
}

.product-checkbox {
  position: absolute;
  top: var(--space-3);
  left: var(--space-3);
  z-index: 10;
}

/* 🖼️ 商品图片 */
.product-image {
  position: relative;
  height: 200px;
  background: var(--quantum-bg-elevated);
  overflow: hidden;
}

.product-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.product-card:hover .product-img {
  transform: scale(1.05);
}

.model-3d-btn {
  position: absolute;
  top: var(--space-3);
  right: var(--space-3);
  width: 32px;
  height: 32px;
  background: var(--quantum-primary);
  border: none;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  z-index: 5;
}

.model-3d-btn:hover {
  background: var(--quantum-secondary);
  transform: scale(1.1);
}

.product-badges {
  position: absolute;
  top: var(--space-3);
  left: var(--space-3);
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  z-index: 5;
}

.badge {
  padding: var(--space-1) var(--space-2);
  font-size: var(--text-xs);
  font-weight: 600;
  border-radius: 4px;
  color: white;
}

.new-badge {
  background: var(--quantum-primary);
}

.hot-badge {
  background: var(--quantum-error);
}

.stock-badge {
  background: var(--quantum-warning);
}

.product-status {
  position: absolute;
  bottom: var(--space-3);
  right: var(--space-3);
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.product-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.product-card:hover .product-overlay {
  opacity: 1;
}

.overlay-actions {
  display: flex;
  gap: var(--space-2);
}

.overlay-btn {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
}

.overlay-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* 📝 商品信息 */
.product-info {
  padding: var(--space-4);
}

.product-header {
  margin-bottom: var(--space-3);
}

.product-name {
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--space-1);
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-sku {
  font-size: var(--text-sm);
  color: var(--quantum-fg-muted);
}

.product-category {
  margin-bottom: var(--space-3);
}

.category-badge {
  padding: var(--space-1) var(--space-2);
  font-size: var(--text-xs);
  background: var(--quantum-bg-elevated);
  color: var(--quantum-fg-secondary);
  border-radius: 4px;
}

.product-pricing {
  margin-bottom: var(--space-3);
}

.price-current {
  font-size: var(--text-xl);
  font-weight: 700;
}

.price-original {
  font-size: var(--text-sm);
  color: var(--quantum-fg-muted);
  text-decoration: line-through;
}

/* 📦 库存管理 */
.product-stock {
  margin-bottom: var(--space-4);
}

.stock-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
}

.stock-label {
  font-size: var(--text-sm);
  color: var(--quantum-fg-secondary);
}

.stock-actions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.stock-btn {
  width: 24px;
  height: 24px;
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 4px;
  color: var(--quantum-fg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.stock-btn:hover:not(:disabled) {
  background: var(--quantum-primary);
  color: white;
}

.stock-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.stock-value {
  font-size: var(--text-sm);
  font-weight: 600;
  min-width: 40px;
  text-align: center;
}

.stock-bar {
  height: 4px;
  background: var(--quantum-bg-elevated);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: var(--space-2);
}

.stock-fill {
  height: 100%;
  transition: all var(--transition-normal);
}

.stock-info {
  display: flex;
  justify-content: space-between;
  font-size: var(--text-xs);
  color: var(--quantum-fg-muted);
}

/* 📊 商品统计 */
.product-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-4);
  margin-bottom: var(--space-4);
  text-align: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: var(--text-lg);
  font-weight: 700;
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: var(--text-xs);
  color: var(--quantum-fg-muted);
}

/* 🎮 商品操作 */
.product-actions {
  display: flex;
  gap: var(--space-2);
}

/* 📄 分页 */
.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: var(--space-8);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.pagination-info {
  font-size: var(--text-sm);
  color: var(--quantum-fg-secondary);
}

/* 📝 编辑商品模态框样式 */
.product-form {
  max-height: 60vh;
  overflow-y: auto;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-4);
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group-full {
  grid-column: 1 / -1;
}

.form-label {
  font-size: var(--text-sm);
  color: var(--quantum-fg-secondary);
  margin-bottom: var(--space-2);
  font-weight: 500;
}

.form-checkbox {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  cursor: pointer;
  margin-top: var(--space-2);
}

/* 🎭 模态框样式 */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.modal-content {
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6);
  border-bottom: 1px solid var(--quantum-border-color);
}

.modal-title {
  font-size: var(--text-xl);
  font-weight: 600;
}

.modal-close {
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  color: var(--quantum-fg-primary);
  cursor: pointer;
  border-radius: 4px;
  transition: all var(--transition-normal);
}

.modal-close:hover {
  background: var(--quantum-bg-elevated);
}

.modal-body {
  padding: var(--space-6);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-3);
  padding: var(--space-6);
  border-top: 1px solid var(--quantum-border-color);
}

/* 🎮 3D模型查看器 */
.model-3d-viewer {
  height: 400px;
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-4);
}

.model-placeholder {
  font-size: var(--text-lg);
  text-align: center;
}

.model-controls {
  display: flex;
  justify-content: center;
  gap: var(--space-3);
}

/* 📦 库存表单 */
.inventory-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  font-size: var(--text-sm);
  color: var(--quantum-fg-secondary);
  margin-bottom: var(--space-2);
}

.form-value {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--quantum-fg-primary);
}

.stock-adjustment {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.adjustment-btn {
  width: 32px;
  height: 32px;
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 4px;
  color: var(--quantum-fg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.adjustment-btn:hover {
  background: var(--quantum-primary);
  color: white;
}

.adjustment-input {
  flex: 1;
  text-align: center;
}

/* 🎨 状态颜色 */
.stock-fill.normal {
  background: linear-gradient(90deg, var(--quantum-success), var(--quantum-primary));
}

.stock-fill.warning {
  background: linear-gradient(90deg, var(--quantum-warning), var(--quantum-secondary));
}

.stock-fill.critical {
  background: linear-gradient(90deg, var(--quantum-error), var(--quantum-warning));
}

.stock-value.normal {
  color: var(--quantum-success);
}

.stock-value.warning {
  color: var(--quantum-warning);
}

.stock-value.critical {
  color: var(--quantum-error);
}

/* 状态指示器颜色 */
.status-active {
  background: var(--quantum-success);
}

.status-draft {
  background: var(--quantum-warning);
}

.status-inactive {
  background: var(--quantum-error);
}

.status-default {
  background: var(--quantum-bg-muted);
}

/* 📱 响应式设计 */
@media (max-width: 1024px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .batch-header {
    flex-direction: column;
    gap: var(--space-3);
  }
}

@media (max-width: 640px) {
  .quantum-products-page {
    padding: var(--space-4);
  }

  .filters-grid {
    grid-template-columns: 1fr;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .product-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .modal-content {
    width: 95%;
  }
}
</style>
