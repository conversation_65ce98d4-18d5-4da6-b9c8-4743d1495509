<template>
  <div class="quantum-user-detail-page">
    <!-- 🌌 页面标题 -->
    <div class="page-header quantum-data-stream quantum-matrix-bg">
      <div class="header-content">
        <div class="breadcrumb mb-2">
          <NuxtLink to="/users" class="breadcrumb-link text-[var(--quantum-fg-secondary)] hover:text-[var(--quantum-primary)]">
            用户管理
          </NuxtLink>
          <span class="breadcrumb-separator mx-2 text-[var(--quantum-fg-muted)]">/</span>
          <span class="breadcrumb-current text-[var(--quantum-fg-primary)]">用户详情</span>
        </div>
        <h1 class="page-title quantum-text-neon quantum-glow-effect">
          👤 {{ user?.full_name || user?.username || '用户详情' }}
        </h1>
        <p class="page-subtitle quantum-text-glow">
          >> 用户信息与权限管理中心 <<
        </p>
        <div class="quantum-status-bar">
          <span class="quantum-hud-element">🆔 ID: {{ user?.id }}</span>
          <span class="quantum-hud-element" :class="getStatusClass(user?.is_active)">
            🔴 状态: {{ getStatusLabel(user?.is_active) }}
          </span>
          <span class="quantum-hud-element">🔑 角色: {{ getUserRoleDisplay(user) }}</span>
          <span class="quantum-hud-element">⚡ 同步: {{ lastSyncTime }}</span>
        </div>
      </div>
      <div class="header-actions">
        <button @click="refreshUser" class="action-btn refresh-btn">
          <i class="i-carbon-refresh"></i>
          <span>刷新</span>
        </button>
      </div>
    </div>

    <div v-if="user" class="user-detail-layout grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 左侧：用户基本信息 -->
      <div class="user-info-section lg:col-span-2 space-y-6">
        <!-- 用户概览卡片 -->
        <div class="quantum-card-hologram p-6 rounded-xl">
          <h2 class="section-title quantum-text-matrix text-xl font-semibold mb-4">用户概览</h2>
          <div class="user-overview grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 用户头像和基本信息 -->
            <div class="user-profile-section">
              <div class="user-avatar-container quantum-energy-ring w-32 h-32 mx-auto mb-4">
                <img
                  v-if="user.avatar_url"
                  :src="user.avatar_url"
                  :alt="user.username"
                  class="w-full h-full rounded-full object-cover"
                />
                <div v-else class="w-full h-full bg-[var(--quantum-bg-elevated)] rounded-full flex items-center justify-center">
                  <i class="i-carbon-user text-4xl text-[var(--quantum-fg-secondary)]"></i>
                </div>
              </div>
              <div class="user-basic-info text-center">
                <h3 class="user-name text-xl font-bold quantum-text-glow mb-2">{{ user.full_name || user.username }}</h3>
                <p class="user-email text-[var(--quantum-fg-secondary)] mb-2">{{ user.email }}</p>
                <div class="user-status-badge">
                  <span class="status-indicator px-3 py-1 rounded-full text-sm font-medium" :class="getStatusBadgeClass(user.is_active)">
                    {{ getStatusLabel(user.is_active) }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 用户详细信息 -->
            <div class="user-details-section space-y-4">
              <!-- 编辑模式 -->
              <div v-if="isEditMode" class="edit-form space-y-4">
                <div class="form-group">
                  <label class="form-label text-sm text-[var(--quantum-fg-secondary)] mb-2 block">全名</label>
                  <input
                    v-model="editForm.full_name"
                    type="text"
                    class="form-input w-full p-3 rounded-lg bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)] text-[var(--quantum-fg-primary)] focus:border-[var(--quantum-primary)] focus:outline-none transition-colors"
                    placeholder="请输入全名"
                  />
                </div>
                <div class="form-group">
                  <label class="form-label text-sm text-[var(--quantum-fg-secondary)] mb-2 block">邮箱</label>
                  <input
                    v-model="editForm.email"
                    type="email"
                    class="form-input w-full p-3 rounded-lg bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)] text-[var(--quantum-fg-primary)] focus:border-[var(--quantum-primary)] focus:outline-none transition-colors"
                    placeholder="请输入邮箱"
                  />
                </div>
                <div class="form-group">
                  <label class="form-label text-sm text-[var(--quantum-fg-secondary)] mb-2 block">账户状态</label>
                  <label class="status-toggle flex items-center justify-between cursor-pointer p-3 bg-[var(--quantum-bg-elevated)] rounded-lg border border-[var(--quantum-border-color)]">
                    <div class="status-info">
                      <span class="status-name text-sm font-medium">账户激活状态</span>
                      <p class="status-desc text-xs text-[var(--quantum-fg-secondary)]">控制用户是否可以登录系统</p>
                    </div>
                    <div class="toggle-container">
                      <input
                        type="checkbox"
                        v-model="editForm.is_active"
                        class="sr-only"
                      />
                      <div class="toggle-bg w-12 h-6 bg-[var(--quantum-bg-surface)] rounded-full relative transition-colors border" :class="{ 'bg-[var(--quantum-primary)] border-[var(--quantum-primary)]': editForm.is_active, 'border-[var(--quantum-border-color)]': !editForm.is_active }">
                        <div class="toggle-dot w-5 h-5 bg-white rounded-full absolute top-0.5 transition-transform shadow-sm" :class="{ 'translate-x-6': editForm.is_active, 'translate-x-0.5': !editForm.is_active }"></div>
                      </div>
                    </div>
                  </label>
                </div>
                <div class="form-actions flex gap-3 pt-4">

                </div>
              </div>

              <!-- 查看模式 -->
              <div v-else class="view-mode space-y-4">
                <div class="info-group">
                  <label class="info-label text-sm text-[var(--quantum-fg-secondary)]">用户ID</label>
                  <div class="info-value font-mono text-sm">{{ user.id }}</div>
                </div>
                <div class="info-group">
                  <label class="info-label text-sm text-[var(--quantum-fg-secondary)]">用户名</label>
                  <div class="info-value">{{ user.username }}</div>
                </div>
                <div class="info-group">
                  <label class="info-label text-sm text-[var(--quantum-fg-secondary)]">用户角色</label>
                  <div class="info-value">
                    <div class="user-roles flex flex-wrap gap-2">
                      <span v-if="user.is_superuser" class="role-badge superuser">
                        <i class="i-carbon-crown mr-1"></i>
                        超级管理员
                      </span>
                      <span v-for="role in user.roles" :key="role.id" class="role-badge">
                        {{ role.display_name || role.name }}
                      </span>
                      <span v-if="!user.is_superuser && (!user.roles || user.roles.length === 0)" class="role-badge default">
                        普通用户
                      </span>
                    </div>
                  </div>
                </div>
                <div class="info-group">
                  <label class="info-label text-sm text-[var(--quantum-fg-secondary)]">注册时间</label>
                  <div class="info-value">{{ formatDate(user.created_at) }}</div>
                </div>
                <div class="info-group">
                  <label class="info-label text-sm text-[var(--quantum-fg-secondary)]">最后登录</label>
                  <div class="info-value">{{ formatDateTime(user.last_login_at) }}</div>
                </div>
                <div class="info-group">
                  <label class="info-label text-sm text-[var(--quantum-fg-secondary)]">登录次数</label>
                  <div class="info-value quantum-text-glow">{{ (user.login_count || 0).toLocaleString() }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 用户活动统计 -->
        <div class="quantum-card-hologram p-6 rounded-xl">
          <h2 class="section-title quantum-text-matrix text-xl font-semibold mb-4">活动统计</h2>
          <div class="activity-metrics grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="metric-card p-4 rounded-lg bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)]">
              <div class="metric-header flex items-center justify-between mb-2">
                <span class="metric-label text-sm text-[var(--quantum-fg-secondary)]">今日登录</span>
                <i class="i-carbon-login text-[var(--quantum-primary)]"></i>
              </div>
              <div class="metric-value text-2xl font-bold quantum-text-glow">{{ userStats.todayLogins }}</div>
            </div>

            <div class="metric-card p-4 rounded-lg bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)]">
              <div class="metric-header flex items-center justify-between mb-2">
                <span class="metric-label text-sm text-[var(--quantum-fg-secondary)]">本周活跃</span>
                <i class="i-carbon-time text-[var(--quantum-secondary)]"></i>
              </div>
              <div class="metric-value text-2xl font-bold quantum-text-glow">{{ userStats.weeklyActive }}h</div>
            </div>

            <div class="metric-card p-4 rounded-lg bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)]">
              <div class="metric-header flex items-center justify-between mb-2">
                <span class="metric-label text-sm text-[var(--quantum-fg-secondary)]">操作次数</span>
                <i class="i-carbon-touch-interaction text-[var(--quantum-accent)]"></i>
              </div>
              <div class="metric-value text-2xl font-bold quantum-text-glow">{{ userStats.totalActions }}</div>
            </div>

            <div class="metric-card p-4 rounded-lg bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)]">
              <div class="metric-header flex items-center justify-between mb-2">
                <span class="metric-label text-sm text-[var(--quantum-fg-secondary)]">设备数量</span>
                <i class="i-carbon-devices text-[var(--quantum-warning)]"></i>
              </div>
              <div class="metric-value text-2xl font-bold quantum-text-glow">{{ Math.floor(userStats.deviceCount) }}</div>
            </div>
          </div>
        </div>

        <!-- 权限概览 -->
        <div class="quantum-card-hologram p-6 rounded-xl">
          <div class="section-header mb-4">
            <h2 class="section-title quantum-text-matrix text-xl font-semibold">权限概览</h2>
          </div>

          <div v-if="user.is_superuser" class="superuser-notice p-4 rounded-lg bg-[var(--quantum-secondary)]/10 border border-[var(--quantum-secondary)]/30">
            <div class="flex items-center gap-3">
              <i class="i-carbon-crown text-2xl text-[var(--quantum-secondary)]"></i>
              <div>
                <h3 class="font-semibold text-[var(--quantum-secondary)]">超级管理员</h3>
                <p class="text-sm text-[var(--quantum-fg-secondary)]">拥有系统所有权限，无需额外配置</p>
              </div>
            </div>
          </div>

          <div v-else class="permissions-summary">
            <div class="permission-stats grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div class="stat-card p-3 rounded-lg bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)]">
                <div class="stat-value text-lg font-bold quantum-text-glow">{{ userStats.totalRoles }}</div>
                <div class="stat-label text-xs text-[var(--quantum-fg-secondary)]">分配角色</div>
              </div>
              <div class="stat-card p-3 rounded-lg bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)]">
                <div class="stat-value text-lg font-bold quantum-text-glow">{{ userStats.totalPermissions }}</div>
                <div class="stat-label text-xs text-[var(--quantum-fg-secondary)]">有效权限</div>
              </div>
              <div class="stat-card p-3 rounded-lg bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)]">
                <div class="stat-value text-lg font-bold quantum-text-glow">{{ userStats.moduleAccess }}</div>
                <div class="stat-label text-xs text-[var(--quantum-fg-secondary)]">模块访问</div>
              </div>
              <div class="stat-card p-3 rounded-lg bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)]">
                <div class="stat-value text-lg font-bold" :class="userStats.riskLevel === 'low' ? 'text-[var(--quantum-success)]' : userStats.riskLevel === 'medium' ? 'text-[var(--quantum-warning)]' : 'text-[var(--quantum-error)]'">
                  {{ userStats.riskLevel.toUpperCase() }}
                </div>
                <div class="stat-label text-xs text-[var(--quantum-fg-secondary)]">风险等级</div>
              </div>
            </div>

            <div class="permission-hint p-3 rounded-lg bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)]">
              <div class="flex items-center gap-2 text-sm text-[var(--quantum-fg-secondary)]">
                <i class="i-carbon-information"></i>
                <span>点击"管理权限"按钮进入详细的角色和权限配置页面</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 最近活动 -->
        <div class="quantum-card-hologram p-6 rounded-xl">
          <h2 class="section-title quantum-text-matrix text-xl font-semibold mb-4">最近活动</h2>
          <div class="activity-timeline space-y-4">
            <div 
              v-for="activity in recentActivities" 
              :key="activity.id"
              class="activity-item flex items-start gap-4 p-4 rounded-lg bg-[var(--quantum-bg-elevated)]"
            >
              <div class="activity-icon quantum-energy-ring w-10 h-10 flex items-center justify-center">
                <i :class="activity.icon" class="text-[var(--quantum-primary)]"></i>
              </div>
              <div class="activity-content flex-1">
                <div class="activity-title font-semibold quantum-text-glow">{{ activity.title }}</div>
                <div class="activity-description text-sm text-[var(--quantum-fg-secondary)] mb-1">{{ activity.description }}</div>
                <div class="activity-meta flex items-center gap-4 text-xs text-[var(--quantum-fg-muted)]">
                  <span>{{ formatDateTime(activity.timestamp) }}</span>
                  <span>{{ activity.device }}</span>
                  <span>{{ activity.location }}</span>
                </div>
              </div>
              <div class="activity-status">
                <span class="status-badge px-2 py-1 rounded text-xs" :class="getActivityStatusClass(activity.status)">
                  {{ activity.status }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：操作面板 -->
      <div class="user-control-section space-y-6">
        <!-- 用户操作 -->
        <div class="quantum-card-hologram p-6 rounded-xl">
          <h2 class="section-title quantum-text-matrix text-xl font-semibold mb-4">用户操作</h2>
          <div class="user-actions space-y-3">
            <button @click="editUser" class="action-btn primary-btn w-full justify-center">
              <i class="i-carbon-edit"></i>
              <span>{{ isEditMode ? '保存修改' : '编辑用户' }}</span>
            </button>
            <button v-if="isEditMode" @click="cancelEdit" class="action-btn secondary-btn w-full justify-center">
              <i class="i-carbon-close"></i>
              <span>取消编辑</span>
            </button>
            <button v-if="!isEditMode" @click="resetPassword" class="action-btn secondary-btn w-full justify-center">
              <i class="i-carbon-password"></i>
              <span>重置密码</span>
            </button>
            <!-- 🔒 权限管理按钮 - 只有超级管理员可见 -->
            <button v-if="authStore.user?.is_superuser" @click="managePermissions" class="action-btn secondary-btn w-full justify-center" title="点击管理用户权限">
              <i class="i-carbon-security"></i>
              <span>管理权限</span>
            </button>
            <!-- 备用直接链接按钮 - 只有超级管理员可见 -->
            <NuxtLink v-if="authStore.user?.is_superuser" :to="`/users/${userId}/permissions`" class="action-btn primary-btn w-full justify-center mt-2" style="background: var(--quantum-primary); color: white;">
              <i class="i-carbon-security"></i>
              <span>直接跳转权限管理</span>
            </NuxtLink>
          </div>
        </div>

        <!-- 安全与设置 -->
        <div class="quantum-card-hologram p-6 rounded-xl">
          <h2 class="section-title quantum-text-matrix text-xl font-semibold mb-4">安全与设置</h2>

          <!-- 安全信息 -->
          <div class="security-section mb-6">
            <h3 class="subsection-title text-sm font-medium text-[var(--quantum-fg-secondary)] mb-3">安全状态</h3>
            <div class="security-info space-y-2">
              <div class="security-item flex justify-between items-center p-2 bg-[var(--quantum-bg-elevated)] rounded">
                <span class="security-label text-sm">密码强度</span>
                <span class="security-value text-sm font-semibold text-[var(--quantum-success)]">强</span>
              </div>
              <div class="security-item flex justify-between items-center p-2 bg-[var(--quantum-bg-elevated)] rounded">
                <span class="security-label text-sm">登录设备</span>
                <span class="security-value text-sm">{{ Math.floor(userStats.deviceCount) }}台</span>
              </div>
              <div class="security-item flex justify-between items-center p-2 bg-[var(--quantum-bg-elevated)] rounded">
                <span class="security-label text-sm">异常登录</span>
                <span class="security-value text-sm text-[var(--quantum-success)]">无</span>
              </div>
            </div>
          </div>

          <!-- 用户设置 -->
          <div class="settings-section">
            <h3 class="subsection-title text-sm font-medium text-[var(--quantum-fg-secondary)] mb-3">功能设置</h3>
            <div class="user-settings space-y-3">
              <div class="setting-item">
                <label class="setting-toggle flex items-center justify-between cursor-pointer">
                  <span class="setting-label text-sm">双因子认证</span>
                  <div class="toggle-container flex items-center">
                    <input
                      type="checkbox"
                      v-model="userSettings.twoFactorAuth"
                      class="sr-only"
                    />
                    <div class="toggle-bg w-10 h-5 bg-[var(--quantum-bg-elevated)] rounded-full relative transition-colors border border-[var(--quantum-border-color)]" :class="{ 'bg-[var(--quantum-primary)] border-[var(--quantum-primary)]': userSettings.twoFactorAuth }">
                      <div class="toggle-dot w-4 h-4 bg-white rounded-full absolute top-0.5 transition-transform shadow-sm" :class="{ 'translate-x-5': userSettings.twoFactorAuth, 'translate-x-0.5': !userSettings.twoFactorAuth }"></div>
                    </div>
                  </div>
                </label>
              </div>

              <div class="setting-item">
                <label class="setting-toggle flex items-center justify-between cursor-pointer">
                  <span class="setting-label text-sm">API访问</span>
                  <div class="toggle-container flex items-center">
                    <input
                      type="checkbox"
                      v-model="userSettings.apiAccess"
                      class="sr-only"
                    />
                    <div class="toggle-bg w-10 h-5 bg-[var(--quantum-bg-elevated)] rounded-full relative transition-colors border border-[var(--quantum-border-color)]" :class="{ 'bg-[var(--quantum-primary)] border-[var(--quantum-primary)]': userSettings.apiAccess }">
                      <div class="toggle-dot w-4 h-4 bg-white rounded-full absolute top-0.5 transition-transform shadow-sm" :class="{ 'translate-x-5': userSettings.apiAccess, 'translate-x-0.5': !userSettings.apiAccess }"></div>
                    </div>
                  </div>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户未找到 -->
    <div v-else class="user-not-found text-center py-12">
      <div class="not-found-icon quantum-energy-ring w-20 h-20 mx-auto mb-4 flex items-center justify-center">
        <i class="i-carbon-user-identification text-3xl text-[var(--quantum-warning)]"></i>
      </div>
      <h3 class="not-found-title text-xl font-semibold quantum-text-matrix mb-2">用户未找到</h3>
      <p class="not-found-description text-[var(--quantum-fg-secondary)] mb-4">指定的用户不存在或已被删除。</p>
      <NuxtLink to="/users" class="action-btn primary-btn">
        <i class="i-carbon-arrow-left"></i>
        <span>返回用户列表</span>
      </NuxtLink>
    </div>

    <!-- 权限管理模态框 -->
    <div v-if="showPermissionsModal" class="modal-overlay fixed inset-0 bg-black/50 flex items-center justify-center z-50" @click="showPermissionsModal = false">
      <div class="modal-content quantum-card-hologram p-6 rounded-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto" @click.stop>
        <div class="modal-header flex items-center justify-between mb-6">
          <h3 class="modal-title text-xl font-semibold quantum-text-matrix">
            <i class="i-carbon-security mr-2"></i>
            权限管理 - {{ user?.full_name || user?.username }}
          </h3>
          <button @click="showPermissionsModal = false" class="close-btn text-[var(--quantum-fg-secondary)] hover:text-[var(--quantum-fg-primary)]">
            <i class="i-carbon-close text-xl"></i>
          </button>
        </div>

        <div class="modal-body">
          <div class="permission-notice p-4 rounded-lg bg-[var(--quantum-primary)]/10 border border-[var(--quantum-primary)]/30 mb-6">
            <div class="flex items-center gap-3">
              <i class="i-carbon-information text-[var(--quantum-primary)]"></i>
              <div>
                <div class="font-semibold text-[var(--quantum-fg-primary)]">权限管理功能</div>
                <div class="text-sm text-[var(--quantum-fg-secondary)] mt-1">
                  完整的权限管理功能请访问专用页面，这里提供快速操作选项。
                </div>
              </div>
            </div>
          </div>

          <div class="quick-actions space-y-4">
            <button @click="goToPermissionsPage" class="w-full action-btn primary-btn justify-center">
              <i class="i-carbon-security mr-2"></i>
              打开完整权限管理页面
            </button>

            <div class="divider flex items-center my-4">
              <div class="flex-1 h-px bg-[var(--quantum-border-color)]"></div>
              <span class="px-3 text-sm text-[var(--quantum-fg-muted)]">或快速操作</span>
              <div class="flex-1 h-px bg-[var(--quantum-border-color)]"></div>
            </div>

            <!-- 🔒 超级管理员设置 - 只有超级管理员可见 -->
            <div v-if="authStore.user?.is_superuser" class="quick-permission-toggle">
              <label class="permission-toggle-card flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all"
                     :class="user?.is_superuser ? 'border-[var(--quantum-primary)] bg-[var(--quantum-primary)]/10' : 'border-[var(--quantum-border-color)] hover:border-[var(--quantum-primary)]'">
                <input
                  type="checkbox"
                  :checked="user?.is_superuser"
                  @change="toggleSuperuser"
                  class="w-5 h-5 text-[var(--quantum-primary)] bg-[var(--quantum-bg-elevated)] border-[var(--quantum-border-color)] rounded focus:ring-[var(--quantum-primary)]"
                />
                <div class="ml-4">
                  <div class="font-semibold text-[var(--quantum-fg-primary)]">
                    {{ user?.is_superuser ? '✅ 超级管理员' : '设为超级管理员' }}
                  </div>
                  <div class="text-sm text-[var(--quantum-fg-secondary)] mt-1">
                    {{ user?.is_superuser ? '当前用户拥有系统最高权限' : '启用后用户将获得系统最高权限' }}
                  </div>
                </div>
              </label>
            </div>

            <!-- 🚫 非超级管理员的提示 -->
            <div v-else class="permission-access-denied p-4 rounded-lg bg-[var(--quantum-error)]/10 border border-[var(--quantum-error)]/30">
              <div class="flex items-center gap-3">
                <i class="i-carbon-locked text-[var(--quantum-error)]"></i>
                <div>
                  <div class="font-semibold text-[var(--quantum-error)]">权限不足</div>
                  <div class="text-sm text-[var(--quantum-fg-secondary)] mt-1">
                    只有超级管理员才能管理用户权限
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer flex gap-3 mt-6 pt-4 border-t border-[var(--quantum-border-color)]">
          <button @click="showPermissionsModal = false" class="flex-1 action-btn secondary-btn justify-center">
            关闭
          </button>
          <button @click="goToPermissionsPage" class="flex-1 action-btn primary-btn justify-center">
            完整权限管理
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useTheme } from '~/composables/useTheme'
import { useAuthStore } from '~/stores/authStore'

// 🔒 权限验证
const authStore = useAuthStore()

// 页面元数据
definePageMeta({
  middleware: 'auth',
  title: 'User Details - AR System Dashboard',
  description: 'Quantum advanced user details and permission management'
})

// 路由参数
const route = useRoute()
const router = useRouter()
const userId = route.params.id

// 主题管理
const { currentTheme } = useTheme()

// API
const { users: usersApi } = useApi()

// 响应式数据
const user = ref(null)
const userActivities = ref([])
const isLoading = ref(false)
const error = ref('')
const lastSyncTime = ref(new Date().toLocaleTimeString())

// 编辑模式
const isEditMode = ref(false)
const editForm = ref({
  full_name: '',
  email: '',
  is_active: true
})

// 权限管理模态框
const showPermissionsModal = ref(false)

// 用户统计（基于真实数据计算）
const userStats = computed(() => ({
  todayLogins: user.value?.login_count || 0,
  weeklyActive: Math.floor((user.value?.login_count || 0) * 0.1),
  totalActions: (user.value?.login_count || 0) * 5,
  deviceCount: Math.min((user.value?.login_count || 0) / 10, 10),
  // 权限相关统计
  totalRoles: user.value?.roles?.length || 0,
  totalPermissions: user.value?.is_superuser ? '∞' : (user.value?.permissions?.length || 0),
  moduleAccess: user.value?.is_superuser ? '全部' : Math.min(user.value?.roles?.length || 0, 6),
  riskLevel: user.value?.is_superuser ? 'high' : (user.value?.roles?.length || 0) > 2 ? 'medium' : 'low'
}))

// 用户设置
const userSettings = ref({
  twoFactorAuth: false,
  apiAccess: false
})



// 最近活动（基于用户活动API）
const recentActivities = computed(() => {
  if (!userActivities.value.length) {
    return [
      {
        id: 1,
        title: '用户注册',
        description: '用户成功注册AR系统账户',
        icon: 'i-carbon-user-plus',
        timestamp: user.value?.created_at ? new Date(user.value.created_at) : new Date(),
        device: 'Web浏览器',
        location: '未知',
        status: '成功'
      },
      {
        id: 2,
        title: '最后登录',
        description: '用户最后一次登录系统',
        icon: 'i-carbon-login',
        timestamp: user.value?.last_login_at ? new Date(user.value.last_login_at) : new Date(),
        device: 'Web浏览器',
        location: '未知',
        status: '成功'
      }
    ]
  }
  return userActivities.value
})

// 加载用户数据
const loadUser = async () => {
  isLoading.value = true
  error.value = ''

  try {
    const response = await usersApi.get(Number(userId))
    user.value = response

    // 初始化编辑表单
    editForm.value = {
      full_name: response.full_name || '',
      email: response.email || '',
      is_active: response.is_active || false
    }

    console.log('用户详情加载成功:', response)
  } catch (err: any) {
    console.error('加载用户详情失败:', err)
    error.value = err.message || '加载用户详情失败'
  } finally {
    isLoading.value = false
  }
}

// 加载用户活动
const loadUserActivities = async () => {
  try {
    const response = await usersApi.activities(Number(userId), { page: 1, page_size: 10 })
    userActivities.value = response.activities || []
  } catch (err: any) {
    console.error('加载用户活动失败:', err)
  }
}

// 编辑用户
const editUser = async () => {
  if (isEditMode.value) {
    // 保存模式
    await saveUser()
  } else {
    // 编辑模式
    isEditMode.value = true
  }
}

const saveUser = async () => {
  try {
    const response = await usersApi.update(Number(userId), editForm.value)
    user.value = { ...user.value, ...response }
    isEditMode.value = false
    console.log('用户更新成功:', response)
    alert('用户信息更新成功！')
    // 刷新页面数据
    await loadUser()
  } catch (err: any) {
    console.error('更新用户失败:', err)
    alert('更新用户失败：' + (err.message || '未知错误'))
  }
}

const cancelEdit = () => {
  isEditMode.value = false
  // 重置表单
  if (user.value) {
    editForm.value = {
      full_name: user.value.full_name || '',
      email: user.value.email || '',
      is_active: user.value.is_active || false
    }
  }
}

// 刷新用户数据
const refreshUser = () => {
  loadUser()
  loadUserActivities()
  lastSyncTime.value = new Date().toLocaleTimeString()
}

// 操作方法
const resetPassword = async () => {
  const newPassword = prompt('请输入新密码（至少6位）:')
  if (!newPassword) return

  if (newPassword.length < 6) {
    alert('密码长度至少6位')
    return
  }

  if (confirm(`确定要将用户密码重置为: ${newPassword} 吗？`)) {
    try {
      await usersApi.resetPassword(Number(userId), newPassword)
      console.log('密码重置成功:', userId)
      alert('密码重置成功！')
    } catch (err: any) {
      console.error('重置密码失败:', err)
      alert('重置密码失败：' + (err.message || '未知错误'))
    }
  }
}

const managePermissions = async () => {
  console.log('🔐 权限管理按钮被点击')
  console.log('用户ID:', userId)
  console.log('目标路径:', `/users/${userId}/permissions`)

  try {
    // 方法1：使用 router.push (推荐)
    console.log('🚀 使用 router.push 进行跳转...')
    await router.push(`/users/${userId}/permissions`)
    console.log('✅ router.push 跳转成功')
  } catch (error) {
    console.error('❌ router.push 失败，尝试其他方法:', error)

    try {
      // 方法2：使用 navigateTo
      console.log('🚀 使用 navigateTo 进行跳转...')
      await navigateTo(`/users/${userId}/permissions`)
      console.log('✅ navigateTo 跳转成功')
    } catch (error2) {
      console.error('❌ navigateTo 也失败，使用强制跳转:', error2)

      // 方法3：强制跳转
      console.log('🚀 使用 window.location 强制跳转...')
      window.location.href = `/users/${userId}/permissions`
    }
  }
}



// 格式化方法
const formatDate = (dateString: string) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateString: string) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 方法
const getStatusClass = (isActive: boolean) => {
  return isActive ? 'text-[var(--quantum-success)]' : 'text-[var(--quantum-warning)]'
}

const getStatusLabel = (isActive: boolean) => {
  return isActive ? '活跃' : '非活跃'
}

const getStatusBadgeClass = (isActive: boolean) => {
  return isActive
    ? 'bg-[var(--quantum-success)] text-white'
    : 'bg-[var(--quantum-warning)] text-white'
}



const getActivityStatusClass = (status: string) => {
  const classes = {
    '成功': 'bg-[var(--quantum-success)] text-white',
    '失败': 'bg-[var(--quantum-error)] text-white',
    '警告': 'bg-[var(--quantum-warning)] text-white'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-500 text-white'
}

// 获取用户角色显示
const getUserRoleDisplay = (user: any) => {
  if (!user) return '加载中...'
  if (user.is_superuser) return '超级管理员'
  if (user.roles && user.roles.length > 0) {
    return user.roles.map((role: any) => role.display_name || role.name).join(', ')
  }
  return '普通用户'
}

// 页面挂载时加载数据
onMounted(() => {
  console.log('用户详情页面初始化，用户ID:', userId)
  loadUser()
  loadUserActivities()
})
</script>

<style scoped>
/* 面包屑导航 */
.breadcrumb-link {
  transition: color var(--transition-fast);
}

/* 用户头像 */
.user-avatar-container {
  position: relative;
  overflow: hidden;
}

/* 指标卡片 */
.metric-card {
  position: relative;
  overflow: hidden;
  transition: all var(--transition-fast);
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--quantum-primary), var(--quantum-accent));
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.metric-card:hover::before {
  opacity: 1;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* 权限项 */
.permission-item {
  transition: all var(--transition-fast);
  border-left: 3px solid transparent;
}

.permission-item:hover {
  border-left-color: var(--quantum-primary);
  transform: translateX(2px);
}

/* 活动时间线 */
.activity-item {
  transition: all var(--transition-fast);
  border-left: 3px solid transparent;
}

.activity-item:hover {
  border-left-color: var(--quantum-accent);
  transform: translateX(2px);
}

/* 切换开关 */
.toggle-bg {
  transition: background-color var(--transition-fast);
}

.toggle-dot {
  transition: transform var(--transition-fast);
}

/* 安全信息项 */
.security-item {
  transition: all var(--transition-fast);
}

.security-item:hover {
  background: var(--quantum-bg-hover);
  transform: translateX(2px);
}

/* 状态徽章 */
.status-badge {
  font-weight: 500;
}

/* 角色徽章 */
.role-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid var(--quantum-border-color);
  background: var(--quantum-bg-elevated);
  color: var(--quantum-fg-primary);
  transition: all var(--transition-fast);
}

.role-badge.superuser {
  background: var(--quantum-secondary);
  color: white;
  border-color: var(--quantum-secondary);
  box-shadow: 0 0 10px rgba(255, 107, 157, 0.3);
}

.role-badge.default {
  background: var(--quantum-bg-muted);
  color: var(--quantum-fg-secondary);
  border-color: var(--quantum-border-color);
}

.role-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .user-detail-layout {
    grid-template-columns: 1fr;
  }

  .user-overview {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .activity-metrics {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .activity-metrics {
    grid-template-columns: 1fr;
  }

  .permissions-grid {
    grid-template-columns: 1fr;
  }

  .activity-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}
</style>
