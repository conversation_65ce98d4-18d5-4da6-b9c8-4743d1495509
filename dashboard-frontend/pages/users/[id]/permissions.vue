<template>
  <div class="quantum-user-permissions-page min-h-screen bg-[var(--quantum-bg-primary)]">
    <!-- 页面头部 -->
    <div class="page-header bg-[var(--quantum-matrix-bg)] border-b border-[var(--quantum-border-color)]" style="padding: var(--space-8);">
      <div class="header-content flex justify-between items-end">
        <div class="header-info">
          <!-- 面包屑导航 -->
          <nav class="breadcrumb" style="margin-bottom: var(--space-4); font-size: var(--text-sm);">
            <NuxtLink to="/users" class="breadcrumb-link text-[var(--quantum-fg-secondary)] hover:text-[var(--quantum-primary)]">
              用户管理
            </NuxtLink>
            <span class="breadcrumb-separator mx-2 text-[var(--quantum-fg-muted)]">/</span>
            <span class="breadcrumb-current text-[var(--quantum-fg-primary)]">权限管理</span>
          </nav>
          
          <div class="page-title-info">
            <h1 class="page-title quantum-text-matrix font-bold" style="font-size: var(--text-3xl); margin-bottom: var(--space-2);">
              <i class="i-carbon-security" style="margin-right: var(--space-3);"></i>
              权限管理
            </h1>
            <div class="page-subtitle text-[var(--quantum-fg-secondary)]" style="font-size: var(--text-base);">
              管理用户 {{ user?.full_name || user?.username || 'Loading...' }} 的系统权限和角色分配
            </div>
          </div>
        </div>
        
        <div class="header-actions flex" style="gap: var(--space-4);">
          <button @click="savePermissions" class="action-btn primary-btn" :disabled="!hasChanges">
            <i class="i-carbon-save"></i>
            <span>保存权限</span>
          </button>
          <NuxtLink :to="`/users/${userId}`" class="action-btn secondary-btn">
            <i class="i-carbon-arrow-left"></i>
            <span>返回用户详情</span>
          </NuxtLink>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="page-content" style="padding: var(--space-8);">
      <div v-if="isLoading" class="loading-state text-center" style="padding: var(--space-12) 0;">
        <div class="quantum-energy-ring mx-auto" style="width: var(--space-16); height: var(--space-16); margin-bottom: var(--space-4);"></div>
        <p class="text-[var(--quantum-fg-secondary)]">正在加载权限数据...</p>
      </div>

      <div v-else-if="error" class="error-state text-center" style="padding: var(--space-12) 0;">
        <div class="error-icon mx-auto flex items-center justify-center" style="width: var(--space-16); height: var(--space-16); margin-bottom: var(--space-4);">
          <i class="i-carbon-warning text-[var(--quantum-error)]" style="font-size: var(--text-3xl);"></i>
        </div>
        <h3 class="error-title font-semibold" style="font-size: var(--text-xl); margin-bottom: var(--space-2);">加载失败</h3>
        <p class="error-message text-[var(--quantum-fg-secondary)]" style="margin-bottom: var(--space-4);">{{ error }}</p>
        <button @click="loadUserPermissions" class="action-btn primary-btn">
          <i class="i-carbon-refresh"></i>
          <span>重试</span>
        </button>
      </div>

      <div v-else-if="user" class="permissions-content grid grid-cols-1 lg:grid-cols-3" style="gap: var(--space-8);">
        <!-- 左侧：权限设置 -->
        <div class="permissions-settings lg:col-span-2 space-y-6">
          
          <!-- 权限管理 -->
          <div class="quantum-card-hologram">
            <h2 class="quantum-text-matrix font-semibold" style="font-size: var(--text-xl); margin-bottom: var(--space-6);">
              <i class="i-carbon-security" style="margin-right: var(--space-2);"></i>
              用户权限设置
            </h2>

            <!-- 超级用户设置 -->
            <div class="permission-section" style="margin-bottom: var(--space-6);">
              <label class="permission-toggle flex items-center rounded-lg border-2 cursor-pointer"
                     style="padding: var(--space-6); transition: all var(--transition-fast);"
                     :class="editForm.is_superuser ? 'border-[var(--quantum-primary)] bg-[var(--quantum-primary-bg)]' : 'border-[var(--quantum-border-color)] bg-[var(--quantum-bg-elevated)] hover:border-[var(--quantum-primary)]'">
                <input
                  type="checkbox"
                  v-model="editForm.is_superuser"
                  @change="onSuperuserChange"
                  class="text-[var(--quantum-primary)] bg-[var(--quantum-bg-elevated)] border-[var(--quantum-border-color)] rounded focus:ring-[var(--quantum-primary)]"
                  style="width: var(--space-6); height: var(--space-6);"
                />
                <div class="permission-info" style="margin-left: var(--space-6);">
                  <div class="permission-title font-bold text-[var(--quantum-fg-primary)]" style="font-size: var(--text-xl);">
                    {{ editForm.is_superuser ? '✅ 超级管理员' : '设为超级管理员' }}
                  </div>
                  <div class="permission-description text-[var(--quantum-fg-secondary)]" style="margin-top: var(--space-2);">
                    {{ editForm.is_superuser ? '当前用户拥有系统最高权限，可以执行所有操作' : '启用后用户将获得系统最高权限，包括用户管理、系统设置等' }}
                  </div>
                </div>
              </label>
            </div>

          </div>

          <!-- 角色分配 (仅非超级用户显示) -->
          <div v-if="!editForm.is_superuser" class="quantum-card-hologram">
            <h2 class="quantum-text-matrix font-semibold" style="font-size: var(--text-xl); margin-bottom: var(--space-6);">
              <i class="i-carbon-user-role" style="margin-right: var(--space-2);"></i>
              角色分配
            </h2>

            <div v-if="availableRoles.length > 0" class="roles-selection space-y-4">
              <div
                v-for="role in availableRoles"
                :key="role.id"
                class="role-option"
              >
                <label class="role-card flex items-center rounded-lg border-2 cursor-pointer"
                       style="padding: var(--space-5); transition: all var(--transition-fast);"
                       :class="editForm.role_ids.includes(role.id) ? 'border-[var(--quantum-secondary)] bg-[var(--quantum-secondary-bg)]' : 'border-[var(--quantum-border-color)] bg-[var(--quantum-bg-elevated)] hover:border-[var(--quantum-secondary)]'">
                  <input
                    type="checkbox"
                    :value="role.id"
                    v-model="editForm.role_ids"
                    @change="onRoleChange"
                    class="text-[var(--quantum-secondary)] bg-[var(--quantum-bg-elevated)] border-[var(--quantum-border-color)] rounded focus:ring-[var(--quantum-secondary)]"
                    style="width: var(--space-5); height: var(--space-5);"
                  />
                  <div class="role-details flex-1" style="margin-left: var(--space-5);">
                    <div class="role-header flex items-center justify-between" style="margin-bottom: var(--space-3);">
                      <div class="role-name font-bold text-[var(--quantum-fg-primary)]" style="font-size: var(--text-lg);">{{ role.display_name }}</div>
                      <span
                        class="role-type quantum-hud-element" style="padding: var(--space-2) var(--space-3); font-size: var(--text-sm);"
                        :class="role.is_system ? 'quantum-status-admin' : 'quantum-status-user'"
                      >
                        {{ role.is_system ? '系统角色' : '自定义角色' }}
                      </span>
                    </div>
                    <div class="role-description text-[var(--quantum-fg-secondary)]" style="margin-bottom: var(--space-3);">
                      {{ role.description || '暂无描述' }}
                    </div>
                    <div class="role-permissions text-[var(--quantum-fg-muted)]" style="font-size: var(--text-sm);">
                      权限数量: {{ role.permissions?.length || 0 }} 个
                    </div>
                  </div>
                </label>
              </div>
            </div>

            <div v-else class="no-roles text-center" style="padding: var(--space-16) 0;">
              <i class="i-carbon-user-role text-[var(--quantum-fg-muted)]" style="font-size: var(--text-5xl); margin-bottom: var(--space-6);"></i>
              <h3 class="font-semibold text-[var(--quantum-fg-secondary)]" style="font-size: var(--text-xl); margin-bottom: var(--space-3);">暂无可分配角色</h3>
              <p class="text-[var(--quantum-fg-muted)]">系统中还没有创建任何角色，请先创建角色</p>
            </div>
          </div>

          <!-- 权限预览 (仅非超级用户且有角色时显示) -->
          <div v-if="!editForm.is_superuser && Object.keys(effectivePermissions).length > 0" class="quantum-card-hologram">
            <h2 class="quantum-text-matrix font-semibold" style="font-size: var(--text-xl); margin-bottom: var(--space-6);">
              <i class="i-carbon-view" style="margin-right: var(--space-2);"></i>
              有效权限预览
            </h2>

            <div class="permissions-preview">
              <div class="permission-modules space-y-4">
                <div v-for="(permissions, module) in effectivePermissions" :key="module" class="permission-module">
                  <div class="module-header flex items-center" style="margin-bottom: var(--space-3);">
                    <i class="i-carbon-folder text-[var(--quantum-accent)]" style="margin-right: var(--space-2); font-size: var(--text-lg);"></i>
                    <h4 class="module-name font-semibold text-[var(--quantum-fg-primary)]" style="font-size: var(--text-base);">{{ module }}</h4>
                    <span class="module-count quantum-hud-element" style="margin-left: var(--space-3); padding: var(--space-1) var(--space-2); font-size: var(--text-xs);">{{ permissions.length }}</span>
                  </div>
                  <div class="permissions-list grid grid-cols-1 md:grid-cols-2 gap-2">
                    <div v-for="permission in permissions" :key="permission"
                         class="permission-item flex items-center rounded" style="padding: var(--space-2); background: var(--quantum-bg-elevated); border: 1px solid var(--quantum-border-color);">
                      <i class="i-carbon-checkmark text-[var(--quantum-success)]" style="margin-right: var(--space-2);"></i>
                      <span class="permission-name text-[var(--quantum-fg-primary)]" style="font-size: var(--text-sm);">{{ permission }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>


        </div>

        <!-- 右侧：状态和操作 -->
        <div class="permissions-sidebar space-y-6">
          <!-- 权限摘要 -->
          <div class="quantum-card-hologram">
            <h3 class="quantum-text-matrix font-semibold" style="font-size: var(--text-lg); margin-bottom: var(--space-4);">
              <i class="i-carbon-chart-bar" style="margin-right: var(--space-2);"></i>
              权限摘要
            </h3>
            <div class="summary-items space-y-3">
              <div class="summary-item flex justify-between items-center" style="padding: var(--space-3); background: var(--quantum-bg-elevated); border-radius: var(--space-2);">
                <span class="summary-label text-[var(--quantum-fg-secondary)]" style="font-size: var(--text-sm);">权限等级</span>
                <span 
                  class="summary-value quantum-hud-element" style="padding: var(--space-1) var(--space-3); font-size: var(--text-sm);"
                  :class="editForm.is_superuser ? 'quantum-status-admin' : 'quantum-status-user'"
                >
                  {{ editForm.is_superuser ? '超级用户' : '普通用户' }}
                </span>
              </div>
              
              <div class="summary-item flex justify-between items-center" style="padding: var(--space-3); background: var(--quantum-bg-elevated); border-radius: var(--space-2);">
                <span class="summary-label text-[var(--quantum-fg-secondary)]" style="font-size: var(--text-sm);">权限范围</span>
                <span class="summary-value font-semibold text-[var(--quantum-success)]" style="font-size: var(--text-sm);">
                  {{ totalPermissions }}
                </span>
              </div>

              <div class="summary-item flex justify-between items-center" style="padding: var(--space-3); background: var(--quantum-bg-elevated); border-radius: var(--space-2);">
                <span class="summary-label text-[var(--quantum-fg-secondary)]" style="font-size: var(--text-sm);">账户状态</span>
                <span class="summary-value font-semibold text-[var(--quantum-fg-primary)]" style="font-size: var(--text-sm);">
                  {{ user?.is_active ? '正常' : '已禁用' }}
                </span>
              </div>
            </div>
          </div>

          <!-- 变更提示 -->
          <div v-if="hasChanges" class="quantum-card-hologram border-2 border-[var(--quantum-warning)]">
            <h3 class="font-semibold text-[var(--quantum-warning)]" style="font-size: var(--text-lg); margin-bottom: var(--space-4);">
              <i class="i-carbon-warning" style="margin-right: var(--space-2);"></i>
              有未保存的变更
            </h3>
            <div class="changes-info text-[var(--quantum-fg-secondary)]" style="font-size: var(--text-sm); margin-bottom: var(--space-4);">
              您对用户权限进行了修改，请点击"保存权限"按钮保存变更。
            </div>
            <button @click="savePermissions" class="w-full quantum-btn-primary">
              <i class="i-carbon-save" style="margin-right: var(--space-2);"></i>
              保存权限变更
            </button>
          </div>

          <!-- 快速操作 -->
          <div class="quantum-card-hologram">
            <h3 class="quantum-text-matrix font-semibold" style="font-size: var(--text-lg); margin-bottom: var(--space-4);">
              <i class="i-carbon-settings" style="margin-right: var(--space-2);"></i>
              快速操作
            </h3>
            <div class="quick-actions space-y-3">
              <NuxtLink :to="`/users/${userId}`" class="w-full action-btn secondary-btn justify-center">
                <i class="i-carbon-user" style="margin-right: var(--space-2);"></i>
                查看用户详情
              </NuxtLink>
              
              <NuxtLink to="/users" class="w-full action-btn secondary-btn justify-center">
                <i class="i-carbon-list" style="margin-right: var(--space-2);"></i>
                返回用户列表
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 🎯 完整的三层权限管理系统
// 严格遵循后端权限架构和量子变量系统

import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTheme } from '~/composables/useTheme'
import { useApi } from '~/composables/useApi'

// 路由和主题
const route = useRoute()
const router = useRouter()
const { currentTheme } = useTheme()

// 用户ID
const userId = route.params.id as string

// API
const { users: usersApi, roles: rolesApi, permissionCheck: permissionCheckApi } = useApi()

// 响应式数据
const user = ref(null)
const availableRoles = ref([])
const userPermissions = ref(null)
const isLoading = ref(false)
const error = ref('')

// 编辑表单
const editForm = ref({
  is_superuser: false,
  role_ids: []
})

// 原始数据（用于检测变更）
const originalForm = ref({
  is_superuser: false,
  role_ids: []
})

// 🧮 计算属性
const hasChanges = computed(() => {
  return editForm.value.is_superuser !== originalForm.value.is_superuser ||
         JSON.stringify(editForm.value.role_ids.sort()) !== JSON.stringify(originalForm.value.role_ids.sort())
})

const selectedRoles = computed(() => {
  return availableRoles.value.filter(role => editForm.value.role_ids.includes(role.id))
})

const totalPermissions = computed(() => {
  if (editForm.value.is_superuser) return '全部权限'

  let count = 0
  selectedRoles.value.forEach(role => {
    count += role.permissions?.length || 0
  })
  return count > 0 ? `${count} 个权限` : '基础权限'
})

const effectivePermissions = computed(() => {
  if (editForm.value.is_superuser) {
    return {
      '系统管理': ['用户管理', '角色管理', '权限管理', '系统设置'],
      '设备管理': ['设备查看', '设备创建', '设备更新', '设备删除', '设备控制'],
      '应用管理': ['应用查看', '应用安装', '应用卸载', '应用更新', '应用删除'],
      '数据分析': ['数据查看', '数据导出'],
      '电商管理': ['商品管理', '订单管理'],
      '社区管理': ['内容管理', '用户互动']
    }
  }

  const permissions = {}
  selectedRoles.value.forEach(role => {
    role.permissions?.forEach(perm => {
      const module = perm.module || '其他'
      if (!permissions[module]) {
        permissions[module] = []
      }
      permissions[module].push(perm.display_name || perm.name)
    })
  })

  return permissions
})

// 📡 数据加载
const loadUserPermissions = async () => {
  isLoading.value = true
  error.value = ''

  try {
    console.log('开始加载用户权限数据，用户ID:', userId)

    // 并行加载用户数据、角色列表、用户权限详情
    const [userResponse, rolesResponse, userPermissionsResponse] = await Promise.all([
      usersApi.get(Number(userId)),
      rolesApi.list({ page: 1, page_size: 100 }),
      permissionCheckApi.getUserPermissions(Number(userId))
    ])

    console.log('用户数据:', userResponse)
    console.log('角色列表:', rolesResponse)
    console.log('用户权限详情:', userPermissionsResponse)

    user.value = userResponse
    availableRoles.value = rolesResponse || []
    userPermissions.value = userPermissionsResponse

    // 初始化表单
    editForm.value = {
      is_superuser: userResponse.is_superuser || false,
      role_ids: userPermissionsResponse.roles?.map(role => role.id) || []
    }

    // 保存原始数据
    originalForm.value = {
      is_superuser: editForm.value.is_superuser,
      role_ids: [...editForm.value.role_ids]
    }

    console.log('权限数据加载完成')
  } catch (err: any) {
    console.error('加载权限数据失败:', err)
    error.value = err.message || '加载权限数据失败'
  } finally {
    isLoading.value = false
  }
}

// 🎛️ 事件处理
const onSuperuserChange = () => {
  console.log('超级用户状态变化:', editForm.value.is_superuser)
  if (editForm.value.is_superuser) {
    // 清空角色选择
    editForm.value.role_ids = []
  }
}

const onRoleChange = () => {
  console.log('角色选择变化:', editForm.value.role_ids)
}

// 💾 保存权限
const savePermissions = async () => {
  try {
    console.log('开始保存权限设置...')
    console.log('当前表单状态:', editForm.value)
    console.log('原始状态:', originalForm.value)

    // 🔒 超级管理员唯一性检查
    if (editForm.value.is_superuser && !originalForm.value.is_superuser) {
      const confirmMessage = '⚠️ 重要提醒：\n\n系统只能有一个超级管理员！\n\n设置此用户为超级管理员将会：\n1. 赋予该用户最高权限\n2. 确保系统安全管理\n\n确定要继续吗？'
      if (!confirm(confirmMessage)) {
        return
      }
    }

    // 🔒 防止移除最后一个超级管理员
    if (!editForm.value.is_superuser && originalForm.value.is_superuser) {
      const confirmMessage = '⚠️ 安全警告：\n\n您正在移除超级管理员权限！\n\n请确保：\n1. 系统中还有其他超级管理员\n2. 您了解此操作的后果\n\n确定要继续吗？'
      if (!confirm(confirmMessage)) {
        return
      }
    }

    // 更新超级用户状态
    if (editForm.value.is_superuser !== originalForm.value.is_superuser) {
      console.log('更新超级用户状态:', editForm.value.is_superuser)
      await permissionCheckApi.updateSuperuserStatus(Number(userId), {
        is_superuser: editForm.value.is_superuser
      })
      console.log('超级用户状态更新成功')
    }

    // 如果不是超级用户，更新角色分配
    if (!editForm.value.is_superuser) {
      const rolesChanged = JSON.stringify(editForm.value.role_ids.sort()) !== JSON.stringify(originalForm.value.role_ids.sort())

      if (rolesChanged) {
        console.log('更新用户角色分配:', { from: originalForm.value.role_ids, to: editForm.value.role_ids })
        await permissionCheckApi.assignUserRoles(Number(userId), {
          role_ids: editForm.value.role_ids,
          action: 'replace'
        })
        console.log('角色分配更新成功')
      }
    }

    // 更新原始数据
    originalForm.value = {
      is_superuser: editForm.value.is_superuser,
      role_ids: [...editForm.value.role_ids]
    }

    // 重新加载数据
    await loadUserPermissions()

    console.log('权限保存成功')
    alert('✅ 权限设置保存成功！')
  } catch (err: any) {
    console.error('保存权限失败:', err)

    // 🔒 处理超级管理员唯一性错误
    if (err.message && err.message.includes('只能有一个超级管理员')) {
      alert('🚫 操作失败：\n\n' + err.message + '\n\n系统安全策略：确保始终只有一个超级管理员。')
    } else if (err.message && err.message.includes('不能移除最后一个超级管理员')) {
      alert('🚫 操作失败：\n\n' + err.message + '\n\n系统安全策略：必须至少保留一个超级管理员。')
    } else {
      alert('❌ 保存权限失败：' + (err.message || '未知错误'))
    }
  }
}

// 🚀 组件挂载
onMounted(() => {
  loadUserPermissions()
})
</script>

<style scoped>
/* 🎨 完整权限管理页面样式 - 严格使用量子变量系统 */

/* 📱 响应式布局 */
@media (max-width: 640px) {
  .permissions-content {
    grid-template-columns: 1fr !important;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start !important;
    gap: var(--space-4);
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

/* 🎯 按钮样式 - 使用量子变量 */
.action-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--space-2);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  transition: all var(--transition-fast);
  border: 1px solid var(--quantum-border-color);
  background: var(--quantum-bg-elevated);
  color: var(--quantum-fg-primary);
  text-decoration: none;
}

.action-btn:hover {
  border-color: var(--quantum-primary);
  box-shadow: var(--quantum-glow-primary);
  transform: translateY(-1px);
}

.action-btn.primary-btn {
  background: var(--quantum-primary);
  color: white;
  border-color: var(--quantum-primary);
}

.action-btn.primary-btn:hover {
  background: var(--quantum-secondary);
  border-color: var(--quantum-secondary);
  box-shadow: var(--quantum-glow-secondary);
}

.action-btn.secondary-btn {
  background: var(--quantum-bg-surface);
  border-color: var(--quantum-border-color);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 🏷️ 状态标签样式 */
.quantum-hud-element {
  display: inline-block;
  font-family: var(--font-mono);
  font-weight: var(--font-medium);
  border-radius: var(--space-1);
  border: 1px solid var(--quantum-border-color);
  background: var(--quantum-bg-glass);
  backdrop-filter: blur(10px);
}

.quantum-status-admin {
  background: rgba(0, 212, 255, 0.1);
  border-color: var(--quantum-primary);
  color: var(--quantum-primary);
}

.quantum-status-user {
  background: rgba(255, 107, 157, 0.1);
  border-color: var(--quantum-secondary);
  color: var(--quantum-secondary);
}

/* 🎨 主按钮样式 */
.quantum-btn-primary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-4) var(--space-6);
  background: var(--quantum-primary);
  color: white;
  border: none;
  border-radius: var(--space-2);
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.quantum-btn-primary:hover {
  background: var(--quantum-secondary);
  box-shadow: var(--quantum-glow-secondary);
  transform: translateY(-1px);
}

/* 🌟 特殊效果 */
.quantum-energy-ring {
  animation: quantumPulse 3s ease-in-out infinite;
}

/* 🎭 深色主题适配 */
[data-theme="dark"] .action-btn {
  background: var(--quantum-bg-elevated);
  border-color: var(--quantum-border-color);
}

[data-theme="dark"] .quantum-hud-element {
  background: var(--quantum-bg-glass);
  backdrop-filter: blur(15px);
}

[data-theme="dark"] .role-card {
  box-shadow: var(--quantum-shadow-normal);
}

/* 🌞 浅色主题适配 */
[data-theme="light"] .action-btn {
  background: var(--quantum-bg-surface);
  border-color: var(--quantum-border-color);
  box-shadow: var(--quantum-shadow-normal);
}

[data-theme="light"] .quantum-hud-element {
  background: var(--quantum-bg-glass);
  backdrop-filter: blur(5px);
  box-shadow: var(--quantum-shadow-normal);
}

[data-theme="light"] .role-card {
  box-shadow: var(--quantum-shadow-normal);
}

/* 🎯 角色卡片特殊样式 */
.role-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--quantum-glow-primary);
}

.system-role-option .role-card:hover {
  box-shadow: var(--quantum-glow-primary);
}

.custom-role-option .role-card:hover {
  box-shadow: var(--quantum-glow-secondary);
}

/* 📊 权限预览样式 */
.permission-item {
  transition: all var(--transition-fast);
}

.permission-item:hover {
  background: var(--quantum-bg-surface);
  border-color: var(--quantum-success);
}
</style>
