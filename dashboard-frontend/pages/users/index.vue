<template>
  <div class="quantum-users-page">
    <!-- 🌌 量子用户管理页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title quantum-text-neon quantum-glow-effect">
          👥 QUANTUM USER MANAGEMENT MATRIX
        </h1>
        <p class="page-subtitle quantum-text-glow">
          >> AR USER IDENTITY & ACCESS CONTROL SYSTEM <<
        </p>
        <div class="quantum-status-bar">
          <span class="quantum-hud-element">👤 USERS: {{ totalUsers }}</span>
          <span class="quantum-hud-element">🟢 ACTIVE: {{ activeUsers }}</span>
          <span class="quantum-hud-element">🔒 ADMINS: {{ adminUsers }}</span>
          <span class="quantum-hud-element">⚡ SYNC: {{ lastSyncTime }}</span>
        </div>
      </div>
      <div class="header-actions">
        <button @click="refreshUsers" class="action-btn refresh-btn">
          <i class="i-carbon-refresh"></i>
          <span>刷新用户</span>
        </button>
        <button @click="addUser" class="action-btn add-btn">
          <i class="i-carbon-user-plus"></i>
          <span>添加用户</span>
        </button>
      </div>
    </div>

    <!-- 📊 量子用户统计面板 -->
    <div class="quantum-metrics-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <div v-for="metric in userMetricsComputed" :key="metric.id"
           class="quantum-card-hologram quantum-metric-card p-4 rounded-xl">
        <div class="quantum-metric-header flex justify-between items-start mb-4">
          <div class="quantum-energy-ring quantum-metric-ring w-12 h-12">
            <i :class="metric.icon" class="quantum-metric-icon text-lg"></i>
          </div>
          <div class="quantum-metric-status">
            <span class="quantum-hud-element text-xs px-2 py-1 rounded">{{ metric.status }}</span>
          </div>
        </div>
        <div class="quantum-metric-content text-center">
          <div class="quantum-metric-value quantum-text-glow text-2xl font-bold mb-2">
            {{ metric.value }}
          </div>
          <div class="quantum-metric-label text-sm text-[var(--quantum-fg-secondary)] mb-3">
            {{ metric.label }}
          </div>
          <div class="quantum-metric-progress mb-2">
            <div class="quantum-progress-bar h-2 rounded-full overflow-hidden bg-[var(--quantum-bg-elevated)]">
              <div class="quantum-progress-fill h-full transition-all duration-500 bg-gradient-to-r from-[var(--quantum-primary)] to-[var(--quantum-accent)]"
                   :style="{ width: `${metric.progress}%` }"></div>
            </div>
          </div>
          <div class="quantum-metric-change text-sm flex items-center justify-center gap-1"
               :class="metric.changeType === 'positive' ? 'text-[var(--quantum-success)]' : 'text-[var(--quantum-error)]'">
            <i :class="getChangeIcon(metric.changeType)" class="text-xs"></i>
            <span>{{ Math.abs(metric.change) }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 🔍 量子用户搜索与筛选 -->
    <div class="quantum-card-hologram quantum-search-panel p-4 mb-6 rounded-xl">
      <div class="search-controls flex flex-wrap gap-4 items-center">
        <div class="search-input-group flex-1 min-w-64">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索用户姓名、邮箱或ID..."
            class="quantum-input w-full px-4 py-2 bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)] rounded-lg text-[var(--quantum-fg-primary)] placeholder-[var(--quantum-fg-muted)]"
          />
        </div>
        <div class="filter-controls flex gap-2">
          <select v-model="statusFilter" class="quantum-select px-3 py-2 bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)] rounded-lg text-[var(--quantum-fg-primary)]">
            <option value="">所有状态</option>
            <option value="active">活跃</option>
            <option value="inactive">非活跃</option>
            <option value="suspended">已暂停</option>
          </select>
          <select v-model="roleFilter" class="quantum-select px-3 py-2 bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)] rounded-lg text-[var(--quantum-fg-primary)]">
            <option value="">所有角色</option>
            <option value="admin">管理员</option>
            <option value="developer">开发者</option>
            <option value="user">普通用户</option>
            <option value="guest">访客</option>
          </select>
        </div>
      </div>
    </div>



    <!-- 👥 量子用户表格 -->
    <div class="quantum-users-table quantum-card-hologram rounded-xl overflow-hidden mb-6">
      <div class="table-header bg-[var(--quantum-bg-elevated)] px-6 py-4 border-b border-[var(--quantum-border-color)]">
        <h3 class="quantum-text-matrix text-lg font-semibold">USER DATABASE</h3>
      </div>

      <div class="table-content overflow-x-auto">
        <table class="w-full">
          <thead class="bg-[var(--quantum-bg-elevated)] border-b border-[var(--quantum-border-color)]">
            <tr>
              <th class="text-left px-6 py-4 text-[var(--quantum-fg-secondary)] font-medium text-sm">用户</th>
              <th class="text-left px-6 py-4 text-[var(--quantum-fg-secondary)] font-medium text-sm">邮箱</th>
              <th class="text-left px-6 py-4 text-[var(--quantum-fg-secondary)] font-medium text-sm">权限</th>
              <th class="text-left px-6 py-4 text-[var(--quantum-fg-secondary)] font-medium text-sm">状态</th>
              <th class="text-left px-6 py-4 text-[var(--quantum-fg-secondary)] font-medium text-sm">登录次数</th>
              <th class="text-left px-6 py-4 text-[var(--quantum-fg-secondary)] font-medium text-sm">最后登录</th>
              <th class="text-center px-6 py-4 text-[var(--quantum-fg-secondary)] font-medium text-sm">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="user in filteredUsers"
              :key="user.id"
              class="border-b border-[var(--quantum-border-color)] hover:bg-[var(--quantum-bg-elevated)] transition-colors cursor-pointer"
              @click="selectUser(user)"
            >
              <!-- 用户信息 -->
              <td class="px-6 py-4">
                <div class="flex items-center">
                  <div class="user-avatar quantum-energy-ring w-10 h-10 p-1 mr-3">
                    <img v-if="user.avatar_url" :src="user.avatar_url" :alt="user.username" class="w-full h-full object-cover rounded-full" />
                    <div v-else class="w-full h-full bg-[var(--quantum-bg-elevated)] rounded-full flex items-center justify-center">
                      <i class="i-carbon-user text-lg text-[var(--quantum-fg-secondary)]"></i>
                    </div>
                  </div>
                  <div>
                    <div class="quantum-text-glow font-medium">
                      {{ user.full_name || user.username }}
                    </div>
                    <div class="text-[var(--quantum-fg-muted)] text-sm font-mono">
                      #{{ user.id }}
                    </div>
                  </div>
                </div>
              </td>

              <!-- 邮箱 -->
              <td class="px-6 py-4 text-[var(--quantum-fg-primary)]">
                {{ user.email }}
              </td>

              <!-- 权限 -->
              <td class="px-6 py-4">
                <div class="flex flex-wrap gap-1">
                  <!-- 超级管理员标签 -->
                  <span v-if="user.is_superuser"
                        class="permission-badge superuser-badge inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-semibold">
                    <i class="i-carbon-crown text-xs"></i>
                    超级管理员
                  </span>

                  <!-- 角色标签 -->
                  <span v-for="role in getUserRoles(user)" :key="role.name"
                        class="permission-badge role-badge inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium"
                        :class="getRoleBadgeClass(role.name)">
                    <i :class="getRoleIcon(role.name)" class="text-xs"></i>
                    {{ role.display_name }}
                  </span>

                  <!-- 普通用户标签 -->
                  <span v-if="!user.is_superuser && (!user.roles || user.roles.length === 0)"
                        class="permission-badge user-badge inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium">
                    <i class="i-carbon-user text-xs"></i>
                    普通用户
                  </span>

                  <!-- 权限数量指示器 -->
                  <span v-if="user.permissions && user.permissions.length > 0"
                        class="permission-count-badge inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs"
                        :title="`拥有 ${user.permissions.length} 个权限`">
                    <i class="i-carbon-security text-xs"></i>
                    {{ user.permissions.length }}
                  </span>
                </div>
              </td>

              <!-- 状态 -->
              <td class="px-6 py-4">
                <span
                  class="quantum-hud-element px-3 py-1 rounded text-sm font-medium"
                  :class="getStatusClass(user.is_active)"
                >
                  {{ user.is_active ? 'ACTIVE' : 'INACTIVE' }}
                </span>
              </td>

              <!-- 登录次数 -->
              <td class="px-6 py-4 text-[var(--quantum-fg-primary)] text-sm quantum-text-glow">
                {{ user.login_count || 0 }}
              </td>

              <!-- 最后登录 -->
              <td class="px-6 py-4 text-[var(--quantum-fg-primary)] text-sm">
                {{ formatLastLogin(user.last_login_at) }}
              </td>

              <!-- 操作按钮 -->
              <td class="px-6 py-4" @click.stop>
                <div class="flex gap-2 justify-center">
                  <button @click="editUser(user)"
                          class="quantum-btn-pulse text-xs px-3 py-1"
                          title="编辑用户">
                    <i class="i-carbon-edit"></i>
                  </button>
                  <button @click="managePermissions(user)"
                          class="quantum-btn-secondary text-xs px-3 py-1"
                          title="管理权限">
                    <i class="i-carbon-security"></i>
                  </button>
                  <button @click="resetPassword(user)"
                          class="quantum-btn-warning text-xs px-3 py-1"
                          title="重置密码">
                    <i class="i-carbon-password"></i>
                  </button>
                  <button @click="sendNotification(user)"
                          class="quantum-btn-info text-xs px-3 py-1"
                          title="发送通知">
                    <i class="i-carbon-email"></i>
                  </button>
                  <button @click="deleteUser(user)"
                          class="quantum-btn-danger text-xs px-3 py-1"
                          title="删除用户">
                    <i class="i-carbon-trash-can"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>



    <!-- 创建用户模态框 -->
    <div v-if="showCreateModal" class="modal-overlay fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="cancelCreate">
      <div class="modal-content quantum-card-hologram p-6 rounded-xl max-w-md w-full mx-4" @click.stop>
        <div class="modal-header flex justify-between items-center mb-6">
          <h3 class="modal-title text-xl font-semibold quantum-text-matrix">创建新用户</h3>
          <button @click="cancelCreate" class="close-btn text-[var(--quantum-fg-muted)] hover:text-[var(--quantum-fg-primary)] transition-colors">
            <i class="i-carbon-close text-xl"></i>
          </button>
        </div>

        <div class="modal-body space-y-4">
          <div class="form-group">
            <label class="form-label text-sm text-[var(--quantum-fg-secondary)] mb-2 block">用户名 *</label>
            <input
              v-model="createForm.username"
              type="text"
              class="form-input w-full p-3 rounded-lg bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)] text-[var(--quantum-fg-primary)] focus:border-[var(--quantum-primary)] focus:outline-none transition-colors"
              placeholder="请输入用户名"
              required
            />
          </div>

          <div class="form-group">
            <label class="form-label text-sm text-[var(--quantum-fg-secondary)] mb-2 block">邮箱 *</label>
            <input
              v-model="createForm.email"
              type="email"
              class="form-input w-full p-3 rounded-lg bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)] text-[var(--quantum-fg-primary)] focus:border-[var(--quantum-primary)] focus:outline-none transition-colors"
              placeholder="请输入邮箱"
              required
            />
          </div>

          <div class="form-group">
            <label class="form-label text-sm text-[var(--quantum-fg-secondary)] mb-2 block">全名</label>
            <input
              v-model="createForm.full_name"
              type="text"
              class="form-input w-full p-3 rounded-lg bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)] text-[var(--quantum-fg-primary)] focus:border-[var(--quantum-primary)] focus:outline-none transition-colors"
              placeholder="请输入全名"
            />
          </div>

          <div class="form-group">
            <label class="form-label text-sm text-[var(--quantum-fg-secondary)] mb-2 block">密码 *</label>
            <input
              v-model="createForm.password"
              type="password"
              class="form-input w-full p-3 rounded-lg bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)] text-[var(--quantum-fg-primary)] focus:border-[var(--quantum-primary)] focus:outline-none transition-colors"
              placeholder="请输入密码"
              required
            />
          </div>

          <div class="form-group">
            <label class="status-toggle flex items-center cursor-pointer">
              <input
                type="checkbox"
                v-model="createForm.is_active"
                class="sr-only"
              />
              <div class="toggle-bg w-12 h-6 bg-[var(--quantum-bg-elevated)] rounded-full relative transition-colors border" :class="{ 'bg-[var(--quantum-primary)] border-[var(--quantum-primary)]': createForm.is_active, 'border-[var(--quantum-border-color)]': !createForm.is_active }">
                <div class="toggle-dot w-5 h-5 bg-white rounded-full absolute top-0.5 transition-transform" :class="{ 'translate-x-6': createForm.is_active, 'translate-x-0.5': !createForm.is_active }"></div>
              </div>
              <span class="toggle-label ml-3 text-sm">{{ createForm.is_active ? '激活用户' : '禁用用户' }}</span>
            </label>
          </div>

          <div class="form-group">
            <label class="role-toggle flex items-center cursor-pointer">
              <input
                type="checkbox"
                v-model="createForm.is_superuser"
                class="sr-only"
              />
              <div class="toggle-bg w-12 h-6 bg-[var(--quantum-bg-elevated)] rounded-full relative transition-colors border" :class="{ 'bg-[var(--quantum-secondary)] border-[var(--quantum-secondary)]': createForm.is_superuser, 'border-[var(--quantum-border-color)]': !createForm.is_superuser }">
                <div class="toggle-dot w-5 h-5 bg-white rounded-full absolute top-0.5 transition-transform" :class="{ 'translate-x-6': createForm.is_superuser, 'translate-x-0.5': !createForm.is_superuser }"></div>
              </div>
              <span class="toggle-label ml-3 text-sm">{{ createForm.is_superuser ? '超级管理员' : '普通用户' }}</span>
            </label>
          </div>
        </div>

        <div class="modal-footer flex gap-3 pt-6">
          <button
            @click="createUser"
            :disabled="isCreating"
            class="create-btn flex items-center gap-2 px-4 py-2 bg-[var(--quantum-primary)] text-white rounded-lg hover:brightness-110 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <i class="i-carbon-user-plus"></i>
            {{ isCreating ? '创建中...' : '创建用户' }}
          </button>
          <button @click="cancelCreate" class="cancel-btn flex items-center gap-2 px-4 py-2 bg-[var(--quantum-bg-elevated)] text-[var(--quantum-fg-primary)] border border-[var(--quantum-border-color)] rounded-lg hover:bg-[var(--quantum-bg-surface)] transition-all">
            <i class="i-carbon-close"></i>
            取消
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'

// 路由
const router = useRouter()

// API
const { users: usersApi } = useApi()

// 响应式数据
const users = ref([])
const userMetrics = ref([])
const isLoading = ref(false)
const error = ref('')

// 搜索和筛选
const searchQuery = ref('')
const statusFilter = ref('')
const roleFilter = ref('')

const currentPage = ref(1)
const pageSize = ref(20)
const totalUsers = ref(0)
const totalPages = ref(0)

// 计算属性
const activeUsers = computed(() => users.value.filter((u: any) => u.is_active).length)
const adminUsers = computed(() => users.value.filter((u: any) => u.is_superuser).length)
const lastSyncTime = computed(() => new Date().toLocaleTimeString())

const filteredUsers = computed(() => {
  return users.value.filter((user: any) => {
    const matchesSearch = !searchQuery.value ||
      user.username?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      user.email?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      user.full_name?.toLowerCase().includes(searchQuery.value.toLowerCase())

    const matchesStatus = !statusFilter.value ||
      (statusFilter.value === 'active' ? user.is_active : !user.is_active)
    const matchesRole = !roleFilter.value ||
      (roleFilter.value === 'admin' ? user.is_superuser : !user.is_superuser)

    return matchesSearch && matchesStatus && matchesRole
  })
})

// 用户统计指标（基于真实数据）
const userMetricsComputed = computed(() => [
  {
    id: 1,
    label: 'Total Users',
    value: totalUsers.value,
    icon: 'i-carbon-user-multiple',
    status: 'GROWING',
    progress: 88,
    change: 12,
    changeType: 'positive'
  },
  {
    id: 2,
    label: 'Active Users',
    value: activeUsers.value,
    icon: 'i-carbon-user-online',
    status: 'ONLINE',
    progress: 76,
    change: 8,
    changeType: 'positive'
  },
  {
    id: 3,
    label: 'New Signups',
    value: Math.floor(totalUsers.value * 0.1),
    icon: 'i-carbon-user-plus',
    status: 'MONTHLY',
    progress: 65,
    change: 23,
    changeType: 'positive'
  },
  {
    id: 4,
    label: 'Admin Users',
    value: adminUsers.value,
    icon: 'i-carbon-user-admin',
    status: 'SECURE',
    progress: 95,
    change: 2,
    changeType: 'positive'
  }
])

// 方法
const getStatusClass = (isActive: boolean) => {
  return isActive ? 'quantum-status-active' : 'quantum-status-inactive'
}

const getChangeIcon = (changeType: string) => {
  return changeType === 'positive' ? 'i-carbon-arrow-up' : 'i-carbon-arrow-down'
}

const formatDate = (dateString: string) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const formatLastLogin = (dateString: string) => {
  if (!dateString) return '从未登录'
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  return `${days}天前`
}

// 加载用户列表
const loadUsers = async () => {
  isLoading.value = true
  error.value = ''

  try {
    const params: any = {
      page: currentPage.value,
      page_size: pageSize.value
    }

    // 只添加有值的参数
    if (searchQuery.value && searchQuery.value.trim()) {
      params.search = searchQuery.value.trim()
    }

    if (statusFilter.value === 'active') {
      params.is_active = true
    } else if (statusFilter.value === 'inactive') {
      params.is_active = false
    }

    if (roleFilter.value === 'admin') {
      params.is_superuser = true
    } else if (roleFilter.value === 'user') {
      params.is_superuser = false
    }

    const response = await usersApi.list(params)

    // 处理用户数据
    users.value = response.users || []
    totalUsers.value = response.total || 0
    totalPages.value = response.total_pages || 0

    console.log('用户列表加载成功:', response)

    // 🎯 加载用户角色信息
    if (users.value.length > 0) {
      const userIds = users.value.map(user => user.id)
      await loadUserRoles(userIds)
      console.log('✅ 用户角色信息加载完成')
    }
  } catch (err: any) {
    console.error('加载用户列表失败:', err)
    error.value = err.message || '加载用户列表失败'
  } finally {
    isLoading.value = false
  }
}

// 刷新用户列表
const refreshUsers = () => {
  currentPage.value = 1
  loadUsers()
}

// 搜索用户
const searchUsers = () => {
  currentPage.value = 1
  loadUsers()
}

// 筛选用户
const filterUsers = () => {
  currentPage.value = 1
  loadUsers()
}

// 创建用户相关
const showCreateModal = ref(false)
const createForm = ref({
  username: '',
  email: '',
  full_name: '',
  password: '',
  is_active: true,
  is_superuser: false,
  role_ids: []
})
const isCreating = ref(false)

const addUser = () => {
  showCreateModal.value = true
  // 重置表单
  createForm.value = {
    username: '',
    email: '',
    full_name: '',
    password: '',
    is_active: true,
    is_superuser: false,
    role_ids: []
  }
}

const createUser = async () => {
  if (!createForm.value.username || !createForm.value.email || !createForm.value.password) {
    alert('请填写必填字段：用户名、邮箱、密码')
    return
  }

  isCreating.value = true

  try {
    const response = await usersApi.create(createForm.value)
    console.log('用户创建成功:', response)
    alert('用户创建成功！')
    showCreateModal.value = false
    refreshUsers()
  } catch (err: any) {
    console.error('创建用户失败:', err)
    alert('创建用户失败：' + (err.message || '未知错误'))
  } finally {
    isCreating.value = false
  }
}

const cancelCreate = () => {
  showCreateModal.value = false
}



const editUser = (user: any) => {
  console.log('Editing user:', user.id)
  // 跳转到用户详情页面进行编辑
  router.push(`/users/${user.id}`)
}

const deleteUser = async (user: any) => {
  if (confirm(`确定要删除用户 "${user.full_name || user.username}" 吗？此操作不可恢复。`)) {
    try {
      await usersApi.delete(user.id)
      console.log('用户删除成功:', user.id)
      alert('用户删除成功！')
      refreshUsers()
    } catch (err: any) {
      console.error('删除用户失败:', err)
      alert('删除用户失败：' + (err.message || '未知错误'))
    }
  }
}

const viewProfile = (user: any) => {
  // 跳转到用户详情页面
  router.push(`/users/${user.id}`)
}

const managePermissions = (user: any) => {
  console.log('Managing permissions for user:', user.id)
  // 跳转到用户权限管理页面
  router.push(`/users/${user.id}/permissions`)
}

const resetPassword = (user: any) => {
  console.log('Resetting password for user:', user.id)
  // TODO: 实现重置密码功能
  alert(`重置用户 ${user.username} 的密码功能正在开发中`)
}

const sendNotification = (user: any) => {
  console.log('Sending notification to user:', user.id)
  // TODO: 实现发送通知功能
  alert(`向用户 ${user.username} 发送通知功能正在开发中`)
}

// 选择用户（跳转到用户详情页面）
const selectUser = (user: any) => {
  console.log('选择用户:', user.username)
  // 跳转到用户详情页面
  router.push(`/users/${user.id}`)
}

// 🎨 权限标签相关方法
const getUserRoles = (user: any) => {
  // 临时解决方案：根据用户ID获取角色信息
  const userRoleInfo = userRolesMap.value[user.id]
  if (userRoleInfo && userRoleInfo.roles) {
    return userRoleInfo.roles.filter(role => role.is_active)
  }

  // 如果没有详细角色信息，根据is_superuser推断
  if (user.is_superuser) {
    return [{ name: 'admin', display_name: '系统管理员' }]
  }

  return []
}

// 用户角色信息映射
const userRolesMap = ref<Record<number, any>>({})

// 批量加载用户角色信息
const loadUserRoles = async (userIds: number[]) => {
  try {
    // 为每个用户单独获取角色信息
    const promises = userIds.map(async (userId) => {
      try {
        const roleInfo = await permissionCheckApi.getUserPermissions(userId)
        userRolesMap.value[userId] = roleInfo
      } catch (error) {
        console.warn(`获取用户 ${userId} 角色信息失败:`, error)
        userRolesMap.value[userId] = { roles: [], permissions: [] }
      }
    })

    await Promise.all(promises)
  } catch (error) {
    console.error('批量加载用户角色失败:', error)
  }
}

const getRoleIcon = (roleName: string) => {
  const iconMap = {
    'admin': 'i-carbon-crown',
    'manager': 'i-carbon-user-role',
    'developer': 'i-carbon-code',
    'operator': 'i-carbon-tools',
    'user': 'i-carbon-user'
  }
  return iconMap[roleName] || 'i-carbon-user-role'
}

const getRoleBadgeClass = (roleName: string) => {
  const classMap = {
    'admin': 'admin-role-badge',
    'manager': 'manager-role-badge',
    'developer': 'developer-role-badge',
    'operator': 'operator-role-badge',
    'user': 'user-role-badge'
  }
  return classMap[roleName] || 'default-role-badge'
}

// 监听搜索和筛选变化
watch([searchQuery, statusFilter, roleFilter], () => {
  // 防抖搜索
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    filterUsers()
  }, 500)
})

let searchTimeout: any = null

// 页面挂载时加载数据
onMounted(() => {
  console.log('Quantum User Management Matrix initialized')
  loadUsers()
})
</script>

<style scoped>
.quantum-users-page {
  padding: var(--space-6);
  min-height: 100vh;
}

.quantum-users-grid {
  animation: quantumFadeIn 0.6s ease-out;
}

.quantum-user-card {
  transition: all var(--transition-normal);
  border: 1px solid var(--quantum-border-color);
}

.quantum-user-card:hover {
  transform: translateY(-4px);
  border-color: var(--quantum-primary);
  box-shadow: var(--quantum-glow-primary);
}

.quantum-status-active {
  background: rgba(0, 255, 65, 0.2);
  border-color: var(--quantum-success);
  color: var(--quantum-success);
}

.quantum-status-inactive {
  background: rgba(255, 170, 0, 0.2);
  border-color: var(--quantum-warning);
  color: var(--quantum-warning);
}

.quantum-status-suspended {
  background: rgba(255, 71, 87, 0.2);
  border-color: var(--quantum-error);
  color: var(--quantum-error);
}

/* 🎨 权限标签样式 */
.permission-badge {
  transition: all var(--transition-fast);
  border: 1px solid transparent;
  backdrop-filter: blur(8px);
  font-weight: 600;
  letter-spacing: 0.025em;
}

.permission-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 超级管理员标签 */
.superuser-badge {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  border-color: #ff6b6b;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.superuser-badge:hover {
  background: linear-gradient(135deg, #ff5252, #e53e3e);
  box-shadow: 0 4px 16px rgba(255, 107, 107, 0.4);
}

/* 角色标签样式 */
.admin-role-badge {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.manager-role-badge {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: white;
  border-color: #f093fb;
  box-shadow: 0 2px 8px rgba(240, 147, 251, 0.3);
}

.developer-role-badge {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: white;
  border-color: #4facfe;
  box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
}

.operator-role-badge {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
  color: #1a202c;
  border-color: #43e97b;
  box-shadow: 0 2px 8px rgba(67, 233, 123, 0.3);
}

.user-role-badge {
  background: linear-gradient(135deg, #a8edea, #fed6e3);
  color: #4a5568;
  border-color: #a8edea;
  box-shadow: 0 2px 8px rgba(168, 237, 234, 0.3);
}

.default-role-badge {
  background: linear-gradient(135deg, #d299c2, #fef9d7);
  color: #4a5568;
  border-color: #d299c2;
  box-shadow: 0 2px 8px rgba(210, 153, 194, 0.3);
}

/* 普通用户标签 */
.user-badge {
  background: rgba(113, 128, 150, 0.1);
  color: var(--quantum-fg-secondary);
  border-color: rgba(113, 128, 150, 0.3);
}

.user-badge:hover {
  background: rgba(113, 128, 150, 0.2);
  border-color: rgba(113, 128, 150, 0.5);
}

/* 权限数量指示器 */
.permission-count-badge {
  background: rgba(var(--quantum-primary-rgb), 0.1);
  color: var(--quantum-primary);
  border-color: rgba(var(--quantum-primary-rgb), 0.3);
  font-family: 'JetBrains Mono', monospace;
}

.permission-count-badge:hover {
  background: rgba(var(--quantum-primary-rgb), 0.2);
  border-color: var(--quantum-primary);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .permission-badge {
    font-size: 0.625rem;
    padding: 0.25rem 0.5rem;
  }
}

@keyframes quantumFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
