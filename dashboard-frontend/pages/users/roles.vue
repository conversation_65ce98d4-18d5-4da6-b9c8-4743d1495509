<template>
  <div class="quantum-roles-page">
    <!-- 🌌 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title quantum-text-neon">
          🎭 QUANTUM ROLE MANAGEMENT MATRIX
        </h1>
        <p class="page-subtitle quantum-text-glow">
          >> ROLE & PERMISSION ORCHESTRATION SYSTEM <<
        </p>
        <div class="quantum-status-bar">
          <span class="quantum-hud-element">
            <i class="i-carbon-user-role"></i>
            ROLES: {{ totalRoles }}
          </span>
          <span class="quantum-hud-element">
            <i class="i-carbon-security"></i>
            PERMISSIONS: {{ totalPermissions }}
          </span>
          <span class="quantum-hud-element">
            <i class="i-carbon-user-multiple"></i>
            ASSIGNED: {{ assignedUsers }}
          </span>
          <span class="quantum-hud-element">
            <i class="i-carbon-time"></i>
            SYNC: {{ lastSyncTime }}
          </span>
        </div>
      </div>
      <div class="header-actions">
        <button @click="refreshRoles" class="quantum-btn-secondary quantum-ripple">
          <i class="i-carbon-refresh"></i>
          <span>刷新角色</span>
        </button>
        <button @click="createRole" class="quantum-btn-primary quantum-ripple">
          <i class="i-carbon-add"></i>
          <span>创建角色</span>
        </button>
      </div>
    </div>

    <!-- 📊 角色统计面板 -->
    <div class="quantum-metrics-grid">
      <div v-for="metric in roleMetrics" :key="metric.id"
           class="quantum-card-hologram quantum-metric-card">
        <div class="quantum-metric-header">
          <div class="quantum-metric-ring">
            <i :class="metric.icon" class="quantum-metric-icon"></i>
          </div>
          <div class="quantum-metric-status">
            <span class="quantum-hud-element">{{ metric.status }}</span>
          </div>
        </div>
        <div class="quantum-metric-content">
          <div class="quantum-metric-value quantum-text-glow">
            {{ metric.value }}
          </div>
          <div class="quantum-metric-label">
            {{ metric.label }}
          </div>
          <div class="quantum-metric-progress">
            <div class="quantum-progress-bar">
              <div class="quantum-progress-fill" :style="{ width: `${metric.progress}%` }"></div>
            </div>
          </div>
          <div class="quantum-metric-change" :class="metric.changeType">
            <i :class="getChangeIcon(metric.changeType)"></i>
            <span>{{ Math.abs(metric.change) }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 🎭 角色管理网格 -->
    <div class="roles-layout">
      <!-- 角色列表 -->
      <div class="roles-section">
        <div class="section-header">
          <h2 class="quantum-text-neon">系统角色</h2>
          <div class="search-controls">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="搜索角色..."
              class="quantum-input"
            />
          </div>
        </div>
        
        <div class="roles-grid">
          <div v-for="role in filteredRoles" :key="role.id"
               class="quantum-card-hologram role-card"
               :class="{ 'selected': selectedRole?.id === role.id }"
               @click="selectRole(role)">
            <div class="role-header">
              <div class="role-icon">
                <i :class="role.icon" class="text-2xl"></i>
              </div>
              <div class="role-info">
                <h3 class="role-name quantum-text-matrix">{{ role.displayName }}</h3>
                <p class="role-description">{{ role.description }}</p>
              </div>
              <div class="role-status">
                <span class="quantum-hud-element" :class="role.status">
                  {{ role.status.toUpperCase() }}
                </span>
              </div>
            </div>
            
            <div class="role-stats">
              <div class="stat-item">
                <span class="stat-value quantum-text-glow">{{ role.userCount }}</span>
                <span class="stat-label">用户</span>
              </div>
              <div class="stat-item">
                <span class="stat-value quantum-text-glow">{{ role.permissionCount }}</span>
                <span class="stat-label">权限</span>
              </div>
              <div class="stat-item">
                <span class="stat-value quantum-text-glow">{{ role.level }}</span>
                <span class="stat-label">等级</span>
              </div>
            </div>
            
            <div class="role-actions">
              <button @click.stop="editRole(role)" class="quantum-btn-ghost">
                <i class="i-carbon-edit"></i>
                编辑
              </button>
              <button @click.stop="duplicateRole(role)" class="quantum-btn-ghost">
                <i class="i-carbon-copy"></i>
                复制
              </button>
              <button @click.stop="deleteRole(role)" class="quantum-btn-ghost text-red-400">
                <i class="i-carbon-trash-can"></i>
                删除
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 权限详情面板 -->
      <div v-if="selectedRole" class="permissions-section">
        <div class="section-header">
          <h2 class="quantum-text-neon">权限配置</h2>
          <div class="permission-actions">
            <button @click="savePermissions" class="quantum-btn-primary">
              <i class="i-carbon-save"></i>
              保存权限
            </button>
          </div>
        </div>
        
        <div class="quantum-card-hologram permissions-panel">
          <div class="role-summary">
            <div class="role-avatar">
              <i :class="selectedRole.icon" class="text-3xl"></i>
            </div>
            <div class="role-details">
              <h3 class="quantum-text-matrix">{{ selectedRole.displayName }}</h3>
              <p class="role-description">{{ selectedRole.description }}</p>
              <div class="role-meta">
                <span class="quantum-hud-element">等级: {{ selectedRole.level }}</span>
                <span class="quantum-hud-element">用户: {{ selectedRole.userCount }}</span>
              </div>
            </div>
          </div>
          
          <div class="permissions-tree">
            <div v-for="category in permissionCategories" :key="category.id"
                 class="permission-category">
              <div class="category-header" @click="toggleCategory(category.id)">
                <i :class="category.expanded ? 'i-carbon-chevron-down' : 'i-carbon-chevron-right'"></i>
                <i :class="category.icon"></i>
                <span class="category-name">{{ category.name }}</span>
                <span class="permission-count">({{ category.permissions.length }})</span>
              </div>
              
              <div v-if="category.expanded" class="permissions-list">
                <div v-for="permission in category.permissions" :key="permission.id"
                     class="permission-item">
                  <label class="permission-checkbox">
                    <input
                      type="checkbox"
                      :checked="hasPermission(permission.id)"
                      @change="togglePermission(permission.id)"
                    />
                    <span class="checkmark"></span>
                    <div class="permission-info">
                      <span class="permission-name">{{ permission.name }}</span>
                      <span class="permission-description">{{ permission.description }}</span>
                    </div>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useApi } from '~/composables/useApi'
import { useAuthStore } from '~/stores/authStore'

// 页面元数据
definePageMeta({
  title: 'Role Management - AR System Dashboard',
  description: 'Quantum role and permission management system'
})

// API和权限
const { roles: rolesApi } = useApi()
const { user: currentUser } = useAuthStore()

// 响应式数据
const searchQuery = ref('')
const selectedRole = ref(null)
const roles = ref([])
const isLoading = ref(false)
const error = ref('')

// 🔒 权限级别定义 (与数据库保持一致)
const ROLE_LEVELS = {
  'admin': 10,      // 超级管理员 - 系统最高权限
  'manager': 6,     // 部门经理 - 部门级管理权限
  'developer': 5,   // 开发者 - 开发工具和API权限
  'operator': 3,    // 操作员 - 基本操作权限
  'user': 1         // 普通用户 - 基础功能权限
}

// 🔒 获取当前用户的权限级别
const getCurrentUserLevel = () => {
  if (!currentUser?.is_superuser) return 1 // 普通用户级别

  // 超级管理员可以看到所有角色
  return 10
}

// 🔒 过滤用户可见的角色
const filterVisibleRoles = (allRoles) => {
  const userLevel = getCurrentUserLevel()

  return allRoles.filter(role => {
    const roleLevel = ROLE_LEVELS[role.name] || 1
    // 用户只能看到自己级别及以下的角色
    return roleLevel <= userLevel
  })
}

// 加载角色数据
const loadRoles = async () => {
  isLoading.value = true
  error.value = ''

  try {
    console.log('正在加载角色列表...')
    const response = await rolesApi.list({ page_size: 100, include_system: true })

    // 转换API数据为前端显示格式
    const allRoles = (response || []).map(role => ({
      id: role.id,
      name: role.name, // 保持原始name用于权限判断
      displayName: role.display_name || role.name,
      description: role.description || '暂无描述',
      icon: getRoleIcon(role.name),
      status: role.is_active ? 'active' : 'inactive',
      level: ROLE_LEVELS[role.name] || 1,
      userCount: role.user_count || 0,
      permissionCount: role.permissions?.length || 0,
      permissions: role.permissions?.map(p => p.name) || [],
      isSystem: role.is_system || false
    }))

    // 🔒 根据用户权限过滤可见角色
    roles.value = filterVisibleRoles(allRoles)

    console.log('角色列表加载成功:', roles.value)
    console.log('当前用户权限级别:', getCurrentUserLevel())
  } catch (err) {
    console.error('加载角色列表失败:', err)
    error.value = '加载角色列表失败，请检查网络连接或稍后重试'
  } finally {
    isLoading.value = false
  }
}

// 根据角色名称获取图标
const getRoleIcon = (roleName) => {
  const iconMap = {
    'admin': 'i-carbon-crown',        // 超级管理员 - 皇冠图标
    'manager': 'i-carbon-user-role',  // 部门经理 - 用户角色图标
    'developer': 'i-carbon-code',     // 开发者 - 代码图标
    'operator': 'i-carbon-tools',     // 操作员 - 工具图标
    'user': 'i-carbon-user'           // 普通用户 - 用户图标
  }
  return iconMap[roleName] || 'i-carbon-user-role'
}

// 权限分类
const permissionCategories = ref([
  {
    id: 'user',
    name: '用户管理',
    icon: 'i-carbon-user-multiple',
    expanded: true,
    permissions: [
      { id: 'user.view', name: '查看用户', description: '查看用户列表和详情' },
      { id: 'user.create', name: '创建用户', description: '创建新用户账户' },
      { id: 'user.edit', name: '编辑用户', description: '修改用户信息' },
      { id: 'user.delete', name: '删除用户', description: '删除用户账户' },
      { id: 'user.manage', name: '用户管理', description: '完整的用户管理权限' }
    ]
  },
  {
    id: 'system',
    name: '系统管理',
    icon: 'i-carbon-settings',
    expanded: false,
    permissions: [
      { id: 'system.config', name: '系统配置', description: '修改系统配置' },
      { id: 'system.monitor', name: '系统监控', description: '查看系统状态' },
      { id: 'system.backup', name: '数据备份', description: '执行数据备份' },
      { id: 'system.logs', name: '日志查看', description: '查看系统日志' }
    ]
  }
])

// 角色统计指标
const roleMetrics = ref([
  {
    id: 1,
    label: 'Total Roles',
    value: 12,
    icon: 'i-carbon-user-role',
    status: 'ACTIVE',
    progress: 85,
    change: 8,
    changeType: 'positive'
  },
  {
    id: 2,
    label: 'Custom Roles',
    value: 8,
    icon: 'i-carbon-settings-adjust',
    status: 'CUSTOM',
    progress: 67,
    change: 15,
    changeType: 'positive'
  },
  {
    id: 3,
    label: 'Permissions',
    value: 45,
    icon: 'i-carbon-security',
    status: 'SECURE',
    progress: 92,
    change: 3,
    changeType: 'positive'
  },
  {
    id: 4,
    label: 'Assigned Users',
    value: 1307,
    icon: 'i-carbon-user-multiple',
    status: 'ASSIGNED',
    progress: 78,
    change: 12,
    changeType: 'positive'
  }
])

// 计算属性
const totalRoles = computed(() => roles.value.length)
const totalPermissions = computed(() => 45)
const assignedUsers = computed(() => roles.value.reduce((sum, role) => sum + role.userCount, 0))
const lastSyncTime = computed(() => new Date().toLocaleTimeString())

const filteredRoles = computed(() => {
  if (!searchQuery.value) return roles.value
  return roles.value.filter(role =>
    role.displayName.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    role.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    role.description.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// 方法
const getChangeIcon = (changeType: string) => {
  return changeType === 'positive' ? 'i-carbon-arrow-up' : 'i-carbon-arrow-down'
}

const selectRole = (role: any) => {
  selectedRole.value = role
}

const refreshRoles = () => {
  console.log('Refreshing roles...')
}

const createRole = () => {
  console.log('Creating new role...')
}

const editRole = (role: any) => {
  console.log('Editing role:', role.id)
}

const duplicateRole = (role: any) => {
  console.log('Duplicating role:', role.id)
}

const deleteRole = (role: any) => {
  console.log('Deleting role:', role.id)
}

const toggleCategory = (categoryId: string) => {
  const category = permissionCategories.value.find(c => c.id === categoryId)
  if (category) {
    category.expanded = !category.expanded
  }
}

const hasPermission = (permissionId: string) => {
  if (!selectedRole.value) return false
  return selectedRole.value.permissions.includes(permissionId) || 
         selectedRole.value.permissions.includes('*')
}

const togglePermission = (permissionId: string) => {
  if (!selectedRole.value) return
  const permissions = selectedRole.value.permissions
  const index = permissions.indexOf(permissionId)
  if (index > -1) {
    permissions.splice(index, 1)
  } else {
    permissions.push(permissionId)
  }
}

const savePermissions = () => {
  console.log('Saving permissions for role:', selectedRole.value?.id)
}

onMounted(() => {
  console.log('Quantum Role Management Matrix initialized')
  loadRoles() // 加载角色数据
})
</script>

<style scoped>
.quantum-roles-page {
  padding: var(--space-6);
  min-height: 100vh;
}

.quantum-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.roles-layout {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: var(--space-6);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
}

.roles-grid {
  display: grid;
  gap: var(--space-4);
}

.role-card {
  padding: var(--space-4);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.role-card:hover,
.role-card.selected {
  border-color: var(--quantum-primary);
  box-shadow: var(--quantum-glow-primary);
}

.role-header {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  margin-bottom: var(--space-3);
}

.role-icon {
  color: var(--quantum-primary);
}

.role-info {
  flex: 1;
}

.role-name {
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--space-1);
}

.role-description {
  color: var(--quantum-fg-secondary);
  font-size: var(--text-sm);
}

.role-stats {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-3);
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-weight: 700;
  font-size: var(--text-lg);
}

.stat-label {
  color: var(--quantum-fg-muted);
  font-size: var(--text-xs);
}

.role-actions {
  display: flex;
  gap: var(--space-2);
}

.permissions-section {
  position: sticky;
  top: var(--space-6);
  height: fit-content;
}

.permissions-panel {
  padding: var(--space-6);
}

.role-summary {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--quantum-border-color);
}

.role-avatar {
  color: var(--quantum-primary);
}

.role-meta {
  display: flex;
  gap: var(--space-2);
  margin-top: var(--space-2);
}

.permission-category {
  margin-bottom: var(--space-4);
}

.category-header {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3);
  background: var(--quantum-bg-elevated);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.category-header:hover {
  background: var(--quantum-bg-hover);
}

.permissions-list {
  padding: var(--space-2) 0;
}

.permission-item {
  padding: var(--space-2) var(--space-4);
}

.permission-checkbox {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  cursor: pointer;
}

.permission-checkbox input {
  margin: 0;
}

.permission-info {
  flex: 1;
}

.permission-name {
  display: block;
  font-weight: 500;
  margin-bottom: var(--space-1);
}

.permission-description {
  color: var(--quantum-fg-secondary);
  font-size: var(--text-sm);
}

@media (max-width: 1024px) {
  .roles-layout {
    grid-template-columns: 1fr;
  }
  
  .permissions-section {
    position: static;
  }
}
</style>
