<template>
  <div class="login-page">
    <!-- 🌌 量子背景 -->
    <div class="quantum-bg">
      <div class="quantum-particles"></div>
      <div class="quantum-waves"></div>
    </div>

    <!-- 🎯 登录卡片 -->
    <div class="login-container">
      <!-- 🎨 主题切换按钮 -->
      <div class="theme-toggle-wrapper">
        <button @click="toggleTheme" class="theme-toggle-btn" :title="isDark ? '切换到浅色模式' : '切换到深色模式'">
          <i :class="themeIcon" class="theme-icon"></i>
        </button>
      </div>

      <div class="login-card">
        <!-- 🌟 Logo区域 -->
        <div class="logo-section">
          <div class="quantum-logo">
            <i class="i-carbon-dashboard text-4xl"></i>
          </div>
          <h1 class="login-title">AR-System Dashboard</h1>
          <p class="login-subtitle">管理后台登录</p>
        </div>

        <!-- 🔐 登录表单 -->
        <form @submit.prevent="handleLogin" class="login-form">
          <div class="form-group">
            <label class="form-label">用户名或邮箱</label>
            <div class="input-wrapper">
              <i class="i-carbon-user input-icon"></i>
              <input
                v-model="loginForm.username"
                type="text"
                class="quantum-input"
                placeholder="请输入用户名或邮箱"
                required
              >
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">密码</label>
            <div class="input-wrapper">
              <i class="i-carbon-password input-icon"></i>
              <input
                v-model="loginForm.password"
                :type="showPassword ? 'text' : 'password'"
                class="quantum-input"
                placeholder="请输入密码"
                required
              >
              <button
                type="button"
                @click="showPassword = !showPassword"
                class="password-toggle"
              >
                <i :class="showPassword ? 'i-carbon-view-off' : 'i-carbon-view'"></i>
              </button>
            </div>
          </div>

          <div class="form-options">
            <label class="checkbox-wrapper">
              <input v-model="loginForm.rememberMe" type="checkbox" class="quantum-checkbox">
              <span class="checkbox-label">记住登录状态</span>
            </label>
          </div>

          <button type="submit" :disabled="isLoading" class="login-btn">
            <div v-if="isLoading" class="loading-spinner"></div>
            <i v-else class="i-carbon-login mr-3"></i>
            {{ isLoading ? '登录中...' : '登录管理后台' }}
          </button>
        </form>

        <!-- 🔗 返回前台 -->
        <div class="back-to-frontend">
          <a href="http://localhost:3002" class="back-link">
            <i class="i-carbon-arrow-left mr-2"></i>
            返回前台网站
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  layout: false,
  auth: false  // 登录页面不需要认证
})

useHead({
  title: 'Dashboard 登录 - AR-System 管理后台',
  meta: [
    { name: 'description', content: 'AR-System 管理后台登录页面' }
  ]
})

// 响应式状态
const isLoading = ref(false)
const showPassword = ref(false)

// 表单数据
const loginForm = ref({
  username: '',
  password: '',
  rememberMe: false
})

// Store
const authStore = useAuthStore()

// 主题管理
const { isDark, toggleTheme, themeIcon } = useTheme()

// 检查是否已登录
onMounted(() => {
  authStore.initAuth()
  if (authStore.isLoggedIn) {
    navigateTo('/')
  }
})

// 登录处理
const handleLogin = async () => {
  isLoading.value = true
  try {
    await authStore.login({
      username: loginForm.value.username,
      password: loginForm.value.password
    })
    
    console.log('Dashboard login successful')
    await navigateTo('/')
  } catch (error) {
    console.error('Dashboard login failed:', error)
    alert('登录失败：' + (error.message || '请检查您的凭据'))
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
/* 🌟 Dashboard 登录页面样式 - 遵循量子变量规则 */
.login-page {
  min-height: 100vh;
  background: var(--quantum-matrix-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* 🌌 量子背景 */
.quantum-bg {
  position: absolute;
  inset: 0;
  opacity: 0.1;
}

[data-theme="dark"] .quantum-bg {
  opacity: 0.3;
}

.quantum-particles {
  position: absolute;
  inset: 0;
  background-image:
    radial-gradient(2px 2px at 20px 30px, var(--quantum-primary), transparent),
    radial-gradient(2px 2px at 40px 70px, var(--quantum-accent), transparent);
  background-repeat: repeat;
  background-size: 200px 200px;
  animation: particles-float var(--transition-epic) ease-in-out infinite;
}

.quantum-waves {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(0, 212, 255, 0.05) 50%,
    transparent 70%
  );
  animation: waves-move var(--transition-epic) ease-in-out infinite;
}

/* 🎯 登录容器 */
.login-container {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 400px;
  padding: var(--space-6);
}

/* 🎨 主题切换按钮 */
.theme-toggle-wrapper {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  z-index: 20;
}

.theme-toggle-btn {
  width: var(--space-12);
  height: var(--space-12);
  background: var(--quantum-bg-glass);
  border: 1px solid var(--quantum-border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
  box-shadow: var(--quantum-shadow-glass);
}

.theme-toggle-btn:hover {
  background: var(--quantum-bg-surface);
  border-color: var(--quantum-primary);
  box-shadow: var(--quantum-glow-primary);
  transform: scale(1.1);
}

.theme-icon {
  font-size: var(--text-lg);
  color: var(--quantum-fg-primary);
  transition: all var(--transition-fast);
}

.login-card {
  background: var(--quantum-bg-surface);
  border: 1px solid var(--quantum-border-color);
  border-radius: var(--space-6);
  padding: var(--space-12);
  backdrop-filter: blur(20px);
  box-shadow: var(--quantum-shadow-glass);
  transition: all var(--transition-normal);
}

/* 🌟 Logo区域 */
.logo-section {
  text-align: center;
  margin-bottom: var(--space-8);
}

.quantum-logo {
  width: var(--space-20);
  height: var(--space-20);
  margin: 0 auto var(--space-6);
  background: linear-gradient(135deg, var(--quantum-primary), var(--quantum-accent));
  border-radius: var(--space-5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--quantum-glow-primary);
  transition: all var(--transition-normal);
}

.quantum-logo:hover {
  transform: scale(1.05);
  box-shadow: var(--quantum-glow-primary), var(--quantum-shadow-elevated);
}

.login-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-extrabold);
  color: var(--quantum-fg-primary);
  margin-bottom: var(--space-2);
}

.login-subtitle {
  color: var(--quantum-fg-muted);
  font-size: var(--text-base);
}

/* 📝 表单样式 */
.login-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.form-group {
  position: relative;
}

.form-label {
  display: block;
  color: var(--quantum-fg-primary);
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-2);
}

.input-wrapper {
  position: relative;
}

.input-icon {
  position: absolute;
  left: var(--space-4);
  top: 50%;
  transform: translateY(-50%);
  color: var(--quantum-fg-muted);
  font-size: var(--text-lg);
  z-index: 10;
}

.quantum-input {
  width: 100%;
  padding: var(--space-4) var(--space-4) var(--space-4) var(--space-12);
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: var(--space-3);
  color: var(--quantum-fg-primary);
  font-size: var(--text-base);
  font-family: var(--font-sans);
  transition: all var(--transition-normal);
}

.quantum-input::placeholder {
  color: var(--quantum-fg-muted);
}

.quantum-input:focus {
  outline: none;
  border-color: var(--quantum-primary);
  box-shadow: var(--quantum-glow-primary);
  background: var(--quantum-bg-surface);
}

.password-toggle {
  position: absolute;
  right: var(--space-4);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--quantum-fg-muted);
  cursor: pointer;
  font-size: var(--text-lg);
  transition: color var(--transition-fast);
}

.password-toggle:hover {
  color: var(--quantum-primary);
}

/* ✅ 复选框样式 */
.form-options {
  margin: var(--space-4) 0;
}

.checkbox-wrapper {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
  gap: var(--space-3);
}

.quantum-checkbox {
  width: var(--space-5);
  height: var(--space-5);
  border: 2px solid var(--quantum-border-color);
  border-radius: var(--space-1);
  background: transparent;
  cursor: pointer;
  appearance: none;
  transition: all var(--transition-fast);
  position: relative;
  flex-shrink: 0;
  margin-top: var(--space-1);
}

.quantum-checkbox:checked {
  background: var(--quantum-primary);
  border-color: var(--quantum-primary);
  box-shadow: var(--quantum-glow-primary);
}

.quantum-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
}

.checkbox-label {
  color: var(--quantum-fg-primary);
  font-size: var(--text-sm);
  line-height: 1.5;
}

/* 🚀 量子登录按钮 */
.login-btn {
  width: 100%;
  padding: var(--space-4) var(--space-6);
  background: linear-gradient(135deg, var(--quantum-primary), var(--quantum-accent));
  border: none;
  border-radius: var(--space-3);
  color: white;
  font-weight: var(--font-bold);
  font-size: var(--text-base);
  font-family: var(--font-sans);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  margin-top: var(--space-6);
  position: relative;
  overflow: hidden;
  box-shadow: var(--quantum-glow-primary);
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--quantum-glow-primary), var(--quantum-shadow-elevated);
  filter: brightness(1.1);
}

.login-btn:active:not(:disabled) {
  transform: translateY(0) scale(0.98);
}

.login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 🔄 量子加载动画 */
.loading-spinner {
  width: var(--space-5);
  height: var(--space-5);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: quantumSpin var(--transition-epic) linear infinite;
}

/* 🔗 返回前台链接 */
.back-to-frontend {
  text-align: center;
  margin-top: var(--space-6);
}

.back-link {
  color: var(--quantum-fg-muted);
  text-decoration: none;
  font-size: var(--text-sm);
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  transition: all var(--transition-fast);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--space-2);
}

.back-link:hover {
  color: var(--quantum-primary);
  background: var(--quantum-bg-glass);
  transform: translateY(-1px);
}

/* 🎬 量子动画关键帧 */
@keyframes particles-float {
  0%, 100% {
    transform: translateY(0px);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px);
    opacity: 0.6;
  }
}

@keyframes waves-move {
  0%, 100% {
    transform: translateX(-100%);
    opacity: 0.2;
  }
  50% {
    transform: translateX(100%);
    opacity: 0.4;
  }
}

@keyframes quantumSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 📱 响应式设计 - 遵循量子断点系统 */
/* 手机端 (640px及以下) */
@media (max-width: 640px) {
  .login-container {
    padding: var(--space-4);
    max-width: 100%;
  }

  .login-card {
    padding: var(--space-8);
    border-radius: var(--space-4);
  }

  .quantum-logo {
    width: var(--space-16);
    height: var(--space-16);
  }

  .login-title {
    font-size: var(--text-2xl);
  }
}

/* 平板端 (641px - 1023px) */
@media (min-width: 641px) and (max-width: 1023px) {
  .login-container {
    max-width: 420px;
  }

  .login-card {
    padding: var(--space-10);
  }
}

/* 桌面端 (1024px+) */
@media (min-width: 1024px) {
  .login-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--quantum-glow-primary), var(--quantum-shadow-glass);
  }
}
</style>
