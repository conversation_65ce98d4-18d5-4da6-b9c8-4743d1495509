/**
 * Dashboard 认证状态管理
 * 管理管理员登录状态和权限
 */

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const token = ref('')
  const isLoading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!user.value && !!token.value)
  const currentUser = computed(() => user.value)

  // 登录方法
  const login = async (credentials: { username: string; password: string }) => {
    isLoading.value = true
    try {
      const { auth } = useApi()

      // 调用真实的登录API
      const response = await auth.login(credentials)

      // 先设置token，这样auth.me()才能正确获取用户信息
      token.value = response.access_token

      // 保存token到localStorage，这样getAuthToken()才能获取到
      if (process.client) {
        localStorage.setItem('auth_token', response.access_token)
        // 同时保存到web-frontend的格式，实现跨项目认证同步
        localStorage.setItem('auth-token', response.access_token)
      }

      // 获取用户信息（现在有token了）
      const userInfo = await auth.me()

      // 检查是否有Dashboard访问权限 (管理员或开发者)
      const hasAccess = userInfo.is_superuser ||
        userInfo.roles?.some(role => role.name === 'developer' || role.name === 'admin')

      if (!hasAccess) {
        throw new Error('您没有访问管理后台的权限，需要管理员或开发者角色')
      }

      // 设置认证状态
      user.value = {
        id: userInfo.id,
        username: userInfo.username,
        email: userInfo.email,
        full_name: userInfo.full_name || userInfo.username,
        is_superuser: userInfo.is_superuser,
        is_staff: userInfo.is_superuser, // 使用is_superuser作为is_staff
        avatar: userInfo.avatar_url,
        permissions: userInfo.permissions || [],
        roles: userInfo.roles || [] // 保存角色信息
      }

      // 保存用户数据到localStorage
      if (process.client) {
        localStorage.setItem('user_data', JSON.stringify(user.value))

        // 同时保存到web-frontend格式，实现跨项目认证同步
        const webUserData = {
          id: user.value.id,
          username: user.value.username,
          email: user.value.email,
          displayName: user.value.full_name,
          avatar: user.value.avatar || '/images/default-avatar.png',
          is_superuser: user.value.is_superuser,
          role: (() => {
            // 根据实际角色信息设置
            if (user.value.roles && user.value.roles.length > 0) {
              const primaryRole = user.value.roles[0]
              return {
                id: primaryRole.name,
                name: primaryRole.name,
                displayName: primaryRole.display_name,
                permissions: user.value.permissions?.map(p => ({
                  id: p,
                  name: p,
                  resource: p.split(':')[0] || 'system',
                  action: p.split(':')[1] || 'manage'
                })) || [],
                level: primaryRole.name === 'admin' ? 10 : primaryRole.name === 'developer' ? 5 : 1
              }
            }
            // 降级到is_superuser判断
            return {
              id: user.value.is_superuser ? 'admin' : 'user',
              name: user.value.is_superuser ? 'admin' : 'user',
              displayName: user.value.is_superuser ? '管理员' : '普通用户',
              permissions: user.value.permissions?.map(p => ({
                id: p,
                name: p,
                resource: p.split(':')[0] || 'system',
                action: p.split(':')[1] || 'manage'
              })) || [],
              level: user.value.is_superuser ? 10 : 1
            }
          })(),
          preferences: {
            theme: 'dark',
            language: 'zh',
            notifications: { email: true, push: true, sms: false },
            privacy: { profileVisibility: 'private', activityTracking: true, dataSharing: false }
          },
          isEmailVerified: true,
          isActive: true,
          createdAt: new Date(),
          lastLoginAt: new Date(),
          lastActivityAt: new Date(),
          loginCount: 1,
          profile: { bio: '', location: '', website: '' }
        }
        localStorage.setItem('user-data', JSON.stringify(webUserData))
      }

      return user.value
    } catch (error) {
      console.error('Dashboard login failed:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 登出方法
  const logout = () => {
    user.value = null
    token.value = ''

    // 清除localStorage - 同时清除两个项目的认证数据
    if (process.client) {
      // 清除dashboard的认证数据
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_data')

      // 清除web-frontend的认证数据
      localStorage.removeItem('auth-token')
      localStorage.removeItem('refresh-token')
      localStorage.removeItem('user-data')
      localStorage.removeItem('user-activities')
    }
  }

  // 初始化认证状态
  const initAuth = () => {
    if (process.client) {
      // 优先检查dashboard专用的token
      let savedToken = localStorage.getItem('auth_token')
      let savedUser = localStorage.getItem('user_data')

      // 如果没有dashboard的token，检查web-frontend的token
      if (!savedToken) {
        savedToken = localStorage.getItem('auth-token')
        savedUser = localStorage.getItem('user-data')

        // 如果找到web-frontend的认证信息，同步到dashboard
        if (savedToken && savedUser) {
          const webUser = JSON.parse(savedUser)

          // 检查是否是管理员用户
          if (webUser.role?.name === 'admin' || webUser.is_superuser) {
            // 同步token
            localStorage.setItem('auth_token', savedToken)
            token.value = savedToken

            // 转换用户数据格式
            const dashboardUser = {
              id: webUser.id,
              username: webUser.username,
              email: webUser.email,
              full_name: webUser.displayName || webUser.username,
              is_superuser: webUser.role?.name === 'admin' || webUser.is_superuser || false,
              is_staff: webUser.role?.name === 'admin' || webUser.is_superuser || false,
              avatar: webUser.avatar,
              permissions: webUser.role?.permissions?.map(p => p.name) || []
            }

            user.value = dashboardUser
            localStorage.setItem('user_data', JSON.stringify(dashboardUser))

            console.log('从web-frontend同步认证状态到dashboard')
            return
          }
        }
      }

      // 使用dashboard自己的认证信息
      if (savedToken && savedUser) {
        token.value = savedToken
        user.value = JSON.parse(savedUser)
      }
    }
  }

  // 检查权限
  const hasPermission = (permission: string) => {
    if (!user.value) return false
    if (user.value.is_superuser) return true
    return user.value.permissions?.includes(permission) || false
  }

  // 更新用户信息
  const updateUser = (userData: any) => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
      
      if (process.client) {
        localStorage.setItem('user_data', JSON.stringify(user.value))
      }
    }
  }

  return {
    // 状态
    user: readonly(user),
    token: readonly(token),
    isLoading: readonly(isLoading),
    
    // 计算属性
    isLoggedIn,
    currentUser,
    
    // 方法
    login,
    logout,
    initAuth,
    hasPermission,
    updateUser
  }
})
