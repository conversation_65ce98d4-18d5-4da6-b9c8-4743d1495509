/**
 * 🎨 简单主题管理 Composable
 */

import { ref, computed, watch, onMounted } from 'vue'

export type ThemeMode = 'light' | 'dark'

export const useTheme = () => {
  // 主题状态
  const currentTheme = ref<ThemeMode>('light')
  const isTransitioning = ref(false)

  // 计算属性
  const isDark = computed(() => currentTheme.value === 'dark')
  const isLight = computed(() => currentTheme.value === 'light')
  const themeIcon = computed(() => isDark.value ? 'i-carbon-sun' : 'i-carbon-moon')

  // 设置主题
  const setTheme = (theme: ThemeMode) => {
    if (isTransitioning.value || currentTheme.value === theme) return

    isTransitioning.value = true

    try {
      // 更新主题
      currentTheme.value = theme
      document.documentElement.setAttribute('data-theme', theme)

      // 保存到本地存储
      localStorage.setItem('dashboard-theme', theme)

      // 触发主题变化事件
      document.dispatchEvent(new CustomEvent('theme-changed', {
        detail: { theme }
      }))

    } finally {
      setTimeout(() => {
        isTransitioning.value = false
      }, 300)
    }
  }

  // 切换主题
  const toggleTheme = () => {
    const newTheme: ThemeMode = currentTheme.value === 'light' ? 'dark' : 'light'
    setTheme(newTheme)
  }

  // 初始化主题
  const initTheme = () => {
    // 从本地存储读取主题
    const savedTheme = localStorage.getItem('dashboard-theme') as ThemeMode
    if (savedTheme && ['light', 'dark'].includes(savedTheme)) {
      currentTheme.value = savedTheme
    } else {
      // 检测系统主题偏好
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      currentTheme.value = prefersDark ? 'dark' : 'light'
    }

    // 应用主题
    document.documentElement.setAttribute('data-theme', currentTheme.value)

    // 监听系统主题变化
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    mediaQuery.addEventListener('change', (e) => {
      if (!localStorage.getItem('dashboard-theme')) {
        const newTheme: ThemeMode = e.matches ? 'dark' : 'light'
        setTheme(newTheme)
      }
    })
  }

  // 监听主题变化
  watch(currentTheme, (newTheme) => {
    document.documentElement.setAttribute('data-theme', newTheme)
  })

  // 组件挂载时初始化
  onMounted(() => {
    initTheme()
  })

  return {
    // 状态
    currentTheme: readonly(currentTheme),
    isTransitioning: readonly(isTransitioning),
    
    // 计算属性
    isDark: readonly(isDark),
    isLight: readonly(isLight),
    themeIcon: readonly(themeIcon),
    
    // 方法
    setTheme,
    toggleTheme,
    initTheme
  }
}

// 全局主题实例
let globalTheme: ReturnType<typeof useTheme> | null = null

export const useGlobalTheme = () => {
  if (!globalTheme) {
    globalTheme = useTheme()
  }
  return globalTheme
}
