/**
 * Dashboard API 调用 Composable
 * 统一管理Dashboard的API调用
 */

export const useApi = () => {
  const config = useRuntimeConfig()
  // 在开发环境中直接调用后端API，在生产环境中使用代理
  const isDev = process.env.NODE_ENV === 'development'
  const apiBase = isDev ? (config.public.apiBase || 'http://localhost:8000') : ''

  // 通用API调用函数
  const apiCall = async <T>(endpoint: string, options: any = {}): Promise<T> => {
    const url = isDev ? `${apiBase}${endpoint}` : endpoint
    
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    }

    try {
      const response = await $fetch<T>(url, defaultOptions)
      return response
    } catch (error) {
      console.error(`API调用失败: ${endpoint}`, error)
      throw error
    }
  }

  // 获取认证token
  const getAuthToken = () => {
    // 从localStorage或cookie中获取token
    if (process.client) {
      return localStorage.getItem('auth_token') || ''
    }
    return ''
  }

  // 认证相关API
  const auth = {
    // 管理员登录
    login: async (credentials: {
      username: string
      password: string
    }) => {
      return await apiCall('/api/v1/auth/login', {
        method: 'POST',
        body: credentials
      })
    },

    // 获取当前用户信息
    me: async () => {
      const token = getAuthToken()
      return await apiCall('/api/v1/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    }
  }

  // 用户管理API
  const users = {
    // 获取用户列表
    list: async (params: {
      page?: number
      page_size?: number
      search?: string
      is_active?: boolean
      is_superuser?: boolean
    } = {}) => {
      const token = getAuthToken()

      // 过滤掉undefined值
      const cleanParams: any = {}
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          cleanParams[key] = value
        }
      })

      const query = new URLSearchParams(cleanParams).toString()
      const url = query ? `/api/v1/users/?${query}` : '/api/v1/users/'

      return await apiCall(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    },

    // 获取用户详情
    get: async (userId: number) => {
      const token = getAuthToken()
      return await apiCall(`/api/v1/users/${userId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    },

    // 创建用户
    create: async (userData: {
      username: string
      email: string
      password: string
      full_name?: string
      is_active?: boolean
      is_superuser?: boolean
      role_ids?: number[]
    }) => {
      const token = getAuthToken()
      return await apiCall('/api/v1/users/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: userData
      })
    },

    // 更新用户
    update: async (userId: number, userData: {
      email?: string
      full_name?: string
      is_active?: boolean
      role_ids?: number[]
    }) => {
      const token = getAuthToken()
      return await apiCall(`/api/v1/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: userData
      })
    },

    // 删除用户
    delete: async (userId: number) => {
      const token = getAuthToken()
      return await apiCall(`/api/v1/users/${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    },

    // 获取用户活动记录
    activities: async (userId: number, params: {
      page?: number
      page_size?: number
    } = {}) => {
      const token = getAuthToken()
      const query = new URLSearchParams(params as any).toString()
      return await apiCall(`/api/v1/users/${userId}/activities?${query}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    },

    // 重置用户密码
    resetPassword: async (userId: number, newPassword: string) => {
      const token = getAuthToken()
      return await apiCall(`/api/v1/users/${userId}/reset-password`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: { new_password: newPassword }
      })
    },

    // 获取用户统计
    stats: async () => {
      const token = getAuthToken()
      return await apiCall('/api/v1/users/stats', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    }
  }

  // 权限管理API
  const permissions = {
    // 获取权限列表
    list: async (params: {
      page?: number
      page_size?: number
      search?: string
      module?: string
    } = {}) => {
      const token = getAuthToken()

      const cleanParams: any = {}
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          cleanParams[key] = value
        }
      })

      const query = new URLSearchParams(cleanParams).toString()
      const url = query ? `/api/v1/permissions?${query}` : '/api/v1/permissions'

      return await apiCall(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    },

    // 创建权限
    create: async (permissionData: {
      name: string
      display_name: string
      description?: string
      resource: string
      action: string
      module?: string
    }) => {
      const token = getAuthToken()
      return await apiCall('/api/v1/permissions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: permissionData
      })
    },

    // 更新权限
    update: async (permissionId: number, permissionData: {
      display_name?: string
      description?: string
      module?: string
    }) => {
      const token = getAuthToken()
      return await apiCall(`/api/v1/permissions/${permissionId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: permissionData
      })
    },

    // 删除权限
    delete: async (permissionId: number) => {
      const token = getAuthToken()
      return await apiCall(`/api/v1/permissions/${permissionId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    }
  }

  // 角色管理API
  const roles = {
    // 获取角色列表
    list: async (params: {
      page?: number
      page_size?: number
      search?: string
      include_system?: boolean
    } = {}) => {
      const token = getAuthToken()

      const cleanParams: any = {}
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          cleanParams[key] = value
        }
      })

      const query = new URLSearchParams(cleanParams).toString()
      const url = query ? `/api/v1/permissions/roles?${query}` : '/api/v1/permissions/roles'

      return await apiCall(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    },

    // 创建角色
    create: async (roleData: {
      name: string
      display_name: string
      description?: string
      is_system?: boolean
      permission_ids?: number[]
    }) => {
      const token = getAuthToken()
      return await apiCall('/api/v1/permissions/roles', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: roleData
      })
    },

    // 更新角色
    update: async (roleId: number, roleData: {
      display_name?: string
      description?: string
      permission_ids?: number[]
    }) => {
      const token = getAuthToken()
      return await apiCall(`/api/v1/permissions/roles/${roleId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: roleData
      })
    },

    // 删除角色
    delete: async (roleId: number) => {
      const token = getAuthToken()
      return await apiCall(`/api/v1/permissions/roles/${roleId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    },

    // 批量管理角色权限
    batchPermissions: async (roleId: number, data: {
      permission_ids: number[]
      action: 'add' | 'remove' | 'replace'
    }) => {
      const token = getAuthToken()
      return await apiCall(`/api/v1/roles/${roleId}/permissions/batch`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: data
      })
    },

    // 批量管理用户角色
    batchUserRoles: async (userId: number, data: {
      role_ids: number[]
      action: 'add' | 'remove' | 'replace'
    }) => {
      const token = getAuthToken()
      return await apiCall(`/api/v1/users/${userId}/roles/batch`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: data
      })
    }
  }

  // 权限检查API
  const permissionCheck = {
    // 检查用户权限
    check: async (data: {
      user_id: number
      permission: string
      resource_id?: string
    }) => {
      const token = getAuthToken()
      return await apiCall('/api/v1/permissions/check', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: data
      })
    },

    // 获取用户权限详情
    getUserPermissions: async (userId: number) => {
      const token = getAuthToken()
      return await apiCall(`/api/v1/permissions/users/${userId}/permissions`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    },

    // 分配用户角色
    assignUserRoles: async (userId: number, data: {
      role_ids: number[]
      action: 'add' | 'remove' | 'replace'
    }) => {
      const token = getAuthToken()
      return await apiCall(`/api/v1/permissions/users/${userId}/roles`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: data
      })
    },

    // 更新用户超级管理员状态
    updateSuperuserStatus: async (userId: number, data: {
      is_superuser: boolean
    }) => {
      const token = getAuthToken()
      return await apiCall(`/api/v1/permissions/users/${userId}/superuser`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: data
      })
    }
  }

  // 设备管理API
  const devices = {
    // 获取设备列表
    list: async (params: {
      page?: number
      page_size?: number
      status?: string
    } = {}) => {
      const token = getAuthToken()
      const query = new URLSearchParams(params as any).toString()
      return await apiCall(`/api/v1/devices/?${query}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    },

    // 获取设备统计
    stats: async () => {
      const token = getAuthToken()
      return await apiCall('/api/v1/devices/stats', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    },

    // 获取设备指标
    metrics: async (deviceId: number, timeRange: string = '24h') => {
      const token = getAuthToken()
      return await apiCall(`/api/v1/devices/${deviceId}/metrics?time_range=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    }
  }

  // 应用管理API
  const applications = {
    // 获取应用列表
    list: async (params: {
      page?: number
      page_size?: number
      category?: string
    } = {}) => {
      const token = getAuthToken()
      const query = new URLSearchParams(params as any).toString()
      return await apiCall(`/api/v1/applications/?${query}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    },

    // 获取应用统计
    stats: async () => {
      const token = getAuthToken()
      return await apiCall('/api/v1/applications/stats', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    }
  }

  // 数据分析API
  const analytics = {
    // 获取系统指标
    systemMetrics: async (timeRange: string = '24h') => {
      const token = getAuthToken()
      return await apiCall(`/api/v1/analytics/system-metrics?time_range=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    },

    // 获取用户行为数据
    userBehavior: async (timeRange: string = '24h') => {
      const token = getAuthToken()
      return await apiCall(`/api/v1/analytics/user-behavior?time_range=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    },

    // 获取性能指标
    performanceMetrics: async (timeRange: string = '24h') => {
      const token = getAuthToken()
      return await apiCall(`/api/v1/analytics/performance-metrics?time_range=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    },

    // 获取业务指标
    businessMetrics: async (timeRange: string = '24h') => {
      const token = getAuthToken()
      return await apiCall(`/api/v1/analytics/business-metrics?time_range=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    }
  }

  // 电商管理API
  const ecommerce = {
    // 获取订单列表
    orders: async (params: {
      page?: number
      page_size?: number
      status?: string
    } = {}) => {
      const token = getAuthToken()
      const query = new URLSearchParams(params as any).toString()
      return await apiCall(`/api/v1/ecommerce/orders/?${query}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    },

    // 获取商品列表
    products: async (params: {
      page?: number
      page_size?: number
      category?: string
    } = {}) => {
      const token = getAuthToken()
      const query = new URLSearchParams(params as any).toString()
      return await apiCall(`/api/v1/ecommerce/products/?${query}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    },

    // 获取电商统计
    stats: async () => {
      const token = getAuthToken()
      return await apiCall('/api/v1/ecommerce/stats', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    }
  }

  // 社区管理API
  const community = {
    // 获取帖子列表
    posts: async (params: {
      page?: number
      page_size?: number
      status?: string
    } = {}) => {
      const token = getAuthToken()
      const query = new URLSearchParams(params as any).toString()
      return await apiCall(`/api/v1/community/posts/?${query}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    },

    // 获取社区统计
    stats: async () => {
      const token = getAuthToken()
      return await apiCall('/api/v1/community/stats', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    }
  }

  // 系统管理API
  const system = {
    // 获取系统配置
    config: async () => {
      const token = getAuthToken()
      return await apiCall('/api/v1/system/config', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    },

    // 获取系统日志
    logs: async (params: {
      page?: number
      page_size?: number
      level?: string
    } = {}) => {
      const token = getAuthToken()
      const query = new URLSearchParams(params as any).toString()
      return await apiCall(`/api/v1/system/logs/?${query}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
    }
  }

  return {
    apiCall,
    auth,
    users,
    permissions,
    roles,
    permissionCheck,
    devices,
    applications,
    analytics,
    ecommerce,
    community,
    system
  }
}
