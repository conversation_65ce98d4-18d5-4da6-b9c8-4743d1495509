import { ref, onMounted, onUnmounted } from 'vue'

interface PerformanceMetrics {
  fcp: number // First Contentful Paint
  lcp: number // Largest Contentful Paint
  fid: number // First Input Delay
  cls: number // Cumulative Layout Shift
  ttfb: number // Time to First Byte
}

interface ResourceTiming {
  name: string
  type: string
  size: number
  duration: number
  startTime: number
}

interface PerformanceConfig {
  enableMonitoring: boolean
  enableOptimizations: boolean
  lazyLoadThreshold: number
  debounceDelay: number
  throttleDelay: number
  cacheExpiry: number
}

const defaultConfig: PerformanceConfig = {
  enableMonitoring: true,
  enableOptimizations: true,
  lazyLoadThreshold: 100,
  debounceDelay: 300,
  throttleDelay: 16,
  cacheExpiry: 5 * 60 * 1000 // 5分钟
}

// 全局性能状态
const performanceMetrics = ref<PerformanceMetrics>({
  fcp: 0,
  lcp: 0,
  fid: 0,
  cls: 0,
  ttfb: 0
})

const resourceTimings = ref<ResourceTiming[]>([])
const isPerformanceSupported = ref(false)
const config = ref<PerformanceConfig>({ ...defaultConfig })

// 性能监控
export function usePerformance(customConfig?: Partial<PerformanceConfig>) {
  // 合并配置
  if (customConfig) {
    config.value = { ...config.value, ...customConfig }
  }

  // 检查浏览器支持
  const checkSupport = () => {
    isPerformanceSupported.value = 
      typeof window !== 'undefined' &&
      'performance' in window &&
      'PerformanceObserver' in window
  }

  // 收集性能指标
  const collectMetrics = () => {
    if (!isPerformanceSupported.value || !config.value.enableMonitoring) return

    try {
      // Navigation Timing
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigation) {
        performanceMetrics.value.ttfb = navigation.responseStart - navigation.requestStart
      }

      // Paint Timing
      const paintEntries = performance.getEntriesByType('paint')
      paintEntries.forEach((entry) => {
        if (entry.name === 'first-contentful-paint') {
          performanceMetrics.value.fcp = entry.startTime
        }
      })

      // LCP Observer
      if ('PerformanceObserver' in window) {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1]
          performanceMetrics.value.lcp = lastEntry.startTime
        })
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })

        // FID Observer
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry: any) => {
            performanceMetrics.value.fid = entry.processingStart - entry.startTime
          })
        })
        fidObserver.observe({ entryTypes: ['first-input'] })

        // CLS Observer
        let clsValue = 0
        const clsObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value
            }
          })
          performanceMetrics.value.cls = clsValue
        })
        clsObserver.observe({ entryTypes: ['layout-shift'] })
      }
    } catch (error) {
      console.warn('Performance metrics collection failed:', error)
    }
  }

  // 收集资源时序
  const collectResourceTimings = () => {
    if (!isPerformanceSupported.value) return

    try {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
      resourceTimings.value = resources.map((resource) => ({
        name: resource.name,
        type: resource.initiatorType,
        size: resource.transferSize || 0,
        duration: resource.duration,
        startTime: resource.startTime
      }))
    } catch (error) {
      console.warn('Resource timings collection failed:', error)
    }
  }

  // 性能优化建议
  const getOptimizationSuggestions = () => {
    const suggestions: string[] = []

    if (performanceMetrics.value.fcp > 2500) {
      suggestions.push('首次内容绘制时间过长，考虑优化关键渲染路径')
    }

    if (performanceMetrics.value.lcp > 4000) {
      suggestions.push('最大内容绘制时间过长，考虑优化图片和字体加载')
    }

    if (performanceMetrics.value.fid > 300) {
      suggestions.push('首次输入延迟过长，考虑减少主线程阻塞')
    }

    if (performanceMetrics.value.cls > 0.25) {
      suggestions.push('累积布局偏移过大，考虑为图片和广告设置尺寸')
    }

    if (performanceMetrics.value.ttfb > 800) {
      suggestions.push('首字节时间过长，考虑优化服务器响应时间')
    }

    return suggestions
  }

  return {
    performanceMetrics: readonly(performanceMetrics),
    resourceTimings: readonly(resourceTimings),
    isPerformanceSupported: readonly(isPerformanceSupported),
    config: readonly(config),
    checkSupport,
    collectMetrics,
    collectResourceTimings,
    getOptimizationSuggestions
  }
}

// 防抖函数
export function useDebounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number = config.value.debounceDelay
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null

  return (...args: Parameters<T>) => {
    if (timeoutId) clearTimeout(timeoutId)
    timeoutId = setTimeout(() => fn(...args), delay)
  }
}

// 节流函数
export function useThrottle<T extends (...args: any[]) => any>(
  fn: T,
  delay: number = config.value.throttleDelay
): (...args: Parameters<T>) => void {
  let lastCall = 0

  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      fn(...args)
    }
  }
}

// 懒加载
export function useLazyLoad(threshold: number = config.value.lazyLoadThreshold) {
  const isIntersecting = ref(false)
  let observer: IntersectionObserver | null = null

  const observe = (element: Element) => {
    if (!element || typeof window === 'undefined') return

    observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          isIntersecting.value = entry.isIntersecting
        })
      },
      {
        rootMargin: `${threshold}px`,
        threshold: 0.1
      }
    )

    observer.observe(element)
  }

  const unobserve = () => {
    observer?.disconnect()
  }

  onUnmounted(() => {
    unobserve()
  })

  return {
    isIntersecting: readonly(isIntersecting),
    observe,
    unobserve
  }
}

// 内存缓存
const cache = new Map<string, { data: any; timestamp: number }>()

export function useCache() {
  const set = (key: string, data: any, expiry: number = config.value.cacheExpiry) => {
    cache.set(key, {
      data,
      timestamp: Date.now() + expiry
    })
  }

  const get = (key: string) => {
    const item = cache.get(key)
    if (!item) return null

    if (Date.now() > item.timestamp) {
      cache.delete(key)
      return null
    }

    return item.data
  }

  const remove = (key: string) => {
    cache.delete(key)
  }

  const clear = () => {
    cache.clear()
  }

  const size = () => cache.size

  return {
    set,
    get,
    remove,
    clear,
    size
  }
}

// 图片优化
export function useImageOptimization() {
  const loadImage = (src: string): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => resolve(img)
      img.onerror = reject
      img.src = src
    })
  }

  const preloadImages = async (urls: string[]) => {
    try {
      await Promise.all(urls.map(loadImage))
    } catch (error) {
      console.warn('Image preloading failed:', error)
    }
  }

  const getOptimizedImageUrl = (
    src: string,
    width?: number,
    height?: number,
    quality: number = 80
  ) => {
    // 这里可以集成图片CDN服务，如Cloudinary、ImageKit等
    const params = new URLSearchParams()
    if (width) params.set('w', width.toString())
    if (height) params.set('h', height.toString())
    params.set('q', quality.toString())
    params.set('f', 'auto')

    return `${src}?${params.toString()}`
  }

  return {
    loadImage,
    preloadImages,
    getOptimizedImageUrl
  }
}

// 网络状态监控
export function useNetworkStatus() {
  const isOnline = ref(navigator.onLine)
  const connectionType = ref<string>('unknown')
  const effectiveType = ref<string>('4g')

  const updateNetworkStatus = () => {
    isOnline.value = navigator.onLine

    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      connectionType.value = connection.type || 'unknown'
      effectiveType.value = connection.effectiveType || '4g'
    }
  }

  onMounted(() => {
    updateNetworkStatus()
    window.addEventListener('online', updateNetworkStatus)
    window.addEventListener('offline', updateNetworkStatus)
  })

  onUnmounted(() => {
    window.removeEventListener('online', updateNetworkStatus)
    window.removeEventListener('offline', updateNetworkStatus)
  })

  return {
    isOnline: readonly(isOnline),
    connectionType: readonly(connectionType),
    effectiveType: readonly(effectiveType)
  }
}

// 内存监控
export function useMemoryMonitoring() {
  const memoryInfo = ref<any>(null)

  const updateMemoryInfo = () => {
    if ('memory' in performance) {
      memoryInfo.value = (performance as any).memory
    }
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  onMounted(() => {
    updateMemoryInfo()
    setInterval(updateMemoryInfo, 5000) // 每5秒更新一次
  })

  return {
    memoryInfo: readonly(memoryInfo),
    formatBytes,
    updateMemoryInfo
  }
}

// 性能报告
export function usePerformanceReport() {
  const generateReport = () => {
    const report = {
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      metrics: performanceMetrics.value,
      resources: resourceTimings.value,
      suggestions: usePerformance().getOptimizationSuggestions(),
      memory: 'memory' in performance ? (performance as any).memory : null,
      connection: 'connection' in navigator ? (navigator as any).connection : null
    }

    return report
  }

  const exportReport = (format: 'json' | 'csv' = 'json') => {
    const report = generateReport()

    if (format === 'json') {
      const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `performance-report-${Date.now()}.json`
      a.click()
      URL.revokeObjectURL(url)
    }
  }

  return {
    generateReport,
    exportReport
  }
}
