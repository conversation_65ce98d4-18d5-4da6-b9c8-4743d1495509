# 🌌 AR-System Dashboard API设计标准规范

## 📋 **目录**
- [接口设计原则](#接口设计原则)
- [RESTful API规范](#restful-api规范)
- [数据结构标准](#数据结构标准)
- [错误处理规范](#错误处理规范)
- [认证授权标准](#认证授权标准)
- [分页查询规范](#分页查询规范)
- [实时数据规范](#实时数据规范)
- [文件上传规范](#文件上传规范)

## 🎯 **接口设计原则**

### **1. 一致性原则**
- 统一的命名规范（camelCase）
- 统一的响应格式
- 统一的错误码体系
- 统一的时间格式（ISO 8601）

### **2. 可预测性原则**
- 清晰的资源路径结构
- 标准的HTTP状态码使用
- 明确的参数验证规则
- 详细的接口文档

### **3. 扩展性原则**
- 版本化API设计
- 向后兼容性保证
- 灵活的查询参数
- 可选的响应字段

## 🔗 **RESTful API规范**

### **基础URL结构**
```
https://api.ar-system.dev/v1/{resource}
```

### **HTTP方法使用规范**
| 方法 | 用途 | 示例 |
|------|------|------|
| GET | 获取资源 | `GET /api/v1/devices` |
| POST | 创建资源 | `POST /api/v1/devices` |
| PUT | 完整更新资源 | `PUT /api/v1/devices/123` |
| PATCH | 部分更新资源 | `PATCH /api/v1/devices/123` |
| DELETE | 删除资源 | `DELETE /api/v1/devices/123` |

### **资源路径规范**
```typescript
// ✅ 正确的资源路径
GET /api/v1/devices                    // 获取设备列表
GET /api/v1/devices/123                // 获取特定设备
GET /api/v1/devices/123/metrics        // 获取设备指标
POST /api/v1/devices/123/actions/restart // 设备操作

// ❌ 错误的资源路径
GET /api/v1/getDevices
GET /api/v1/device_list
POST /api/v1/restartDevice/123
```

## 📊 **数据结构标准**

### **统一响应格式**
```typescript
interface ApiResponse<T = any> {
  success: boolean
  code: number
  message: string
  data?: T
  meta?: {
    timestamp: string
    requestId: string
    version: string
  }
  pagination?: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}
```

### **成功响应示例**
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "device_001",
    "name": "AR-Vision Pro #001",
    "status": "online",
    "createdAt": "2024-06-14T10:30:00.000Z",
    "updatedAt": "2024-06-14T14:30:00.000Z"
  },
  "meta": {
    "timestamp": "2024-06-14T14:30:00.000Z",
    "requestId": "req_abc123",
    "version": "v1"
  }
}
```

### **错误响应示例**
```json
{
  "success": false,
  "code": 400,
  "message": "请求参数错误",
  "errors": [
    {
      "field": "name",
      "code": "REQUIRED",
      "message": "设备名称不能为空"
    }
  ],
  "meta": {
    "timestamp": "2024-06-14T14:30:00.000Z",
    "requestId": "req_abc123",
    "version": "v1"
  }
}
```

## ⚠️ **错误处理规范**

### **HTTP状态码使用**
| 状态码 | 含义 | 使用场景 |
|--------|------|----------|
| 200 | 成功 | 请求成功处理 |
| 201 | 已创建 | 资源创建成功 |
| 204 | 无内容 | 删除成功 |
| 400 | 请求错误 | 参数验证失败 |
| 401 | 未授权 | 认证失败 |
| 403 | 禁止访问 | 权限不足 |
| 404 | 未找到 | 资源不存在 |
| 409 | 冲突 | 资源冲突 |
| 422 | 无法处理 | 业务逻辑错误 |
| 500 | 服务器错误 | 内部错误 |

### **错误码体系**
```typescript
enum ErrorCode {
  // 通用错误 (1000-1999)
  INVALID_REQUEST = 1001,
  MISSING_PARAMETER = 1002,
  INVALID_PARAMETER = 1003,
  
  // 认证错误 (2000-2999)
  UNAUTHORIZED = 2001,
  TOKEN_EXPIRED = 2002,
  INVALID_TOKEN = 2003,
  
  // 权限错误 (3000-3999)
  FORBIDDEN = 3001,
  INSUFFICIENT_PERMISSIONS = 3002,
  
  // 资源错误 (4000-4999)
  RESOURCE_NOT_FOUND = 4001,
  RESOURCE_CONFLICT = 4002,
  RESOURCE_LOCKED = 4003,
  
  // 业务错误 (5000-5999)
  DEVICE_OFFLINE = 5001,
  DEVICE_BUSY = 5002,
  INSUFFICIENT_STORAGE = 5003,
  
  // 系统错误 (9000-9999)
  INTERNAL_ERROR = 9001,
  SERVICE_UNAVAILABLE = 9002,
  DATABASE_ERROR = 9003
}
```

## 🔐 **认证授权标准**

### **JWT Token格式**
```typescript
interface JWTPayload {
  sub: string        // 用户ID
  iat: number        // 签发时间
  exp: number        // 过期时间
  iss: string        // 签发者
  aud: string        // 受众
  roles: string[]    // 用户角色
  permissions: string[] // 用户权限
}
```

### **请求头格式**
```http
Authorization: Bearer <jwt_token>
X-API-Key: <api_key>
X-Request-ID: <unique_request_id>
Content-Type: application/json
```

### **权限验证**
```typescript
interface Permission {
  resource: string   // 资源类型
  action: string     // 操作类型
  scope?: string     // 权限范围
}

// 示例权限
const permissions = [
  { resource: 'device', action: 'read', scope: 'own' },
  { resource: 'device', action: 'write', scope: 'team' },
  { resource: 'user', action: 'admin', scope: 'global' }
]
```

## 📄 **分页查询规范**

### **查询参数**
```typescript
interface PaginationParams {
  page?: number      // 页码，从1开始
  pageSize?: number  // 每页数量，默认20，最大100
  sort?: string      // 排序字段
  order?: 'asc' | 'desc' // 排序方向
  search?: string    // 搜索关键词
  filter?: Record<string, any> // 筛选条件
}
```

### **查询示例**
```http
GET /api/v1/devices?page=1&pageSize=20&sort=createdAt&order=desc&search=AR&filter[status]=online
```

### **响应格式**
```json
{
  "success": true,
  "code": 200,
  "message": "查询成功",
  "data": [...],
  "pagination": {
    "page": 1,
    "pageSize": 20,
    "total": 156,
    "totalPages": 8,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## ⚡ **实时数据规范**

### **WebSocket连接**
```typescript
// 连接URL
ws://api.ar-system.dev/v1/ws?token=<jwt_token>

// 消息格式
interface WSMessage {
  type: string       // 消息类型
  event: string      // 事件名称
  data: any         // 数据内容
  timestamp: string  // 时间戳
  requestId?: string // 请求ID
}
```

### **事件类型**
```typescript
enum WSEventType {
  // 设备事件
  DEVICE_STATUS_CHANGED = 'device.status.changed',
  DEVICE_METRICS_UPDATED = 'device.metrics.updated',
  
  // 用户事件
  USER_ONLINE = 'user.online',
  USER_OFFLINE = 'user.offline',
  
  // 系统事件
  SYSTEM_ALERT = 'system.alert',
  SYSTEM_MAINTENANCE = 'system.maintenance'
}
```

### **Server-Sent Events (SSE)**
```http
GET /api/v1/events/stream
Accept: text/event-stream
Authorization: Bearer <jwt_token>

# 响应格式
data: {"type":"device.status","data":{"id":"device_001","status":"online"}}
event: device-status-changed
id: event_123
retry: 3000
```

## 📁 **文件上传规范**

### **单文件上传**
```http
POST /api/v1/files/upload
Content-Type: multipart/form-data

{
  "file": <binary_data>,
  "category": "avatar",
  "metadata": {
    "description": "用户头像",
    "tags": ["avatar", "user"]
  }
}
```

### **文件信息响应**
```json
{
  "success": true,
  "code": 201,
  "message": "文件上传成功",
  "data": {
    "id": "file_abc123",
    "filename": "avatar.jpg",
    "originalName": "user_avatar.jpg",
    "mimeType": "image/jpeg",
    "size": 102400,
    "url": "https://cdn.ar-system.dev/files/abc123.jpg",
    "thumbnails": {
      "small": "https://cdn.ar-system.dev/files/abc123_small.jpg",
      "medium": "https://cdn.ar-system.dev/files/abc123_medium.jpg"
    },
    "metadata": {
      "width": 1024,
      "height": 1024,
      "format": "JPEG"
    },
    "createdAt": "2024-06-14T14:30:00.000Z"
  }
}
```

## 🔍 **API文档规范**

### **OpenAPI 3.0规范**
```yaml
openapi: 3.0.0
info:
  title: AR-System Dashboard API
  version: 1.0.0
  description: AR系统管理控制台API接口文档
  
servers:
  - url: https://api.ar-system.dev/v1
    description: 生产环境
  - url: https://test-api.ar-system.dev/v1
    description: 测试环境

paths:
  /devices:
    get:
      summary: 获取设备列表
      tags: [设备管理]
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
      responses:
        '200':
          description: 成功获取设备列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceListResponse'
```

## 🧪 **测试规范**

### **接口测试用例**
```typescript
describe('Device API', () => {
  test('GET /devices - 获取设备列表', async () => {
    const response = await request(app)
      .get('/api/v1/devices')
      .set('Authorization', `Bearer ${token}`)
      .expect(200)
    
    expect(response.body.success).toBe(true)
    expect(response.body.data).toBeInstanceOf(Array)
    expect(response.body.pagination).toBeDefined()
  })
  
  test('POST /devices - 创建设备', async () => {
    const deviceData = {
      name: 'Test Device',
      type: 'ar-glasses',
      model: 'AR-Vision Pro'
    }
    
    const response = await request(app)
      .post('/api/v1/devices')
      .set('Authorization', `Bearer ${token}`)
      .send(deviceData)
      .expect(201)
    
    expect(response.body.success).toBe(true)
    expect(response.body.data.id).toBeDefined()
  })
})
```

## 📈 **性能规范**

### **响应时间要求**
- 简单查询：< 100ms
- 复杂查询：< 500ms
- 文件上传：< 5s
- 批量操作：< 10s

### **并发处理**
- 支持1000+并发连接
- 单接口QPS > 500
- WebSocket连接 > 10000

### **缓存策略**
```typescript
// Redis缓存键命名规范
const cacheKeys = {
  user: (id: string) => `user:${id}`,
  device: (id: string) => `device:${id}`,
  deviceList: (params: string) => `devices:list:${params}`,
  session: (token: string) => `session:${token}`
}

// 缓存过期时间
const cacheTTL = {
  user: 3600,        // 1小时
  device: 1800,      // 30分钟
  deviceList: 300,   // 5分钟
  session: 86400     // 24小时
}
```

## 🔄 **版本管理**

### **API版本策略**
- URL版本：`/api/v1/`, `/api/v2/`
- 向后兼容：至少支持2个版本
- 废弃通知：提前3个月通知
- 文档维护：每个版本独立文档

### **版本升级流程**
1. 新版本开发和测试
2. 文档更新和发布
3. 客户端适配期（3个月）
4. 旧版本标记废弃
5. 旧版本下线

---

**📝 本文档将持续更新，确保API设计的一致性和可维护性。所有开发者都应遵循此规范进行接口设计和实现。**
