<template>
  <div class="dashboard-layout" :class="{
    'sidebar-collapsed': sidebarCollapsed && !isMobile,
    'sidebar-visible': sidebarVisible && isMobile
  }">
    <!-- 🎛️ 侧边栏 -->
    <aside class="dashboard-sidebar" style="grid-area: sidebar;">
      <DashboardSidebar 
        :collapsed="sidebarCollapsed"
        @toggle="toggleSidebar"
      />
    </aside>

    <!-- 📊 顶部导航 -->
    <header class="dashboard-header" style="grid-area: header;">
      <DashboardHeader 
        :sidebar-collapsed="sidebarCollapsed"
        @toggle-sidebar="toggleSidebar"
      />
    </header>

    <!-- 📋 主要内容区域 -->
    <main class="dashboard-main" style="grid-area: main;">
      <div class="dashboard-content">
        <!-- 页面内容 -->
        <slot />
      </div>
    </main>

    <!-- 🌌 量子背景效果 -->
    <div class="quantum-bg-overlay">
      <div class="quantum-particles"></div>
      <div class="quantum-waves"></div>
    </div>

    <!-- 📱 移动端导航 -->
    <MobileNavigation v-if="isMobile" />

    <!-- 🔔 通知系统 -->
    <DashboardNotifications />

    <!-- 🎭 模态框容器 -->
    <DashboardModalContainer />

    <!-- 🔧 性能监控 -->
    <PerformanceMonitor v-if="showPerformanceMonitor" :enabled="true" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 侧边栏状态
const sidebarCollapsed = ref(false)  // 桌面端：收起/展开，移动端：隐藏/显示
const isMobile = ref(false)

// 性能监控状态
const showPerformanceMonitor = ref(process.env.NODE_ENV === 'development')

// 计算属性：侧边栏是否显示
const sidebarVisible = computed(() => {
  if (isMobile.value) {
    return !sidebarCollapsed.value  // 移动端：false=隐藏，true=显示
  } else {
    return true  // 桌面端：始终显示，只是收起/展开
  }
})

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value

  // 只在桌面端保存状态到本地存储
  if (process.client && !isMobile.value) {
    localStorage.setItem('dashboard-sidebar-collapsed', String(sidebarCollapsed.value))
  }
}

// 响应式处理
const handleResize = () => {
  if (process.client) {
    const wasMobile = isMobile.value
    isMobile.value = window.innerWidth < 1024

    // 从桌面端切换到移动端时，收起侧边栏
    if (!wasMobile && isMobile.value) {
      sidebarCollapsed.value = true
    }
    // 从移动端切换到桌面端时，恢复之前的状态
    else if (wasMobile && !isMobile.value) {
      const saved = localStorage.getItem('dashboard-sidebar-collapsed')
      if (saved !== null) {
        sidebarCollapsed.value = saved === 'true'
      }
    }
  }
}

// 键盘快捷键
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl/Cmd + B 切换侧边栏
  if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
    event.preventDefault()
    toggleSidebar()
  }
  
  // Escape 关闭侧边栏（移动端）
  if (event.key === 'Escape' && process.client && window.innerWidth < 1024) {
    if (!sidebarCollapsed.value) {
      sidebarCollapsed.value = true
    }
  }
}

// 初始化
onMounted(() => {
  if (process.client) {
    // 初始化移动端状态
    isMobile.value = window.innerWidth < 1024

    // 恢复侧边栏状态
    if (isMobile.value) {
      sidebarCollapsed.value = true
    } else {
      const saved = localStorage.getItem('dashboard-sidebar-collapsed')
      if (saved !== null) {
        sidebarCollapsed.value = saved === 'true'
      }
    }

    // 响应式处理
    handleResize()
    window.addEventListener('resize', handleResize)

    // 键盘快捷键
    document.addEventListener('keydown', handleKeydown)
  }
})

// 清理
onUnmounted(() => {
  if (process.client) {
    window.removeEventListener('resize', handleResize)
    document.removeEventListener('keydown', handleKeydown)
  }
})

// 页面元数据
useHead({
  bodyAttrs: {
    class: 'dashboard-body'
  }
})
</script>

<style scoped>
/* 🎛️ Dashboard 布局 */
.dashboard-layout {
  min-height: 100vh;
  background: var(--quantum-bg-primary);
  display: grid;
  grid-template-areas:
    "sidebar header"
    "sidebar main";
  grid-template-columns: var(--sidebar-width) 1fr;
  grid-template-rows: var(--header-height) 1fr;
  transition: grid-template-columns var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.dashboard-layout.sidebar-collapsed {
  grid-template-columns: var(--sidebar-collapsed-width) 1fr;
}

/* 📊 侧边栏 */
.dashboard-sidebar {
  background: var(--quantum-bg-surface);
  border-right: 1px solid var(--quantum-border-color);
  z-index: 100;
  position: relative;
}

/* 📋 顶部导航 */
.dashboard-header {
  z-index: 90;
  position: relative;
  /* 背景和边框由DashboardHeader组件内部控制，避免双层样式 */
}

/* 📄 主要内容 */
.dashboard-main {
  background: var(--quantum-bg-primary);
  overflow: auto;
  position: relative;
}

.dashboard-content {
  padding: var(--space-6);
  max-width: 100%;
  margin: 0 auto;
}

/* 🌌 量子背景效果 */
.quantum-bg-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
  opacity: 0.05;
}

[data-theme="dark"] .quantum-bg-overlay {
  opacity: 0.1;
}

.quantum-particles {
  position: absolute;
  inset: 0;
  background-image:
    radial-gradient(2px 2px at 20px 30px, var(--quantum-primary), transparent),
    radial-gradient(2px 2px at 40px 70px, var(--quantum-bg-elevated), transparent),
    radial-gradient(1px 1px at 90px 40px, var(--quantum-bg-surface), transparent);
  background-repeat: repeat;
  background-size: 200px 200px;
  animation: quantumParticlesFloat 20s ease-in-out infinite;
}

.quantum-waves {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(0, 212, 255, 0.02) 50%,
    transparent 70%
  );
  animation: quantumWavesMove 15s ease-in-out infinite;
}

[data-theme="dark"] .quantum-waves {
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(0, 212, 255, 0.05) 50%,
    transparent 70%
  );
}

/* 📱 响应式布局 - 统一的断点系统 */

/* 桌面端 (1024px+) - 正常grid布局 */
@media (min-width: 1024px) {
  .dashboard-layout {
    grid-template-areas:
      "sidebar header"
      "sidebar main";
    grid-template-columns: var(--sidebar-width) 1fr;
    grid-template-rows: var(--header-height) 1fr;
  }

  .dashboard-header {
    display: block;
  }
}

/* 移动端和平板 (1023px及以下) - 使用MobileNavigation */
@media (max-width: 1023px) {
  .dashboard-header {
    display: none; /* 隐藏主Header，使用MobileNavigation */
  }

  .dashboard-layout {
    grid-template-areas: "main";
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
    padding-top: 4rem; /* 为MobileNavigation的fixed header留出空间 */
  }

  .dashboard-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    width: var(--sidebar-width);
    transform: translateX(-100%);
    transition: transform var(--transition-normal);
    z-index: 1000;
    background: var(--quantum-bg-surface);
    border-right: 1px solid var(--quantum-border-color);
    box-shadow: var(--quantum-shadow-elevated);
  }

  .dashboard-layout.sidebar-visible .dashboard-sidebar {
    transform: translateX(0);
  }

  /* 移动端遮罩 */
  .dashboard-layout.sidebar-visible::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    z-index: 999;
    animation: fadeIn 0.3s ease;
    backdrop-filter: blur(4px);
    cursor: pointer;
  }
}

/* 小屏平板 (768px及以下) */
@media (max-width: 768px) {
  .dashboard-content {
    padding: 1rem;
  }

  .dashboard-sidebar {
    width: 100vw;
  }
}

/* 手机端 (640px及以下) */
@media (max-width: 640px) {
  .dashboard-content {
    padding: 0.75rem;
  }

  .dashboard-layout {
    padding-top: 3.5rem; /* 移动端header高度更小 */
  }
}

/* 🎬 动画关键帧 */
@keyframes quantumParticlesFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes quantumWavesMove {
  0%, 100% {
    transform: translateX(-100%) skewX(-10deg);
  }
  50% {
    transform: translateX(100%) skewX(10deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 🎭 性能优化 */
@media (prefers-reduced-motion: reduce) {
  .quantum-particles,
  .quantum-waves {
    animation: none !important;
  }
  
  .dashboard-layout,
  .dashboard-sidebar {
    transition: none !important;
  }
}
</style>

<style>
/* 🌐 全局样式 */
.dashboard-body {
  overflow-x: hidden;
}

/* 滚动条样式 */
.dashboard-main::-webkit-scrollbar {
  width: 8px;
}

.dashboard-main::-webkit-scrollbar-track {
  background: var(--quantum-bg-elevated);
}

.dashboard-main::-webkit-scrollbar-thumb {
  background: var(--quantum-border-color);
  border-radius: 4px;
}

.dashboard-main::-webkit-scrollbar-thumb:hover {
  background: var(--quantum-primary);
}
</style>
