<template>
  <div 
    class="dashboard-layout"
    :class="{ 'sidebar-collapsed': sidebarCollapsed }"
  >
    <!-- 侧边栏 -->
    <aside class="dashboard-sidebar">
      <div class="sidebar-content">
        <!-- Logo -->
        <div class="sidebar-logo">
          <h2>AR-System</h2>
        </div>
        
        <!-- 导航菜单 -->
        <nav class="sidebar-nav">
          <NuxtLink to="/" class="nav-item">
            <i class="i-carbon-dashboard"></i>
            <span v-if="!sidebarCollapsed">Dashboard</span>
          </NuxtLink>
          <NuxtLink to="/analytics" class="nav-item">
            <i class="i-carbon-analytics"></i>
            <span v-if="!sidebarCollapsed">Analytics</span>
          </NuxtLink>
          <NuxtLink to="/users" class="nav-item">
            <i class="i-carbon-user-multiple"></i>
            <span v-if="!sidebarCollapsed">Users</span>
          </NuxtLink>
          <NuxtLink to="/settings" class="nav-item">
            <i class="i-carbon-settings"></i>
            <span v-if="!sidebarCollapsed">Settings</span>
          </NuxtLink>
        </nav>
      </div>
    </aside>

    <!-- 顶部导航 -->
    <header class="dashboard-header">
      <div class="header-left">
        <button @click="toggleSidebar" class="btn btn-secondary">
          <i class="i-carbon-menu"></i>
        </button>
      </div>
      
      <div class="header-right">
        <button @click="toggleTheme" class="btn btn-secondary">
          <i :class="themeIcon"></i>
        </button>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="dashboard-main">
      <slot />
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useGlobalTheme } from '~/composables/useTheme'

// 侧边栏状态
const sidebarCollapsed = ref(false)

// 主题管理
const { toggleTheme, themeIcon, initTheme } = useGlobalTheme()

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
  localStorage.setItem('sidebar-collapsed', String(sidebarCollapsed.value))
}

// 响应式处理
const handleResize = () => {
  if (process.client) {
    const isMobile = window.innerWidth < 1024
    if (isMobile) {
      sidebarCollapsed.value = true
    }
  }
}

// 初始化
onMounted(() => {
  // 初始化主题
  initTheme()
  
  // 恢复侧边栏状态
  const saved = localStorage.getItem('sidebar-collapsed')
  if (saved !== null) {
    sidebarCollapsed.value = saved === 'true'
  }
  
  // 响应式处理
  handleResize()
  window.addEventListener('resize', handleResize)
})
</script>

<style scoped>
/* 侧边栏内容 */
.sidebar-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: var(--space-4);
}

.sidebar-logo {
  padding: var(--space-4) 0;
  border-bottom: 1px solid var(--quantum-border-color);
  margin-bottom: var(--space-4);
}

.sidebar-logo h2 {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--quantum-primary);
}

.sidebar-nav {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  border-radius: 0.5rem;
  color: var(--quantum-fg-secondary);
  text-decoration: none;
  transition: all var(--transition-fast);
  font-weight: 500;
}

.nav-item:hover {
  background: var(--quantum-bg-elevated);
  color: var(--quantum-fg-primary);
}

.nav-item.router-link-active {
  background: var(--quantum-primary);
  color: white;
}

.nav-item i {
  font-size: 1.25rem;
  flex-shrink: 0;
}

/* 头部内容 */
.header-left,
.header-right {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.header-right {
  margin-left: auto;
}

/* 响应式侧边栏收起状态 */
.sidebar-collapsed .sidebar-logo h2 {
  display: none;
}

.sidebar-collapsed .nav-item span {
  display: none;
}

.sidebar-collapsed .nav-item {
  justify-content: center;
  padding: var(--space-3);
}
</style>
