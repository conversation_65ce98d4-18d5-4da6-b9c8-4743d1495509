/**
 * Dashboard 认证中间件
 * 保护需要登录的页面
 */

export default defineNuxtRouteMiddleware((to, from) => {
  const authStore = useAuthStore()
  
  // 初始化认证状态
  authStore.initAuth()
  
  // 检查是否已登录
  if (!authStore.isLoggedIn) {
    console.log('用户未登录，重定向到登录页面')
    return navigateTo('/login')
  }
  
  // 检查是否有管理员权限
  const currentUser = authStore.currentUser
  if (currentUser && !currentUser.is_superuser) {
    console.log('用户没有管理员权限')
    return navigateTo('/login')
  }
  
  console.log('认证通过，允许访问')
})
