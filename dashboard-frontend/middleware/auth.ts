/**
 * Dashboard 认证中间件
 * 保护需要登录的页面
 */

export default defineNuxtRouteMiddleware((to, from) => {
  // 检查页面是否需要认证
  if (to.meta.auth === false) {
    console.log('页面不需要认证，跳过权限检查')
    return
  }

  const authStore = useAuthStore()

  // 初始化认证状态
  authStore.initAuth()

  // 检查是否已登录
  if (!authStore.isLoggedIn) {
    console.log('Dashboard访问被拒绝：用户未登录')
    return navigateTo('/login')
  }

  // 严格检查管理员权限
  const currentUser = authStore.currentUser
  if (!currentUser || !currentUser.is_superuser) {
    console.log('Dashboard访问被拒绝：用户不是超级管理员')
    console.log('当前用户:', currentUser)

    // 清除认证状态并重定向
    authStore.logout()

    // 显示错误信息
    throw createError({
      statusCode: 403,
      statusMessage: '访问被拒绝：只有超级管理员才能访问Dashboard'
    })
  }

  console.log('Dashboard认证通过，允许访问')
})
