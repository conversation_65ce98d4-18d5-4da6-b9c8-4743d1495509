/* 🌌 AR-System Dashboard - 量子CSS变量系统 */
/* 基于双鱼星系·量子星核设计理念 - 严格按照文档规范 */

:root {
  /* 🎨 量子主色系 */
  --quantum-primary: #00d4ff;
  --quantum-secondary: #ff6b9d;
  --quantum-accent: #00ff88;
  --quantum-warning: #ffaa00;
  --quantum-error: #ff4757;
  --quantum-success: #2ed573;

  /* 🌈 量子背景色系 */
  --quantum-bg-primary: #ffffff;
  --quantum-bg-secondary: #f8fafc;
  --quantum-bg-elevated: #f1f5f9;
  --quantum-bg-surface: rgba(255, 255, 255, 0.8);
  --quantum-bg-glass: rgba(255, 255, 255, 0.1);
  --quantum-bg-muted: #e2e8f0;
  --quantum-bg-hover: #e2e8f0;

  /* 🔤 量子前景文字色系 */
  --quantum-fg-primary: #0f172a;
  --quantum-fg-secondary: #334155;
  --quantum-fg-muted: #64748b;

  /* 🔲 量子边框色系 */
  --quantum-border-color: rgba(0, 212, 255, 0.2);
  --quantum-border-glow: rgba(0, 212, 255, 0.4);

  /* ✨ 量子发光效果 */
  --quantum-glow-primary: 0 0 20px rgba(0, 212, 255, 0.3);
  --quantum-glow-secondary: 0 0 20px rgba(255, 107, 157, 0.3);
  --quantum-glow-accent: 0 0 20px rgba(0, 255, 136, 0.3);

  /* � Cyber发光效果别名 */
  --glow-cyber-primary: var(--quantum-glow-primary);
  --glow-cyber-secondary: var(--quantum-glow-secondary);
  --glow-cyber-accent: var(--quantum-glow-accent);

  /* �🌫️ 量子阴影效果 */
  --quantum-shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.1);
  --quantum-shadow-elevated: 0 4px 16px rgba(0, 0, 0, 0.1);
  --quantum-shadow-normal: 0 2px 8px rgba(0, 0, 0, 0.1);

  /* 🔤 字体系统 - 简化命名 */
  --font-sans: 'Inter', 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', 'SF Mono', Monaco, monospace;

  /* 📏 文字尺寸 - 简化命名 */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;

  /* 🎯 字体权重 */
  --font-thin: 100;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;

  /* 📐 间距系统 - 简化命名 */
  --space-0: 0;
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-7: 1.75rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;

  /* ⚡ 动画时长 - 简化命名 */
  --transition-instant: 50ms;
  --transition-fast: 150ms;
  --transition-normal: 300ms;
  --transition-slow: 500ms;
  --transition-epic: 1000ms;

  /* 🌊 缓动函数 */
  --ease-default: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);

  /* 🎯 布局变量 - 简化命名 */
  --header-height: 4rem;
  --sidebar-width: 16rem;
  --sidebar-collapsed-width: 4rem;

  /* 🎭 Dashboard特定变量 */
  --dashboard-transition-fast: var(--transition-fast);
  --dashboard-transition-normal: var(--transition-normal);
  --dashboard-ease-default: cubic-bezier(0.4, 0, 0.2, 1);
  --dashboard-z-dropdown: 1000;
  --dashboard-z-modal: 1050;
  --dashboard-z-toast: 1080;

  /* 🌌 量子背景别名 */
  --quantum-matrix-bg: linear-gradient(135deg, var(--quantum-bg-primary) 0%, var(--quantum-bg-secondary) 100%);
}

/* 🌙 量子深色主题 - 匹配useTheme.ts */
[data-theme="dark"] {
  --quantum-bg-primary: #0a0a0f;
  --quantum-bg-secondary: #0f0f1a;
  --quantum-bg-elevated: #1a1a2e;
  --quantum-bg-surface: rgba(26, 26, 46, 0.8);
  --quantum-bg-glass: rgba(0, 212, 255, 0.05);
  --quantum-bg-muted: #2a2a3e;
  --quantum-bg-hover: #2a2a3e;
  --quantum-fg-primary: #ffffff;
  --quantum-fg-secondary: #b3b3cc;
  --quantum-fg-muted: #7a7a99;
  --quantum-border-color: rgba(0, 212, 255, 0.3);
  --quantum-border-glow: rgba(0, 212, 255, 0.6);

  /* 深色模式发光增强 */
  --quantum-glow-primary: 0 0 30px rgba(0, 212, 255, 0.5);
  --quantum-glow-secondary: 0 0 30px rgba(255, 107, 157, 0.5);
  --quantum-glow-accent: 0 0 30px rgba(0, 255, 136, 0.5);
  --quantum-shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.3);
  --quantum-shadow-elevated: 0 4px 16px rgba(0, 0, 0, 0.2);
}

/* ☀️ 量子浅色主题 - 匹配useTheme.ts */
[data-theme="light"] {
  --quantum-bg-primary: #ffffff;
  --quantum-bg-secondary: #f8fafc;
  --quantum-bg-elevated: #f1f5f9;
  --quantum-bg-surface: rgba(255, 255, 255, 0.9);
  --quantum-bg-glass: rgba(255, 255, 255, 0.8);
  --quantum-bg-hover: #e2e8f0;
  --quantum-fg-primary: #0f172a;
  --quantum-fg-secondary: #334155;
  --quantum-fg-muted: #64748b;
  --quantum-border-color: rgba(0, 212, 255, 0.3);
  --quantum-border-glow: rgba(0, 212, 255, 0.5);

  /* 浅色模式发光效果 - 减弱强度 */
  --quantum-glow-primary: 0 0 15px rgba(0, 212, 255, 0.2);
  --quantum-glow-secondary: 0 0 15px rgba(255, 107, 157, 0.2);
  --quantum-glow-accent: 0 0 15px rgba(0, 255, 136, 0.2);
  --quantum-shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.15);
  --quantum-shadow-elevated: 0 4px 16px rgba(0, 0, 0, 0.12);
}

/* 🔄 量子基础样式重置 */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: var(--font-sans);
  font-size: var(--text-base);
  line-height: 1.5;
  color: var(--quantum-fg-primary);
  background: linear-gradient(135deg, var(--quantum-bg-primary) 0%, var(--quantum-bg-secondary) 100%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: all var(--transition-fast);
  position: relative;
  overflow-x: hidden;
}

/* 🎯 量子基础元素样式 */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 600;
  line-height: 1.25;
  color: var(--quantum-fg-primary);
}

h1 {
  font-size: var(--text-4xl);
  color: var(--quantum-fg-primary);
  font-weight: 700;
  transition: all var(--transition-normal);
}

/* 🎨 页面标题专用样式 */
.page-title {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--quantum-fg-primary);
  text-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
  margin-bottom: var(--space-2);
  line-height: 1.2;
}

.page-subtitle {
  font-size: var(--text-base);
  color: var(--quantum-fg-secondary);
  font-weight: 500;
  margin-bottom: var(--space-4);
  opacity: 0.9;
}

/* 🌟 量子标题增强效果 */
.quantum-text-neon {
  color: var(--quantum-primary);
  text-shadow:
    0 0 5px var(--quantum-primary),
    0 0 10px var(--quantum-primary),
    0 0 15px var(--quantum-primary);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.quantum-text-glow {
  color: var(--quantum-accent);
  text-shadow:
    0 0 5px var(--quantum-accent),
    0 0 10px var(--quantum-accent);
  font-weight: 600;
}

.quantum-text-matrix {
  color: var(--quantum-secondary);
  text-shadow:
    0 0 5px var(--quantum-secondary),
    0 0 10px var(--quantum-secondary);
  font-weight: 600;
}

/* 🌞 浅色主题专用文字样式 - 移除模糊的阴影效果 */
[data-theme="light"] .quantum-text-neon {
  color: var(--quantum-primary);
  text-shadow: none;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

[data-theme="light"] .quantum-text-glow {
  color: var(--quantum-accent);
  text-shadow: none;
  font-weight: 600;
}

[data-theme="light"] .quantum-text-matrix {
  color: var(--quantum-secondary);
  text-shadow: none;
  font-weight: 600;
}

[data-theme="light"] .page-title {
  color: var(--quantum-fg-primary);
  text-shadow: none;
}

h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

p {
  margin: 0;
  color: var(--quantum-fg-secondary);
}

a {
  color: var(--quantum-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--quantum-secondary);
}

button {
  font-family: inherit;
  font-size: inherit;
  margin: 0;
  padding: 0;
  border: none;
  background: none;
  cursor: pointer;
  color: inherit;
  transition: all var(--transition-fast);
}

/* 🎨 量子核心组件样式 */
.quantum-card-hologram {
  background: var(--quantum-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--quantum-border-color);
  border-radius: 1rem;
  padding: var(--space-6);
  box-shadow: var(--quantum-shadow-glass);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

/* 🌞 浅色主题专用卡片样式 - 更清晰的背景 */
[data-theme="light"] .quantum-card-hologram {
  background: var(--quantum-bg-glass);
  backdrop-filter: blur(10px);
  border: 1px solid var(--quantum-border-color);
  box-shadow: var(--quantum-shadow-glass);
}

/* 禁用伪元素以避免叠加问题 - 改用简单的悬浮效果 */
.quantum-card-hologram:hover {
  box-shadow: var(--quantum-glow-primary), var(--quantum-shadow-elevated);
  border-color: var(--quantum-border-glow);
  filter: brightness(1.05);
  transform: translateY(-2px);
}

.quantum-card-hologram:active {
  transform: scale(0.98);
}

/* 量子文本样式已移动到上方，避免重复定义 */

.quantum-energy-ring {
  width: 3rem;
  height: 3rem;
  border: 2px solid var(--quantum-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--quantum-bg-glass);
  backdrop-filter: blur(10px);
  position: relative;
  transition: all var(--transition-normal);
}

.quantum-energy-ring:hover {
  box-shadow: var(--quantum-glow-primary), 0 0 0 2px rgba(0, 212, 255, 0.3);
  filter: brightness(1.1);
}

/* 📜 量子滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--quantum-bg-elevated);
}

::-webkit-scrollbar-thumb {
  background: var(--quantum-border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--quantum-primary);
}

/* 🎯 量子焦点样式 */
:focus {
  outline: 2px solid var(--quantum-primary);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* 🔤 字体导入 */
@font-face {
  font-family: 'Inter';
  src: url('./fonts/Inter-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'JetBrains Mono';
  src: url('./fonts/JetBrainsMono-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* ⚡ 量子动画关键帧 */
@keyframes quantumPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: var(--quantum-glow-primary);
  }
  50% {
    transform: scale(1.05);
    box-shadow: var(--quantum-glow-primary), 0 0 40px var(--quantum-primary);
  }
}

@keyframes quantumDataFlow {
  0% {
    background-position: 0% 50%;
    opacity: 0.3;
  }
  50% {
    background-position: 100% 50%;
    opacity: 0.6;
  }
  100% {
    background-position: 200% 50%;
    opacity: 0.3;
  }
}

@keyframes quantumGlow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 🎭 量子动画类 */
.quantum-pulse {
  animation: quantumPulse 2s ease-in-out infinite;
}

.quantum-data-stream {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 212, 255, 0.1),
    transparent
  );
  background-size: 200% 100%;
  animation: quantumDataFlow 8s linear infinite;
}

.quantum-glow-effect {
  animation: quantumGlow 2s ease-in-out infinite;
}

/* 📱 统一响应式断点系统 */
/*
  断点定义：
  - 手机端: 0px - 640px
  - 平板端: 641px - 1023px
  - 桌面端: 1024px+

  重要：所有组件必须使用这些统一的断点！
*/

/* 手机端 (640px及以下) */
@media (max-width: 640px) {
  .quantum-card-hologram {
    margin: var(--space-2);
    padding: var(--space-4);
  }
}

/* 平板端 (641px - 1023px) */
@media (min-width: 641px) and (max-width: 1023px) {
  .quantum-card-hologram {
    margin: var(--space-3);
    padding: var(--space-5);
  }
}

/* 桌面端 (1024px+) */
@media (min-width: 1024px) {
  .quantum-card-hologram {
    margin: var(--space-4);
    padding: var(--space-6);
  }
}

/* 🎯 量子工具类 */
.quantum-matrix-bg {
  background: linear-gradient(135deg, var(--quantum-bg-primary) 0%, var(--quantum-bg-secondary) 100%);
}

.quantum-status-bar {
  display: flex;
  gap: var(--space-4);
  align-items: center;
  flex-wrap: wrap;
}

.quantum-hud-element {
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid var(--quantum-primary);
  color: var(--quantum-primary);
  padding: var(--space-2) var(--space-3);
  border-radius: 0.5rem;
  font-family: var(--font-mono);
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  transition: all var(--transition-fast);
}

/* 🎯 量子状态样式 */
.quantum-status-active {
  background: rgba(34, 197, 94, 0.1);
  border-color: var(--quantum-success);
  color: var(--quantum-success);
}

.quantum-status-inactive {
  background: rgba(239, 68, 68, 0.1);
  border-color: var(--quantum-danger);
  color: var(--quantum-danger);
}

.quantum-status-admin {
  background: rgba(147, 51, 234, 0.1);
  border-color: var(--quantum-primary);
  color: var(--quantum-primary);
}

.quantum-status-user {
  background: rgba(59, 130, 246, 0.1);
  border-color: var(--quantum-secondary);
  color: var(--quantum-secondary);
}

.quantum-status-success {
  background: rgba(34, 197, 94, 0.1);
  border-color: var(--quantum-success);
  color: var(--quantum-success);
}

.quantum-status-warning {
  background: rgba(245, 158, 11, 0.1);
  border-color: var(--quantum-warning);
  color: var(--quantum-warning);
}

.quantum-status-danger {
  background: rgba(239, 68, 68, 0.1);
  border-color: var(--quantum-danger);
  color: var(--quantum-danger);
}

.quantum-status-info {
  background: rgba(59, 130, 246, 0.1);
  border-color: var(--quantum-info);
  color: var(--quantum-info);
}

/* 🌞 浅色主题专用HUD元素样式 */
[data-theme="light"] .quantum-hud-element {
  background: rgba(0, 212, 255, 0.15);
  border: 1px solid var(--quantum-primary);
  color: var(--quantum-primary);
}

/* �🎮 量子按钮系统 */
.quantum-btn-primary {
  background: linear-gradient(135deg, var(--quantum-primary), var(--quantum-secondary));
  color: white;
  border: none;
  padding: var(--space-3) var(--space-6);
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  box-shadow: var(--quantum-shadow-normal);
}

.quantum-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--quantum-glow-primary), var(--quantum-shadow-elevated);
  filter: brightness(1.1);
}

.quantum-btn-secondary {
  background: var(--quantum-bg-glass);
  color: var(--quantum-primary);
  border: 1px solid var(--quantum-border-color);
  padding: var(--space-3) var(--space-6);
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  backdrop-filter: blur(10px);
}

.quantum-btn-secondary:hover {
  background: var(--quantum-primary);
  color: white;
  border-color: var(--quantum-primary);
  box-shadow: var(--quantum-glow-primary);
}

.quantum-btn-ghost {
  background: transparent;
  color: var(--quantum-fg-secondary);
  border: 1px solid var(--quantum-border-color);
  padding: var(--space-2) var(--space-4);
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.quantum-btn-ghost:hover {
  background: var(--quantum-bg-elevated);
  color: var(--quantum-fg-primary);
  border-color: var(--quantum-border-glow);
}

.quantum-btn-pulse {
  background: linear-gradient(135deg, var(--quantum-accent), var(--quantum-primary));
  color: white;
  border: none;
  padding: var(--space-3) var(--space-6);
  border-radius: 0.5rem;
  font-weight: 700;
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  animation: quantumPulse 2s ease-in-out infinite;
}

.quantum-btn-pulse:hover {
  animation-play-state: paused;
  transform: scale(1.05);
  box-shadow: var(--quantum-glow-accent);
}

.quantum-btn-warning {
  background: linear-gradient(135deg, var(--quantum-warning), #f59e0b);
  color: white;
  border: none;
  padding: var(--space-3) var(--space-6);
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.quantum-btn-warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.4);
  filter: brightness(1.1);
}

.quantum-btn-danger {
  background: linear-gradient(135deg, var(--quantum-danger), #dc2626);
  color: white;
  border: none;
  padding: var(--space-3) var(--space-6);
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.quantum-btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.4);
  filter: brightness(1.1);
}

.quantum-btn-info {
  background: linear-gradient(135deg, var(--quantum-info), #2563eb);
  color: white;
  border: none;
  padding: var(--space-3) var(--space-6);
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.quantum-btn-info:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
  filter: brightness(1.1);
}

/* � 浅色主题专用按钮样式 */
[data-theme="light"] .quantum-btn-secondary {
  background: var(--quantum-bg-glass);
  color: var(--quantum-primary);
  border: 1px solid var(--quantum-border-color);
  backdrop-filter: blur(5px);
}

[data-theme="light"] .quantum-btn-secondary:hover {
  background: var(--quantum-primary);
  color: white;
  border-color: var(--quantum-primary);
  box-shadow: var(--quantum-glow-primary);
}

[data-theme="light"] .quantum-btn-ghost {
  background: rgba(255, 255, 255, 0.5);
  color: var(--quantum-fg-secondary);
  border: 1px solid var(--quantum-border-color);
  backdrop-filter: blur(5px);
}

[data-theme="light"] .quantum-btn-ghost:hover {
  background: var(--quantum-bg-elevated);
  color: var(--quantum-fg-primary);
  border-color: var(--quantum-border-glow);
}

/* �🎯 量子输入框系统 */
.quantum-input {
  background: var(--quantum-bg-glass);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-base);
  color: var(--quantum-fg-primary);
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
}

.quantum-input:focus {
  outline: none;
  border-color: var(--quantum-primary);
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.quantum-input::placeholder {
  color: var(--quantum-fg-muted);
}

.quantum-select {
  background: var(--quantum-bg-glass);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-base);
  color: var(--quantum-fg-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
}

.quantum-select:focus {
  outline: none;
  border-color: var(--quantum-primary);
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

/* 🌞 浅色主题专用输入框样式 */
[data-theme="light"] .quantum-input,
[data-theme="light"] .quantum-select {
  background: var(--quantum-bg-glass);
  backdrop-filter: blur(5px);
  border: 1px solid var(--quantum-border-color);
  color: var(--quantum-fg-primary);
}

[data-theme="light"] .quantum-input:focus,
[data-theme="light"] .quantum-select:focus {
  background: rgba(255, 255, 255, 0.95);
  border-color: var(--quantum-primary);
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.15);
}

[data-theme="light"] .quantum-input::placeholder {
  color: var(--quantum-fg-muted);
}

/* 🌞 浅色主题专用textarea样式 */
[data-theme="light"] .quantum-textarea {
  background: var(--quantum-bg-glass);
  backdrop-filter: blur(5px);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-base);
  color: var(--quantum-fg-primary);
  transition: all var(--transition-fast);
  resize: vertical;
  font-family: inherit;
}

[data-theme="light"] .quantum-textarea:focus {
  background: rgba(255, 255, 255, 0.95);
  border-color: var(--quantum-primary);
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.15);
  outline: none;
}

[data-theme="light"] .quantum-textarea::placeholder {
  color: var(--quantum-fg-muted);
}

/* 📊 量子指标卡片系统 */
.quantum-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.quantum-metric-card {
  padding: var(--space-6);
  position: relative;
  overflow: hidden;
}

.quantum-metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
}

.quantum-metric-ring {
  width: 3.5rem;
  height: 3.5rem;
  border: 2px solid var(--quantum-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--quantum-bg-glass);
  backdrop-filter: blur(10px);
  position: relative;
  animation: quantumPulse 3s ease-in-out infinite;
}

.quantum-metric-icon {
  font-size: var(--text-xl);
  color: var(--quantum-primary);
}

.quantum-metric-status {
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.quantum-metric-content {
  text-align: center;
}

.quantum-metric-value {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin-bottom: var(--space-2);
}

.quantum-metric-label {
  font-size: var(--text-sm);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: var(--space-3);
}

.quantum-metric-progress {
  margin-bottom: var(--space-3);
}

.quantum-progress-bar {
  width: 100%;
  height: 4px;
  background: var(--quantum-bg-elevated);
  border-radius: 2px;
  overflow: hidden;
}

.quantum-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--quantum-primary), var(--quantum-accent));
  border-radius: 2px;
  transition: width var(--transition-slow);
  position: relative;
}

.quantum-progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: quantumDataFlow 2s linear infinite;
}

.quantum-metric-change {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-1);
  font-size: var(--text-xs);
  font-weight: 600;
}

.quantum-metric-change.positive {
  color: var(--quantum-success);
}

.quantum-metric-change.negative {
  color: var(--quantum-error);
}

/* 📋 量子页面头部系统 - 专用清晰样式 */
.page-header {
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  border-radius: 1rem;
  position: relative;

  /* 清晰的背景，避免伪元素叠加 */
  background: var(--quantum-bg-glass) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid var(--quantum-border-color) !important;
  box-shadow: var(--quantum-shadow-normal) !important;

  /* 禁用伪元素叠加和动画干扰 */
  overflow: visible;
  animation: none !important;
}

/* 强制禁用页面头部的动画背景 */
.page-header.quantum-data-stream {
  background: var(--quantum-bg-glass) !important;
  animation: none !important;
}

.page-header.quantum-matrix-bg {
  background: var(--quantum-bg-glass) !important;
}

/* 页面头部专用悬浮效果 - 简洁版本 */
.page-header:hover {
  border-color: var(--quantum-border-glow);
  box-shadow: var(--quantum-glow-primary), var(--quantum-shadow-elevated);
  transition: all var(--transition-normal);
}

.header-content {
  position: relative;
  z-index: 10;
}

/* 🎛️ 量子操作按钮容器 */
.header-actions {
  display: flex;
  gap: var(--space-3);
  align-items: center;
  margin-top: var(--space-4);
  flex-wrap: wrap;
}

/* 🎮 统一的量子操作按钮系统 */
.action-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: var(--quantum-bg-elevated);
  border: 1px solid var(--quantum-border-color);
  border-radius: 0.5rem;
  color: var(--quantum-fg-primary);
  font-family: var(--font-sans);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

/* 按钮悬停效果 */
.action-btn:hover {
  background: var(--quantum-bg-surface);
  border-color: var(--quantum-primary);
  color: var(--quantum-primary);
  transform: translateY(-1px);
  box-shadow: var(--quantum-shadow-elevated);
}

/* 按钮激活效果 */
.action-btn:active {
  transform: translateY(0);
  box-shadow: var(--quantum-shadow-normal);
}

/* 🔄 刷新按钮特殊样式 */
.action-btn.refresh-btn:hover {
  color: var(--quantum-primary);
  box-shadow: var(--quantum-glow-primary);
}

.action-btn.refresh-btn:hover i {
  animation: spin 1s linear infinite;
}

/* 🎯 主要操作按钮 */
.action-btn.primary-btn {
  background: linear-gradient(135deg, var(--quantum-primary), var(--quantum-secondary));
  color: white;
  border-color: var(--quantum-primary);
  font-weight: var(--font-semibold);
}

.action-btn.primary-btn:hover {
  background: linear-gradient(135deg, var(--quantum-secondary), var(--quantum-primary));
  transform: translateY(-2px);
  box-shadow: var(--quantum-glow-primary);
}

/* 🔒 次要操作按钮 */
.action-btn.secondary-btn {
  background: var(--quantum-bg-glass);
  backdrop-filter: blur(10px);
  border-color: var(--quantum-border-color);
}

.action-btn.secondary-btn:hover {
  background: var(--quantum-bg-elevated);
  border-color: var(--quantum-border-glow);
}

/* ⚠️ 危险操作按钮 */
.action-btn.danger-btn {
  color: var(--quantum-error);
  border-color: var(--quantum-error);
}

.action-btn.danger-btn:hover {
  background: var(--quantum-error);
  color: white;
  box-shadow: 0 0 20px rgba(255, 71, 87, 0.3);
}

/* 🎭 按钮图标样式 */
.action-btn i {
  font-size: 1rem;
  transition: all var(--transition-fast);
}

/* 🌊 量子涟漪效果 */
.action-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(0, 212, 255, 0.1);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
  pointer-events: none;
}

.action-btn:active::before {
  width: 200px;
  height: 200px;
}

/* 🌟 量子特殊按钮样式 */
.quantum-btn-pulse {
  background: linear-gradient(135deg, var(--quantum-primary), var(--quantum-accent));
  color: white;
  border: 1px solid var(--quantum-primary);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: relative;
  overflow: hidden;
}

.quantum-btn-pulse::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.quantum-btn-pulse:hover::after {
  left: 100%;
}

.quantum-btn-pulse:hover {
  transform: translateY(-2px);
  box-shadow: var(--quantum-glow-primary);
  animation: quantumPulse 2s infinite;
}

/* 🔧 量子次要按钮 */
.quantum-btn-secondary {
  background: var(--quantum-bg-glass);
  backdrop-filter: blur(10px);
  border: 1px solid var(--quantum-border-color);
  color: var(--quantum-fg-primary);
  font-weight: var(--font-medium);
}

.quantum-btn-secondary:hover {
  border-color: var(--quantum-primary);
  color: var(--quantum-primary);
  box-shadow: var(--quantum-glow-primary);
}

.quantum-btn-secondary.active {
  background: var(--quantum-primary);
  color: white;
  border-color: var(--quantum-primary);
}

/* 🎯 量子主要按钮 */
.quantum-btn-primary {
  background: linear-gradient(135deg, var(--quantum-primary), var(--quantum-secondary));
  color: white;
  border: 1px solid var(--quantum-primary);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.quantum-btn-primary:hover {
  background: linear-gradient(135deg, var(--quantum-secondary), var(--quantum-primary));
  transform: translateY(-2px);
  box-shadow: var(--quantum-glow-primary);
}

/* 🌊 量子涟漪效果 */
.quantum-ripple {
  position: relative;
  overflow: hidden;
}

.quantum-ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(0, 212, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.4s, height 0.4s;
  pointer-events: none;
}

.quantum-ripple:active::before {
  width: 300px;
  height: 300px;
}

/* 🎬 量子动画 */
@keyframes quantumPulse {
  0%, 100% {
    box-shadow: var(--quantum-glow-primary);
  }
  50% {
    box-shadow: var(--quantum-glow-primary), 0 0 30px var(--quantum-primary);
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 📱 响应式按钮优化 */
@media (max-width: 640px) {
  .header-actions {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-2);
  }

  .action-btn,
  .quantum-btn-pulse,
  .quantum-btn-secondary,
  .quantum-btn-primary {
    width: 100%;
    justify-content: center;
    padding: var(--space-4) var(--space-3);
    font-size: var(--text-base);
  }

  .action-btn span {
    display: block;
  }
}

@media (min-width: 641px) and (max-width: 1023px) {
  .header-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .action-btn {
    min-width: 140px;
  }
}

/* 🎨 主题特定按钮样式 */
[data-theme="light"] .action-btn {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(0, 0, 0, 0.1);
  color: var(--quantum-fg-primary);
}

[data-theme="light"] .action-btn:hover {
  background: rgba(255, 255, 255, 1);
  border-color: var(--quantum-primary);
  box-shadow: 0 4px 20px rgba(0, 212, 255, 0.2);
}

[data-theme="dark"] .action-btn {
  background: var(--quantum-bg-elevated);
  border-color: var(--quantum-border-color);
}

[data-theme="dark"] .action-btn:hover {
  background: var(--quantum-bg-surface);
  box-shadow: var(--quantum-glow-primary);
}

/* � 量子特殊按钮样式 */
.quantum-btn-pulse {
  background: linear-gradient(135deg, var(--quantum-primary), var(--quantum-accent));
  color: white;
  border: 1px solid var(--quantum-primary);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: relative;
  overflow: hidden;
}

.quantum-btn-pulse::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.quantum-btn-pulse:hover::after {
  left: 100%;
}

.quantum-btn-pulse:hover {
  transform: translateY(-2px);
  box-shadow: var(--quantum-glow-primary);
  animation: quantumPulse 2s infinite;
}

/* 🔧 量子次要按钮 */
.quantum-btn-secondary {
  background: var(--quantum-bg-glass);
  backdrop-filter: blur(10px);
  border: 1px solid var(--quantum-border-color);
  color: var(--quantum-fg-primary);
  font-weight: var(--font-medium);
}

.quantum-btn-secondary:hover {
  border-color: var(--quantum-primary);
  color: var(--quantum-primary);
  box-shadow: var(--quantum-glow-primary);
}

.quantum-btn-secondary.active {
  background: var(--quantum-primary);
  color: white;
  border-color: var(--quantum-primary);
}

/* 🎯 量子主要按钮 */
.quantum-btn-primary {
  background: linear-gradient(135deg, var(--quantum-primary), var(--quantum-secondary));
  color: white;
  border: 1px solid var(--quantum-primary);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.quantum-btn-primary:hover {
  background: linear-gradient(135deg, var(--quantum-secondary), var(--quantum-primary));
  transform: translateY(-2px);
  box-shadow: var(--quantum-glow-primary);
}

/* 🌊 量子涟漪效果 */
.quantum-ripple {
  position: relative;
  overflow: hidden;
}

.quantum-ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(0, 212, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.4s, height 0.4s;
  pointer-events: none;
}

.quantum-ripple:active::before {
  width: 300px;
  height: 300px;
}

/* 🎬 量子动画 */
@keyframes quantumPulse {
  0%, 100% {
    box-shadow: var(--quantum-glow-primary);
  }
  50% {
    box-shadow: var(--quantum-glow-primary), 0 0 30px var(--quantum-primary);
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* �🎯 量子状态栏增强 */
.quantum-status-bar {
  display: flex;
  gap: var(--space-4);
  align-items: center;
  flex-wrap: wrap;
  margin-top: var(--space-4);
}

.quantum-hud-element:hover {
  background: var(--quantum-primary);
  color: white;
  box-shadow: var(--quantum-glow-primary);
  transform: translateY(-1px);
}

/* 🎨 量子背景增强 - 简化版本 */
.quantum-matrix-bg {
  background: linear-gradient(135deg, var(--quantum-bg-primary) 0%, var(--quantum-bg-secondary) 100%);
  position: relative;
}

/* 🔄 量子交互增强 - 已合并到主要定义中 */

/* 🎮 按钮交互增强 */
.quantum-btn-primary:active,
.quantum-btn-secondary:active,
.quantum-btn-pulse:active {
  transform: translateY(0) scale(0.95);
}

.quantum-btn-ghost:active {
  transform: scale(0.95);
}

/* 📱 响应式页面头部 */
@media (max-width: 640px) {
  .page-header {
    padding: var(--space-4);
    margin-bottom: var(--space-4);
  }

  .page-title {
    font-size: var(--text-2xl);
  }

  .page-subtitle {
    font-size: var(--text-sm);
  }

  .header-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .quantum-status-bar {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-2);
  }

  .quantum-hud-element {
    text-align: center;
  }
}

@media (min-width: 641px) and (max-width: 1023px) {
  .header-actions {
    justify-content: center;
  }
}

/* 🎯 量子交互增强系统 */
.quantum-interactive {
  transition: all var(--transition-normal);
  cursor: pointer;
}

.quantum-interactive:hover {
  transform: translateY(-2px);
  box-shadow: var(--quantum-glow-primary);
}

.quantum-interactive:active {
  transform: translateY(0) scale(0.98);
}

/* 🌟 量子悬浮效果 */
.quantum-hover-glow {
  position: relative;
  overflow: hidden;
}

.quantum-hover-glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.quantum-hover-glow:hover::before {
  left: 100%;
}

/* 🎮 量子点击反馈 */
.quantum-ripple {
  position: relative;
  overflow: hidden;
}

.quantum-ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(0, 212, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.quantum-ripple:active::after {
  width: 300px;
  height: 300px;
}

/* 🔄 量子加载状态 */
.quantum-loading {
  position: relative;
  pointer-events: none;
  opacity: 0.7;
}

.quantum-loading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  border: 2px solid var(--quantum-primary);
  border-top: 2px solid transparent;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: quantumSpin 1s linear infinite;
}

/* 📊 量子数据流动效果 */
.quantum-data-flow {
  position: relative;
  overflow: hidden;
}

.quantum-data-flow::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(0, 212, 255, 0.1) 50%, transparent 70%);
  animation: quantumDataFlow 3s linear infinite;
}

/* 🎨 量子状态指示器 */
.quantum-status-success {
  color: var(--quantum-success);
  text-shadow: 0 0 10px var(--quantum-success);
}

.quantum-status-warning {
  color: var(--quantum-warning);
  text-shadow: 0 0 10px var(--quantum-warning);
}

.quantum-status-error {
  color: var(--quantum-error);
  text-shadow: 0 0 10px var(--quantum-error);
}

.quantum-status-info {
  color: var(--quantum-info);
  text-shadow: 0 0 10px var(--quantum-info);
}

/* 🌈 量子渐变背景 */
.quantum-gradient-primary {
  background: linear-gradient(135deg, var(--quantum-primary), var(--quantum-secondary));
}

.quantum-gradient-accent {
  background: linear-gradient(135deg, var(--quantum-accent), var(--quantum-primary));
}

.quantum-gradient-success {
  background: linear-gradient(135deg, var(--quantum-success), var(--quantum-primary));
}

/* 🎯 量子焦点状态 */
.quantum-focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.3);
  border-color: var(--quantum-primary);
}

/* 🔥 量子高亮效果 */
.quantum-highlight {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(255, 107, 157, 0.1));
  border: 1px solid var(--quantum-primary);
  box-shadow: var(--quantum-glow-primary);
}


