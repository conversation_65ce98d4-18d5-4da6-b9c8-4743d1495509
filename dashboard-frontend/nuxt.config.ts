import { defineNuxtConfig } from 'nuxt/config'
import { presetUno, presetIcons, presetTypography } from 'unocss'
import { transformerDirectives, transformerVariantGroup } from 'unocss'

export default defineNuxtConfig({
  compatibilityDate: '2024-04-03',
  ssr: false,

  typescript: {
    strict: true,
    tsConfig: {
      compilerOptions: {
        strict: true,
        noImplicitAny: true,
        strictNullChecks: true,
        strictFunctionTypes: true,
        strictBindCallApply: true,
        strictPropertyInitialization: true,
        noImplicitThis: true,
        alwaysStrict: true,
      },
    },
  },

  devtools: { enabled: true },

  css: [
    '~/assets/css/quantum-variables.css'
  ],

  modules: [
    '@unocss/nuxt',
    '@nuxtjs/color-mode',
    '@pinia/nuxt',
    '@vueuse/nuxt',
  ],

  // 全局路由中间件 - 确保所有页面都需要管理员权限
  router: {
    middleware: ['auth']
  },

  unocss: {
    presets: [
      presetUno(),
      presetIcons({
        scale: 1.2,
        warn: true,
        collections: {
          carbon: true,
          lucide: true,
          ph: true,
          mdi: true,
          heroicons: true,
        },
        extraProperties: {
          'display': 'inline-block',
          'vertical-align': 'middle',
        },
      }),
      presetTypography(),
      // 注释掉Web Fonts预设，避免网络请求
      // presetWebFonts({
      //   fonts: {
      //     sans: ['Inter', 'system-ui', 'sans-serif'],
      //     serif: ['EB Garamond', 'serif'],
      //     mono: ['Fira Code', 'JetBrains Mono', 'monospace'],
      //   },
      //   provider: 'none', // 禁用网络字体，使用本地字体
      // }),
    ],
    transformers: [
      transformerDirectives({ enforce: 'pre' }),
      transformerVariantGroup(),
    ],
    theme: {
      // 使用CSS变量，不需要JS对象
    },
    shortcuts: {
      // 量子Dashboard专用快捷类
      'quantum-card': 'bg-[var(--quantum-bg-surface)] border border-[var(--quantum-border-color)] rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300',
      'quantum-card-hologram': 'quantum-card backdrop-blur-md bg-[var(--quantum-hologram-bg)] border-[var(--quantum-border-emphasis)] shadow-[var(--quantum-glow-primary)]',
      'quantum-btn': 'px-4 py-2 rounded-lg font-medium transition-all var(--quantum-transition-fast) hover:scale-105',
      'quantum-btn-primary': 'quantum-btn bg-[var(--quantum-primary)] text-black hover:shadow-[var(--quantum-glow-primary)]',
      'quantum-btn-secondary': 'quantum-btn bg-[var(--quantum-bg-elevated)] text-[var(--quantum-fg-primary)] border border-[var(--quantum-border-color)] hover:border-[var(--quantum-primary)]',
      'quantum-btn-pulse': 'quantum-btn-primary animate-pulse hover:animate-none',
      'quantum-input': 'w-full px-3 py-2 bg-[var(--quantum-bg-elevated)] border border-[var(--quantum-border-color)] rounded-lg focus:border-[var(--quantum-primary)] focus:outline-none transition-colors',
      'quantum-text-gradient': 'bg-gradient-to-r from-[var(--quantum-primary)] via-[var(--quantum-accent)] to-[var(--quantum-secondary)] bg-clip-text text-transparent',
      'quantum-text-neon': 'text-[var(--quantum-primary)] font-bold shadow-[var(--quantum-glow-primary)]',
      'quantum-text-glow': 'text-[var(--quantum-accent)] shadow-[var(--quantum-glow-accent)]',
      'quantum-text-matrix': 'font-mono text-[var(--quantum-accent)] font-semibold uppercase tracking-wider',
      'quantum-glow': 'shadow-[var(--quantum-glow-primary)]',
      'quantum-border': 'border border-[var(--quantum-border-emphasis)] shadow-[var(--quantum-glow-primary)]',
      'quantum-border-energy': 'border border-[var(--quantum-accent)] shadow-[var(--quantum-glow-accent)]',
      'quantum-border-animated': 'border border-[var(--quantum-border-glow)] animate-pulse',
      'quantum-data-stream': 'bg-[var(--quantum-bg-glass)] backdrop-blur-sm',
      'quantum-matrix-bg': 'bg-[var(--quantum-bg-glass)] backdrop-blur-md',
      'quantum-hud-element': 'bg-[var(--quantum-primary)]/10 border border-[var(--quantum-primary)]/30 text-[var(--quantum-primary)] font-mono text-xs uppercase tracking-wider',
      'quantum-progress-bar': 'bg-[var(--quantum-bg-muted)] rounded-full overflow-hidden',
      'quantum-progress-fill': 'bg-gradient-to-r from-[var(--quantum-primary)] to-[var(--quantum-accent)] h-full transition-all duration-300',
      'quantum-energy-ring': 'border-2 border-[var(--quantum-accent)] rounded-full bg-[var(--quantum-accent)]/10 flex items-center justify-center shadow-[var(--quantum-glow-accent)]',
      'quantum-sound-effect': 'hover:animate-ping',
      'quantum-ripple-effect': 'relative overflow-hidden before:absolute before:inset-0 before:bg-[var(--quantum-primary)]/20 before:scale-0 hover:before:scale-100 before:transition-transform before:duration-300',
    },
    rules: [
      // 自定义规则
      [/^dashboard-grid-(\d+)$/, ([, d]) => ({ 'grid-template-columns': `repeat(${d}, minmax(0, 1fr))` })],
      [/^dashboard-gap-(\d+)$/, ([, d]) => ({ gap: `${d * 0.25}rem` })],
    ],
  },

  colorMode: {
    preference: 'system',
    fallback: 'light',
    classSuffix: '',
  },



  app: {
    head: {
      title: 'AR-System Dashboard',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'description', content: 'AR System Dashboard - 量子星核管理控制台' },
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        {
          rel: 'preconnect',
          href: 'https://fonts.googleapis.com'
        },
        {
          rel: 'preconnect',
          href: 'https://fonts.gstatic.com',
          crossorigin: ''
        },
        {
          rel: 'stylesheet',
          href: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap'
        }
      ],
    },
  },

  performance: {
    images: {
      lazyLoad: true,
    },
  },

  nitro: {
    preset: 'node-server',
    routeRules: {
      '/api/**': { proxy: process.env.API_BASE_URL || 'http://localhost:8000' },
    },
    experimental: {
      wasm: true
    }
  },

  build: {
    transpile: ['three', '@vueuse/core'],
  },

  runtimeConfig: {
    public: {
      apiBase: process.env.API_BASE_URL || 'http://localhost:8000',
      wsBase: process.env.WS_BASE_URL || 'ws://localhost:8000',
      environment: process.env.NODE_ENV || 'development',
    }
  },

  components: [
    {
      path: '~/components',
      pathPrefix: false
    }
  ],

  imports: {
    dirs: [
      'composables',
      'utils',
      'stores',
      'types'
    ]
  },

  vite: {
    optimizeDeps: {
      include: ['three']
    },
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            'three': ['three'],
            'dashboard': ['@vueuse/core']
          }
        }
      }
    }
  },

  experimental: {
    payloadExtraction: false,
    typedPages: true
  }
})
