<script setup lang="ts">
// @ts-nocheck
// 主题切换动画配置
const themeTransition = {
  enterClass: 'transition-colors duration-300',
  leaveClass: 'transition-colors duration-300',
  enterActiveClass: 'transition-colors duration-300',
  leaveActiveClass: 'transition-colors duration-300'
};
</script>

<template>
  <ThemeThemeProvider>
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </ThemeThemeProvider>
</template>

<style>
/* 全局样式 - 所有变量已统一到 variables.css */

/* 主题过渡效果 */
.dark, .light {
  transition: background-color var(--transition-normal) var(--ease-default),
              color var(--transition-normal) var(--ease-default);
}

/* 页面过渡动画 */
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from,
.page-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

/* 布局过渡 */
.layout-enter-active,
.layout-leave-active {
  transition: all 0.3s ease;
}

.layout-enter-from,
.layout-leave-to {
  opacity: 0;
}
</style>
