import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 权限类型定义
export interface Permission {
  id: string
  name: string
  resource: string
  action: string
  conditions?: Record<string, any>
}

// 用户角色类型
export interface UserRole {
  id: string
  name: string
  displayName: string
  permissions: Permission[]
  level: number
}

// 用户偏好设置
export interface UserPreferences {
  theme: 'light' | 'dark' | 'system'
  language: 'zh' | 'en'
  notifications: {
    email: boolean
    push: boolean
    sms: boolean
  }
  privacy: {
    profileVisibility: 'public' | 'private' | 'friends'
    activityTracking: boolean
    dataSharing: boolean
  }
}

// 用户活动记录
export interface UserActivity {
  id: string
  type: 'login' | 'logout' | 'profile_update' | 'purchase' | 'review' | 'other'
  description: string
  timestamp: Date
  metadata?: Record<string, any>
  ipAddress?: string
  userAgent?: string
}

// 增强的用户类型定义
export interface User {
  id: string
  username: string
  email: string
  displayName: string
  avatar?: string
  role: UserRole
  preferences: UserPreferences
  isEmailVerified: boolean
  isActive: boolean
  is_superuser?: boolean // 添加管理员标识
  createdAt: Date
  lastLoginAt: Date
  lastActivityAt: Date
  loginCount: number
  profile: {
    bio?: string
    location?: string
    website?: string
    socialLinks?: Record<string, string>
  }
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const refreshToken = ref<string | null>(null)
  const isLoading = ref(false)
  const activities = ref<UserActivity[]>([])

  // 计算属性
  const isAuthenticated = computed(() => !!user.value && !!token.value)
  const isLoggedIn = computed(() => !!user.value && !!token.value) // 别名，用于兼容
  const currentUser = computed(() => user.value)
  const userPermissions = computed(() => user.value?.role.permissions || [])
  const hasPermission = computed(() => (resource: string, action: string) => {
    return userPermissions.value.some(p =>
      p.resource === resource && p.action === action
    )
  })

  // 默认角色和权限
  const defaultRole: UserRole = {
    id: 'user',
    name: 'user',
    displayName: '普通用户',
    level: 1,
    permissions: [
      { id: 'profile_read', name: '查看个人资料', resource: 'profile', action: 'read' },
      { id: 'profile_update', name: '更新个人资料', resource: 'profile', action: 'update' },
      { id: 'ecommerce_browse', name: '浏览商品', resource: 'ecommerce', action: 'browse' },
      { id: 'ecommerce_purchase', name: '购买商品', resource: 'ecommerce', action: 'purchase' }
    ]
  }

  // 默认用户偏好
  const defaultPreferences: UserPreferences = {
    theme: 'system',
    language: 'zh',
    notifications: {
      email: true,
      push: true,
      sms: false
    },
    privacy: {
      profileVisibility: 'public',
      activityTracking: true,
      dataSharing: false
    }
  }

  // 记录用户活动
  const addActivity = (activity: Omit<UserActivity, 'id' | 'timestamp'>) => {
    const newActivity: UserActivity = {
      id: Date.now().toString(),
      timestamp: new Date(),
      ...activity
    }
    activities.value.unshift(newActivity)

    // 只保留最近100条活动记录
    if (activities.value.length > 100) {
      activities.value = activities.value.slice(0, 100)
    }

    // 保存到localStorage
    localStorage.setItem('user-activities', JSON.stringify(activities.value))
  }

  // 登录方法 - 支持两种参数格式
  const login = async (userData: any) => {
    isLoading.value = true
    try {
      // 如果传入的是登录凭据，调用API
      if (userData.email && userData.password && !userData.id) {
        // 这是登录凭据，需要调用API
        const { auth } = useApi()
        const response = await auth.login({
          username: userData.email,
          password: userData.password
        })

        const userInfo = await auth.me(response.access_token)

        // 设置用户数据
        user.value = {
          id: userInfo.id.toString(),
          username: userInfo.username,
          email: userInfo.email,
          displayName: userInfo.full_name || userInfo.username,
          avatar: userInfo.avatar_url || '/images/default-avatar.png',
          role: defaultRole,
          preferences: defaultPreferences,
          isEmailVerified: userInfo.is_verified,
          isActive: userInfo.is_active,
          createdAt: new Date(userInfo.created_at),
          lastLoginAt: new Date(),
          lastActivityAt: new Date(),
          loginCount: userInfo.login_count || 0,
          profile: {
            bio: userInfo.bio || '',
            location: userInfo.location || '',
            website: userInfo.website || ''
          }
        }

        token.value = response.access_token
      } else {
        // 这是已经处理过的用户数据，直接设置
        user.value = {
          id: userData.id.toString(),
          username: userData.name,
          email: userData.email,
          displayName: userData.name,
          avatar: '/images/default-avatar.png',
          role: defaultRole,
          preferences: defaultPreferences,
          isEmailVerified: true,
          isActive: true,
          createdAt: new Date(),
          lastLoginAt: new Date(),
          lastActivityAt: new Date(),
          loginCount: 1,
          profile: {
            bio: '',
            location: '',
            website: ''
          }
        }

        token.value = userData.token
      }

      // 保存到localStorage
      localStorage.setItem('auth-token', token.value)
      if (refreshToken.value) {
        localStorage.setItem('refresh-token', refreshToken.value)
      }
      localStorage.setItem('user-data', JSON.stringify(user.value))

      // 记录登录活动
      addActivity({
        type: 'login',
        description: '用户登录系统',
        metadata: { email: user.value?.email }
      })

      return user.value
    } catch (error) {
      throw new Error('登录失败')
    } finally {
      isLoading.value = false
    }
  }

  // 注册方法
  const register = async (userData: { username: string; email: string; password: string }) => {
    isLoading.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1500))

      const newUser: User = {
        id: Date.now().toString(),
        username: userData.username,
        email: userData.email,
        displayName: userData.username,
        role: defaultRole,
        preferences: defaultPreferences,
        isEmailVerified: false,
        isActive: true,
        createdAt: new Date(),
        lastLoginAt: new Date(),
        lastActivityAt: new Date(),
        loginCount: 1,
        profile: {
          bio: '',
          location: '',
          website: ''
        }
      }

      user.value = newUser
      token.value = 'mock-jwt-token-' + Date.now()
      refreshToken.value = 'mock-refresh-token-' + Date.now()

      // 保存到localStorage
      localStorage.setItem('auth-token', token.value)
      localStorage.setItem('refresh-token', refreshToken.value)
      localStorage.setItem('user-data', JSON.stringify(newUser))

      // 记录注册活动
      addActivity({
        type: 'other',
        description: '用户注册账户',
        metadata: { username: userData.username, email: userData.email }
      })

      return newUser
    } catch (error) {
      throw new Error('注册失败')
    } finally {
      isLoading.value = false
    }
  }

  // 登出方法
  const logout = () => {
    if (user.value) {
      addActivity({
        type: 'logout',
        description: '用户退出系统'
      })
    }

    // 停止自动权限检查
    stopAutoPermissionCheck()

    user.value = null
    token.value = null
    refreshToken.value = null

    // 清除localStorage - 同时清除两个项目的认证数据
    localStorage.removeItem('auth-token')
    localStorage.removeItem('refresh-token')
    localStorage.removeItem('user-data')
    localStorage.removeItem('user-activities')

    // 清除dashboard的认证数据
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_data')
  }

  // 初始化认证状态
  const initAuth = () => {
    // 优先检查web-frontend自己的认证数据
    let savedToken = localStorage.getItem('auth-token')
    let savedUser = localStorage.getItem('user-data')
    const savedActivities = localStorage.getItem('user-activities')

    // 如果没有web-frontend的认证数据，检查dashboard的认证数据
    if (!savedToken) {
      const dashboardToken = localStorage.getItem('auth_token')
      const dashboardUser = localStorage.getItem('user_data')

      if (dashboardToken && dashboardUser) {
        const dashUser = JSON.parse(dashboardUser)

        // 转换dashboard用户数据到web-frontend格式
        const webUser = {
          id: dashUser.id.toString(),
          username: dashUser.username,
          email: dashUser.email,
          displayName: dashUser.full_name || dashUser.username,
          avatar: dashUser.avatar || '/images/default-avatar.png',
          is_superuser: dashUser.is_superuser, // 保持管理员标识
          role: {
            id: dashUser.is_superuser ? 'admin' : 'user',
            name: dashUser.is_superuser ? 'admin' : 'user',
            displayName: dashUser.is_superuser ? '管理员' : '普通用户',
            permissions: dashUser.permissions?.map(p => ({
              id: p,
              name: p,
              resource: p.split(':')[0] || 'system',
              action: p.split(':')[1] || 'manage'
            })) || [],
            level: dashUser.is_superuser ? 10 : 1
          },
          preferences: {
            theme: 'dark',
            language: 'zh',
            notifications: { email: true, push: true, sms: false },
            privacy: { profileVisibility: 'private', activityTracking: true, dataSharing: false }
          },
          isEmailVerified: true,
          isActive: true,
          createdAt: new Date(),
          lastLoginAt: new Date(),
          lastActivityAt: new Date(),
          loginCount: 1,
          profile: { bio: '', location: '', website: '' }
        }

        // 同步到web-frontend
        token.value = dashboardToken
        user.value = webUser
        localStorage.setItem('auth-token', dashboardToken)
        localStorage.setItem('user-data', JSON.stringify(webUser))

        console.log('从dashboard同步认证状态到web-frontend')
        return
      }
    }

    // 使用web-frontend自己的认证数据
    if (savedToken && savedUser) {
      token.value = savedToken
      refreshToken.value = localStorage.getItem('refresh-token')
      user.value = JSON.parse(savedUser)
    }

    if (savedActivities) {
      activities.value = JSON.parse(savedActivities)
    }

    // 如果用户已登录，启动自动权限检查
    if (token.value && user.value) {
      startAutoPermissionCheck()

      // 延迟1秒后进行首次权限检查
      setTimeout(() => {
        autoRefreshUserInfo()
      }, 1000)
    }
  }

  // 自动权限检查间隔（毫秒）
  const AUTO_REFRESH_INTERVAL = 30000 // 30秒
  let autoRefreshTimer: NodeJS.Timeout | null = null

  // 启动自动权限检查
  const startAutoPermissionCheck = () => {
    if (autoRefreshTimer) {
      clearInterval(autoRefreshTimer)
    }

    autoRefreshTimer = setInterval(() => {
      if (token.value && user.value) {
        autoRefreshUserInfo()
      }
    }, AUTO_REFRESH_INTERVAL)

    console.log('自动权限检查已启动，间隔:', AUTO_REFRESH_INTERVAL / 1000, '秒')
  }

  // 停止自动权限检查
  const stopAutoPermissionCheck = () => {
    if (autoRefreshTimer) {
      clearInterval(autoRefreshTimer)
      autoRefreshTimer = null
      console.log('自动权限检查已停止')
    }
  }

  // 自动刷新用户信息（静默，不显示提示）
  const autoRefreshUserInfo = async () => {
    try {
      console.log('自动检查用户权限...')

      // 检查dashboard的用户数据（如果存在）
      const dashboardUserData = localStorage.getItem('user_data')

      if (dashboardUserData && user.value) {
        const dashUser = JSON.parse(dashboardUserData)

        // 检查权限是否有变化
        const currentIsSuperuser = user.value.is_superuser
        const newIsSuperuser = dashUser.is_superuser

        if (currentIsSuperuser !== newIsSuperuser) {
          console.log('检测到权限变化:', currentIsSuperuser, '->', newIsSuperuser)

          // 更新用户权限
          const updatedUser = {
            ...user.value,
            is_superuser: dashUser.is_superuser,
            role: {
              id: dashUser.is_superuser ? 'admin' : 'user',
              name: dashUser.is_superuser ? 'admin' : 'user',
              displayName: dashUser.is_superuser ? '管理员' : '普通用户',
              permissions: dashUser.permissions?.map(p => ({
                id: p,
                name: p,
                resource: p.split(':')[0] || 'system',
                action: p.split(':')[1] || 'manage'
              })) || [],
              level: dashUser.is_superuser ? 10 : 1
            }
          }

          user.value = updatedUser
          localStorage.setItem('user-data', JSON.stringify(updatedUser))

          console.log('用户权限已自动更新:', updatedUser.role.displayName)

          // 显示权限变化通知
          showPermissionChangeNotification(newIsSuperuser)
        }
      }
    } catch (error) {
      console.error('自动权限检查失败:', error)
    }
  }

  // 显示权限变化通知
  const showPermissionChangeNotification = (isAdmin: boolean) => {
    if (process.client) {
      const notification = document.createElement('div')
      notification.textContent = isAdmin ? '🎉 您已获得管理员权限！' : '📝 您的权限已更新'
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${isAdmin ? 'var(--color-state-success)' : 'var(--color-state-info)'};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: var(--shadow-lg);
        z-index: 9999;
        font-weight: 500;
        animation: slideInRight 0.3s ease-out;
      `

      // 添加动画样式
      if (!document.getElementById('permission-notification-style')) {
        const style = document.createElement('style')
        style.id = 'permission-notification-style'
        style.textContent = `
          @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
          }
        `
        document.head.appendChild(style)
      }

      document.body.appendChild(notification)

      setTimeout(() => {
        if (document.body.contains(notification)) {
          notification.style.animation = 'slideInRight 0.3s ease-out reverse'
          setTimeout(() => {
            if (document.body.contains(notification)) {
              document.body.removeChild(notification)
            }
          }, 300)
        }
      }, 4000)
    }
  }

  // 更新用户信息
  const updateUser = (updatedData: Partial<User>) => {
    if (user.value) {
      user.value = { ...user.value, ...updatedData }
      localStorage.setItem('user-data', JSON.stringify(user.value))

      addActivity({
        type: 'profile_update',
        description: '更新个人资料',
        metadata: { updatedFields: Object.keys(updatedData) }
      })
    }
  }

  // 更新用户偏好
  const updatePreferences = (preferences: Partial<UserPreferences>) => {
    if (user.value) {
      user.value.preferences = { ...user.value.preferences, ...preferences }
      localStorage.setItem('user-data', JSON.stringify(user.value))

      addActivity({
        type: 'other',
        description: '更新用户偏好设置',
        metadata: { preferences }
      })
    }
  }

  // 检查权限
  const checkPermission = (resource: string, action: string): boolean => {
    return hasPermission.value(resource, action)
  }

  // 获取用户活动历史
  const getUserActivities = (limit?: number) => {
    return limit ? activities.value.slice(0, limit) : activities.value
  }

  // 清除活动历史
  const clearActivities = () => {
    activities.value = []
    localStorage.removeItem('user-activities')
  }

  // 从dashboard同步认证状态
  const syncFromDashboard = async (dashUser: any, dashToken: string) => {
    try {
      // 转换dashboard用户数据到web-frontend格式
      const webUser = {
        id: dashUser.id.toString(),
        username: dashUser.username,
        email: dashUser.email,
        displayName: dashUser.full_name || dashUser.username,
        avatar: dashUser.avatar || '/images/default-avatar.png',
        is_superuser: dashUser.is_superuser,
        role: {
          id: dashUser.is_superuser ? 'admin' : 'user',
          name: dashUser.is_superuser ? 'admin' : 'user',
          displayName: dashUser.is_superuser ? '管理员' : '普通用户',
          permissions: dashUser.permissions?.map(p => ({
            id: p,
            name: p,
            resource: p.split(':')[0] || 'system',
            action: p.split(':')[1] || 'manage'
          })) || [],
          level: dashUser.is_superuser ? 10 : 1
        },
        preferences: {
          theme: 'dark',
          language: 'zh',
          notifications: { email: true, push: true, sms: false },
          privacy: { profileVisibility: 'private', activityTracking: true, dataSharing: false }
        },
        isEmailVerified: true,
        isActive: true,
        createdAt: new Date(),
        lastLoginAt: new Date(),
        lastActivityAt: new Date(),
        loginCount: 1,
        profile: { bio: '', location: '', website: '' }
      }

      // 同步到web-frontend
      token.value = dashToken
      user.value = webUser
      localStorage.setItem('auth-token', dashToken)
      localStorage.setItem('user-data', JSON.stringify(webUser))

      // 同时保存到dashboard格式，确保跨系统兼容
      localStorage.setItem('auth_token', dashToken)
      localStorage.setItem('user_data', JSON.stringify(dashUser))

      addActivity({
        type: 'login',
        description: '从Dashboard同步登录',
        metadata: { source: 'dashboard', userId: dashUser.id }
      })

      console.log('从dashboard同步认证状态成功')
    } catch (error) {
      console.error('从dashboard同步认证状态失败:', error)
      throw error
    }
  }

  // 刷新用户信息
  const refreshUserInfo = async () => {
    if (!token.value) {
      throw new Error('No auth token available')
    }

    try {
      console.log('正在从后端API刷新用户信息...')

      // 调用后端API获取最新用户信息
      const response = await fetch('/api/v1/auth/me', {
        headers: {
          'Authorization': `Bearer ${token.value}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`)
      }

      const userData = await response.json()
      console.log('从API获取的用户数据:', userData)

      // 更新用户信息
      if (user.value) {
        const updatedUser = {
          ...user.value,
          id: userData.id.toString(),
          username: userData.username,
          email: userData.email,
          displayName: userData.full_name || userData.username,
          avatar: userData.avatar_url || user.value.avatar,
          is_superuser: userData.is_superuser,
          role: {
            id: userData.is_superuser ? 'admin' : 'user',
            name: userData.is_superuser ? 'admin' : 'user',
            displayName: userData.is_superuser ? '管理员' : '普通用户',
            permissions: [], // 权限需要单独获取
            level: userData.is_superuser ? 10 : 1
          },
          isEmailVerified: userData.is_verified,
          isActive: userData.is_active,
          lastLoginAt: userData.last_login_at ? new Date(userData.last_login_at) : user.value.lastLoginAt,
          loginCount: userData.login_count || user.value.loginCount
        }

        user.value = updatedUser
        localStorage.setItem('user-data', JSON.stringify(updatedUser))

        // 同步到dashboard格式（如果需要）
        const dashboardUser = {
          id: userData.id,
          username: userData.username,
          email: userData.email,
          full_name: userData.full_name || userData.username,
          is_superuser: userData.is_superuser,
          is_staff: userData.is_superuser,
          avatar: userData.avatar_url,
          permissions: []
        }
        localStorage.setItem('user_data', JSON.stringify(dashboardUser))

        console.log('用户权限已刷新:', updatedUser)
        return updatedUser
      }
    } catch (error) {
      console.error('刷新用户信息失败:', error)

      // 如果API调用失败，尝试从dashboard同步
      const dashboardUserData = localStorage.getItem('user_data')
      if (dashboardUserData) {
        console.log('API调用失败，尝试从dashboard同步')
        const dashUser = JSON.parse(dashboardUserData)

        const updatedUser = {
          ...user.value,
          is_superuser: dashUser.is_superuser,
          role: {
            id: dashUser.is_superuser ? 'admin' : 'user',
            name: dashUser.is_superuser ? 'admin' : 'user',
            displayName: dashUser.is_superuser ? '管理员' : '普通用户',
            permissions: dashUser.permissions?.map(p => ({
              id: p,
              name: p,
              resource: p.split(':')[0] || 'system',
              action: p.split(':')[1] || 'manage'
            })) || [],
            level: dashUser.is_superuser ? 10 : 1
          }
        }

        user.value = updatedUser
        localStorage.setItem('user-data', JSON.stringify(updatedUser))

        console.log('从dashboard同步用户权限:', updatedUser)
        return updatedUser
      }

      throw error
    }
  }

  // 刷新token
  const refreshAuthToken = async () => {
    if (!refreshToken.value) {
      throw new Error('No refresh token available')
    }

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))

      const newToken = 'mock-jwt-token-' + Date.now()
      token.value = newToken
      localStorage.setItem('auth-token', newToken)

      return newToken
    } catch (error) {
      // 刷新失败，需要重新登录
      logout()
      throw new Error('Token refresh failed')
    }
  }

  return {
    // 状态
    user: readonly(user),
    token: readonly(token),
    isLoading: readonly(isLoading),
    activities: readonly(activities),

    // 计算属性
    isAuthenticated,
    isLoggedIn,
    currentUser,
    userPermissions,
    hasPermission,

    // 方法
    login,
    register,
    logout,
    initAuth,
    updateUser,
    updatePreferences,
    checkPermission,
    getUserActivities,
    clearActivities,
    addActivity,
    syncFromDashboard,
    refreshUserInfo,
    refreshAuthToken
  }
})
