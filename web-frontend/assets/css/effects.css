/* AR-System 统一效果样式库 */
/* 整合所有视觉效果：量子效果、玻璃态、空间深度、动画等 */

/* ===== 量子效果系统 ===== */

.quantum-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
  pointer-events: none;
}

.quantum-particle {
  position: absolute;
  border-radius: 50%;
  background-color: var(--quantum-particle-color);
  box-shadow: 0 0 8px var(--quantum-particle-glow);
  opacity: 0.5;
  transform-origin: center center;
  animation: quantumParticle var(--quantum-animation-duration) infinite;
}

.quantum-pulsing {
  animation: quantumPulse 2s infinite;
}

/* ===== 玻璃态效果系统 ===== */

.glass-effect {
  background: var(--glass-bg-light);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border: 1px solid var(--glass-border-light);
  box-shadow: var(--glass-shadow);
}

.dark .glass-effect {
  background: var(--glass-bg-dark);
  border: 1px solid var(--glass-border-dark);
}

.glass-effect--heavy {
  backdrop-filter: blur(var(--glass-blur-heavy));
  -webkit-backdrop-filter: blur(var(--glass-blur-heavy));
  background: var(--glass-bg-light-heavy);
}

.dark .glass-effect--heavy {
  background: var(--glass-bg-dark-heavy);
}

.glass-effect--light {
  backdrop-filter: blur(var(--glass-blur-light));
  -webkit-backdrop-filter: blur(var(--glass-blur-light));
  background: var(--glass-bg-light-light);
}

.dark .glass-effect--light {
  background: var(--glass-bg-dark-light);
}

/* ===== 空间深度效果系统 ===== */

.spatial-depth {
  box-shadow: var(--depth-shadow-md);
  transition: var(--depth-transition);
  transform-style: preserve-3d;
  perspective: 1000px;
}

.spatial-depth:hover {
  box-shadow: var(--depth-shadow-lg);
  transform: var(--depth-translate-hover);
}

.spatial-depth--sm { box-shadow: var(--depth-shadow-sm); }
.spatial-depth--md { box-shadow: var(--depth-shadow-md); }
.spatial-depth--lg { box-shadow: var(--depth-shadow-lg); }
.spatial-depth--xl { box-shadow: var(--depth-shadow-xl); }
.spatial-depth--2xl { box-shadow: var(--depth-shadow-2xl); }

.spatial-float {
  animation: spatialFloat 6s ease-in-out infinite;
}

/* ===== 基础过渡效果 ===== */

.transition-all {
  transition-property: all;
  transition-timing-function: var(--ease-default);
  transition-duration: var(--transition-normal);
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: var(--ease-default);
  transition-duration: var(--transition-normal);
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: var(--ease-default);
  transition-duration: var(--transition-normal);
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: var(--ease-default);
  transition-duration: var(--transition-normal);
}

/* ===== 悬停效果类 ===== */

.hover-lift {
  transition: transform var(--transition-normal) var(--ease-out);
}

.hover-lift:hover {
  transform: translateY(-5px);
}

.hover-scale {
  transition: transform var(--transition-normal) var(--ease-out);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow var(--transition-normal) var(--ease-default);
}

.hover-glow:hover {
  box-shadow: 0 0 15px rgba(var(--color-quantum-default-rgb), 0.5);
}

/* ===== 聚焦效果 ===== */

.focus-outline {
  transition: box-shadow var(--transition-fast) var(--ease-default);
}

.focus-outline:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--color-quantum-default-rgb), 0.4);
}

/* ===== 动画类 ===== */

.animate-blink {
  animation: blink 1s step-end infinite;
}

.animate-bounce-slow {
  animation: bounce-slow 2s ease-in-out infinite;
}

.animate-ping-slow {
  animation: ping-slow 3s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.animate-float-1 {
  animation: float-1 15s ease-in-out infinite;
}

.animate-float-2 {
  animation: float-2 18s ease-in-out infinite;
}

.animate-float-3 {
  animation: float-3 12s ease-in-out infinite;
}

.animate-typewriter {
  overflow: hidden;
  white-space: nowrap;
  display: inline-block;
  position: relative;
  animation: typewriter 2s steps(40) 1s forwards;
  width: 0;
}

.animate-typewriter::after {
  content: '';
  position: absolute;
  right: -4px;
  top: 0;
  bottom: 0;
  border-right: 2px solid var(--color-fg-primary);
  animation: blinkCursor 0.8s steps(2) infinite;
}

.animate-gradient {
  background: linear-gradient(-45deg, var(--color-primary), var(--color-secondary), var(--color-accent), var(--color-primary));
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
}

.animate-shine {
  position: relative;
  overflow: hidden;
}

.animate-shine::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-15deg);
  animation: shine 3s infinite;
}

.animate-shake {
  animation: shake 0.8s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

.animate-fade-in {
  animation: fade-in var(--transition-normal) var(--ease-default) forwards;
}

.animate-fade-out {
  animation: fade-out var(--transition-normal) var(--ease-default) forwards;
}

.animate-slide-in-top {
  animation: slide-in-top var(--transition-normal) var(--ease-default) forwards;
}

.animate-slide-in-bottom {
  animation: slide-in-bottom var(--transition-normal) var(--ease-default) forwards;
}

.animate-slide-in-left {
  animation: slide-in-left var(--transition-normal) var(--ease-default) forwards;
}

.animate-slide-in-right {
  animation: slide-in-right var(--transition-normal) var(--ease-default) forwards;
}

.animate-scale-in {
  animation: scale-in var(--transition-normal) var(--ease-out) forwards;
}

.animate-scale-out {
  animation: scale-out var(--transition-normal) var(--ease-default) forwards;
}

.animate-rotate {
  animation: rotate-360 2s linear infinite;
}

/* ===== 页面过渡动画 ===== */

.page-enter-active,
.page-leave-active {
  transition: opacity var(--transition-normal) var(--ease-default);
}

.page-enter-from,
.page-leave-to {
  opacity: 0;
}

/* ===== 关键帧动画定义 ===== */

@keyframes quantumParticle {
  0% {
    transform: translate(0, 0) scale(1) rotate(0deg);
    opacity: 0;
  }
  25% {
    opacity: var(--quantum-particle-opacity, 0.5);
  }
  75% {
    opacity: var(--quantum-particle-opacity, 0.5);
  }
  100% {
    transform: translate(
      calc(var(--random-x, 1) * 100px),
      calc(var(--random-y, 1) * 100px)
    ) scale(0) rotate(360deg);
    opacity: 0;
  }
}

@keyframes quantumPulse {
  0% {
    box-shadow: 0 0 0 0 var(--color-particle-quantum-trail);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(var(--color-quantum-default-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--color-quantum-default-rgb), 0);
  }
}

@keyframes quantumInfoCardFloat {
  0%, 100% {
    transform: translate(-50%, -50%) translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  25% {
    transform: translate(-50%, -50%) translateY(-10px) rotate(90deg);
    opacity: 0.6;
  }
  50% {
    transform: translate(-50%, -50%) translateY(-5px) rotate(180deg);
    opacity: 0.8;
  }
  75% {
    transform: translate(-50%, -50%) translateY(-15px) rotate(270deg);
    opacity: 0.4;
  }
}

@keyframes quantumGlow {
  0%, 100% {
    text-shadow: 0 0 5px var(--color-quantum-default), 0 0 10px var(--color-quantum-default), 0 0 15px var(--color-quantum-default);
  }
  50% {
    text-shadow: 0 0 10px var(--color-quantum-light), 0 0 20px var(--color-quantum-light), 0 0 30px var(--color-quantum-light);
  }
}

@keyframes spatialFloat {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

@keyframes bounce-slow {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes ping-slow {
  0% { transform: scale(1); opacity: 0.7; }
  50% { transform: scale(1.2); opacity: 0.4; }
  100% { transform: scale(1); opacity: 0.7; }
}

@keyframes float-1 {
  0%, 100% { transform: translateY(0) translateX(0); }
  25% { transform: translateY(-10px) translateX(5px); }
  50% { transform: translateY(0) translateX(10px); }
  75% { transform: translateY(10px) translateX(5px); }
}

@keyframes float-2 {
  0%, 100% { transform: translateY(0) translateX(0); }
  25% { transform: translateY(10px) translateX(-5px); }
  50% { transform: translateY(0) translateX(-10px); }
  75% { transform: translateY(-10px) translateX(-5px); }
}

@keyframes float-3 {
  0%, 100% { transform: translateY(0) rotate(0); }
  50% { transform: translateY(-15px) rotate(5deg); }
}

@keyframes typewriter {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blinkCursor {
  from, to { border-color: transparent; }
  50% { border-color: var(--color-fg-primary); }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes shine {
  from { transform: translateX(-100%) skewX(-15deg); }
  to { transform: translateX(200%) skewX(-15deg); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fade-out {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slide-in-top {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slide-in-bottom {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slide-in-left {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slide-in-right {
  from { transform: translateX(20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes scale-in {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes scale-out {
  from { transform: scale(1); opacity: 1; }
  to { transform: scale(0.9); opacity: 0; }
}

@keyframes rotate-360 {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
