/**
 * AR-System 统一变量系统
 * 整合所有主题变量，支持深浅主题无缝切换
 * 最后更新: 2025-06-16
 */

:root {
  /* ===== 核心颜色系统 ===== */

  /* 背景色系统 - 浅色主题默认值 */
  --color-bg-primary: #F9FBFD;
  --color-bg-surface: rgba(255, 255, 255, 0.8);
  --color-bg-elevated: #FFFFFF;
  --color-bg-muted: #F1F5F9;
  --color-bg-subtle: #F8FAFC;
  --color-bg-hover: rgba(0, 0, 0, 0.05);

  /* 前景色系统 */
  --color-fg-primary: #1f2937;
  --color-fg-secondary: #4b5563;
  --color-fg-muted: #64748b;
  --color-fg-subtle: #94a3b8;
  --color-fg-accent: #3A8DFF;
  --color-fg-highlight: #7A5FFF;

  /* 边框色系统 */
  --color-border-default: #e2e8f0;
  --color-border-subtle: #f1f5f9;
  --color-border-emphasis: #cbd5e1;

  /* 状态色系统 */
  --color-state-success: #10b981;
  --color-state-warning: #f59e0b;
  --color-state-error: #ef4444;
  --color-state-info: #3b82f6;

  /* 量子主题色系统 */
  --color-quantum-default: #3A8DFF;
  --color-quantum-light: #A993FF;
  --color-quantum-dark: #00FFD1;

  /* 图标色系统 */
  --color-icon-primary: #4A5568;
  --color-icon-hover: #2C3E50;

  /* RGB值变量 - 用于rgba()函数 */
  --color-bg-primary-rgb: 249, 251, 253;
  --color-bg-surface-rgb: 255, 255, 255;
  --color-fg-primary-rgb: 31, 41, 55;
  --color-border-rgb: 226, 232, 240;
  --color-quantum-default-rgb: 58, 141, 255;
  --color-quantum-light-rgb: 169, 147, 255;
  --color-quantum-dark-rgb: 0, 255, 209;

  /* 粒子效果变量 */
  --color-particle-quantum-trail: rgba(58, 141, 255, 0.6);
  --color-particle-glow-intensity: 0.8;
  --color-particle-trail-opacity: 0.4;

  /* ===== 统一别名系统 ===== */
  /* 为了向后兼容和简化使用，提供统一的别名 */

  /* 主色调别名 */
  --color-primary: var(--color-quantum-default);
  --color-accent: var(--color-quantum-light);
  --color-secondary: var(--color-quantum-dark);

  /* 背景色别名 */
  --bg-primary: var(--color-bg-primary);
  --bg-surface: var(--color-bg-surface);
  --bg-elevated: var(--color-bg-elevated);
  --bg-muted: var(--color-bg-muted);
  --bg-subtle: var(--color-bg-subtle);
  --bg-hover: var(--color-bg-hover);

  /* 前景色别名 */
  --fg-primary: var(--color-fg-primary);
  --fg-secondary: var(--color-fg-secondary);
  --fg-muted: var(--color-fg-muted);
  --fg-subtle: var(--color-fg-subtle);
  --fg-accent: var(--color-fg-accent);
  --fg-highlight: var(--color-fg-highlight);

  /* 边框色别名 */
  --border-default: var(--color-border-default);
  --border-subtle: var(--color-border-subtle);
  --border-emphasis: var(--color-border-emphasis);

  /* 状态色别名 */
  --color-success: var(--color-state-success);
  --color-warning: var(--color-state-warning);
  --color-error: var(--color-state-error);
  --color-info: var(--color-state-info);

  /* 量子色别名 */
  --quantum: var(--color-quantum-default);
  --quantum-light: var(--color-quantum-light);
  --quantum-dark: var(--color-quantum-dark);
  
  /* ===== 设计系统 ===== */

  /* 字体系统 */
  --font-family-sans: 'Inter', 'Noto Sans SC', sans-serif;
  --font-family-mono: 'JetBrains Mono', monospace;
  --font-family-base: var(--font-family-sans);

  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-md: 18px;
  --font-size-lg: 20px;
  --font-size-xl: 24px;
  --font-size-2xl: 30px;
  --font-size-3xl: 36px;

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;

  /* 间距系统 */
  --space-xxs: 0.125rem;
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;

  /* 圆角系统 */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 1rem;
  --radius-full: 9999px;

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-primary: 0 0 15px rgba(58, 141, 255, 0.3);
  --shadow-accent: 0 0 10px rgba(169, 147, 255, 0.3);
  --shadow-subtle: var(--shadow-md);

  /* 深度阴影系统 */
  --depth-shadow-sm: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  --depth-shadow-md: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
  --depth-shadow-lg: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
  --depth-shadow-xl: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);

  /* 过渡系统 */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 400ms cubic-bezier(0.4, 0, 0.2, 1);

  /* 动画缓动函数 */
  --ease-default: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  /* 布局系统 */
  --layout-header-height: 4rem;
  --layout-footer-height: 12rem;
  --layout-max-width: 1440px;
  --layout-sidebar-width: 280px;

  /* 动效系统 */
  --animation-hover-scale: scale(1.05);
  --animation-active-scale: scale(0.98);
  --animation-transition-duration: 0.3s;
  --animation-transition-timing: cubic-bezier(0.4, 0, 0.2, 1);

  /* ===== 量子组件系统 ===== */

  /* Quantum组件通用变量 */
  --quantum-primary: var(--color-quantum-default);
  --quantum-primary-hover: var(--color-quantum-light);
  --quantum-primary-active: var(--color-quantum-dark);
  --quantum-secondary: var(--color-fg-secondary);
  --quantum-secondary-hover: var(--color-fg-primary);
  --quantum-secondary-active: var(--color-fg-muted);
  --quantum-accent: var(--color-quantum-light);
  --quantum-accent-hover: var(--color-quantum-default);
  --quantum-accent-active: var(--color-quantum-dark);
  --quantum-text: var(--color-fg-primary);
  --quantum-border: var(--color-border-default);
  --quantum-bg-subtle: var(--color-bg-subtle);
  --quantum-bg-subtle-active: var(--color-bg-muted);

  /* QuantumInfoCard 专用变量 */
  --quantum-infocard-bg: var(--color-bg-surface);
  --quantum-infocard-text: var(--color-fg-primary);
  --quantum-infocard-font-xs: var(--font-size-sm);
  --quantum-infocard-font-sm: var(--font-size-base);
  --quantum-infocard-font-md: var(--font-size-md);
  --quantum-infocard-font-lg: var(--font-size-lg);
  --quantum-infocard-font-xl: 24px;
  --quantum-infocard-padding-xs: var(--space-sm);
  --quantum-infocard-padding-sm: var(--space-md);
  --quantum-infocard-padding-md: var(--space-lg);
  --quantum-infocard-padding-lg: var(--space-xl);
  --quantum-infocard-padding-xl: var(--space-2xl);

  /* QuantumButton 专用变量 */
  --quantum-button-bg: var(--color-quantum-default);
  --quantum-button-bg-hover: var(--color-quantum-light);
  --quantum-button-text: #ffffff;
  --quantum-button-border: var(--color-quantum-default);

  /* 玻璃态效果变量 */
  --quantum-bg-rgb: var(--color-bg-surface-rgb);
  --quantum-border-rgb: var(--color-border-rgb);

  /* 粒子系统变量 */
  --quantum-particle-size-min: 2px;
  --quantum-particle-size-max: 6px;
  --quantum-particle-color: var(--color-quantum-default);
  --quantum-particle-glow: var(--color-quantum-light);
  --quantum-animation-duration: 2s;
}

}

/* ===== 深色主题覆盖 ===== */
[data-theme="dark"] {
  /* 背景色系统 - 深色主题 */
  --color-bg-primary: #0D1117;
  --color-bg-surface: rgba(255, 255, 255, 0.05);
  --color-bg-elevated: #1a202c;
  --color-bg-muted: #1e293b;
  --color-bg-subtle: #0f172a;
  --color-bg-hover: rgba(255, 255, 255, 0.1);

  /* 前景色系统 - 深色主题 */
  --color-fg-primary: #ffffff;
  --color-fg-secondary: #d1d5db;
  --color-fg-muted: #9ca3af;
  --color-fg-subtle: #6b7280;
  --color-fg-accent: #58A6FF;
  --color-fg-highlight: #00FFD1;

  /* 边框色系统 - 深色主题 */
  --color-border-default: #2d3748;
  --color-border-subtle: #1a202c;
  --color-border-emphasis: #4a5568;

  /* 图标色系统 - 深色主题 */
  --color-icon-primary: #A0AEC0;
  --color-icon-hover: #CBD5E0;

  /* 状态色系统 - 深色主题 */
  --color-state-success: #22c55e;
  --color-state-warning: #fbbf24;
  --color-state-error: #f87171;
  --color-state-info: #60a5fa;

  /* 量子主题色系统 - 深色主题 */
  --color-quantum-default: #58A6FF;
  --color-quantum-light: #00FFD1;
  --color-quantum-dark: #3A8DFF;

  /* 粒子效果变量 - 深色主题 */
  --color-particle-quantum-trail: rgba(88, 166, 255, 0.6);
  --color-particle-glow-intensity: 1;
  --color-particle-trail-opacity: 0.5;
  --quantum-particle-color: var(--color-quantum-default);
  --quantum-particle-glow: var(--color-quantum-light);

  /* RGB变量 - 深色主题 */
  --color-bg-primary-rgb: 13, 17, 23;
  --color-bg-surface-rgb: 255, 255, 255;
  --color-fg-primary-rgb: 255, 255, 255;
  --color-border-rgb: 45, 55, 72;
  --color-quantum-default-rgb: 88, 166, 255;
  --color-quantum-light-rgb: 0, 255, 209;
  --color-quantum-dark-rgb: 58, 141, 255;

  /* 阴影系统 - 深色主题 */
  --shadow-primary: 0 0 25px var(--color-quantum-default);
  --shadow-accent: 0 0 20px var(--color-quantum-light);

  /* Quantum组件 - 深色主题 */
  --quantum-primary-dark: var(--color-quantum-light);
  --quantum-primary-hover-dark: var(--color-quantum-default);
  --quantum-primary-active-dark: var(--color-quantum-dark);
  --quantum-secondary-dark: var(--color-fg-secondary);
  --quantum-secondary-hover-dark: var(--color-fg-primary);
  --quantum-secondary-active-dark: var(--color-fg-muted);
  --quantum-text-dark: var(--color-fg-primary);
  --quantum-border-dark: var(--color-border-emphasis);
  --quantum-bg-subtle-dark: var(--color-bg-surface);
  --quantum-bg-subtle-active-dark: var(--color-bg-elevated)
}

}

/* ===== 主题变体系统 ===== */
[data-theme-variant="quantum"] {
  --color-fg-accent: var(--color-quantum-default);
  --color-fg-highlight: var(--color-quantum-light);
  --color-border-emphasis: var(--color-quantum-dark);
  --color-primary: var(--color-quantum-default);
  --color-accent: var(--color-quantum-light);
  --color-secondary: var(--color-quantum-dark);
}

/* ===== 主题过渡动画 ===== */
.theme-transitioning * {
  transition:
    background-color var(--transition-normal),
    border-color var(--transition-normal),
    color var(--transition-normal),
    box-shadow var(--transition-normal),
    opacity var(--transition-normal) !important;
}
