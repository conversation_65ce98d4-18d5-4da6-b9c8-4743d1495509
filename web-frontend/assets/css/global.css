/* AR-System 全局样式 */
/* 所有变量已统一到 variables.css，此文件仅包含全局样式类 */

/* UnoCSS 兼容性变量 */
:root {
  --un-border-style: solid;
  --un-border-width: 1px;
  --un-border-opacity: 1;
}

/* 基础样式 */
html, body {
  @apply bg-[var(--color-bg-primary)] text-[var(--color-fg-primary)];
  transition: background-color var(--animation-transition-duration), color var(--animation-transition-duration);
}

/* 链接样式 */
a {
  @apply text-[var(--color-fg-accent)] hover:underline transition-colors duration-200;
  font-family: var(--font-family-sans);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-[var(--color-bg-surface)];
}

::-webkit-scrollbar-thumb {
  @apply bg-[var(--color-fg-accent)]/30 rounded-full hover:bg-[var(--color-fg-accent)]/50 transition-colors;
}

/* 暗色模式滚动条 */
[data-theme="dark"] ::-webkit-scrollbar-thumb {
  @apply bg-[var(--color-fg-accent)]/50 hover:bg-[var(--color-fg-accent)]/70;
}

/* 容器 */
.container {
  @apply mx-auto px-4 w-full;
  max-width: 1440px;
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
  @apply font-bold text-balance leading-tight tracking-tight;
  font-feature-settings: 'pnum' on, 'lnum' on, 'ss01' on, 'ss02' on, 'case' on;
  font-family: var(--font-family-sans);
  font-weight: var(--font-weight-bold);
}

h1 { @apply text-4xl md:text-5xl lg:text-6xl; }
h2 { @apply text-3xl md:text-4xl lg:text-5xl; }
h3 { @apply text-2xl md:text-3xl lg:text-4xl; }
h4 { @apply text-xl md:text-2xl lg:text-3xl; }
h5 { @apply text-lg md:text-xl lg:text-2xl; }
h6 { @apply text-base md:text-lg lg:text-xl; }

/* 段落 */
p {
  @apply text-base leading-relaxed text-[var(--color-fg-primary)]/90;
  max-width: 65ch;
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
}

/* 按钮基础样式 */
.btn {
  @apply inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  @apply h-10 px-4 py-2;
  @apply bg-[var(--color-fg-accent)] text-[var(--color-bg-primary)] hover:opacity-90;
  @apply focus-visible:ring-[var(--color-fg-accent)]/50;
  font-family: var(--font-family-sans);
  font-weight: var(--font-weight-medium);
}

/* 卡片样式 */
.card {
  @apply rounded-2xl border bg-[var(--color-bg-surface)] text-[var(--color-fg-primary)] shadow-sm transition-all hover:shadow-md;
  @apply border-[var(--color-border-default)]/50 hover:border-[var(--color-fg-accent)]/30;
  @apply backdrop-blur-sm;
  background: linear-gradient(
    135deg,
    rgba(var(--color-quantum-default-rgb), 0.05) 0%,
    rgba(var(--color-quantum-light-rgb), 0.02) 100%
  );
  box-shadow:
    0 0 20px rgba(var(--color-quantum-default-rgb), 0.1),
    inset 0 0 20px rgba(var(--color-quantum-default-rgb), 0.05);
  font-family: var(--font-family-sans);
}

/* 输入框样式 */
.input {
  @apply flex h-10 w-full rounded-lg border border-[var(--color-border-default)] bg-[var(--color-bg-subtle)] px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-[var(--color-fg-muted)] focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[var(--color-fg-accent)] focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 暗色模式标识 */
.dark {
  color-scheme: dark;
}

/* 打印样式 */
@media print {
  body {
    background: white;
    color: black;
  }

  .no-print {
    display: none !important;
  }
}
