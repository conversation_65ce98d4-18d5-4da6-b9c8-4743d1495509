/* 动画效果系统 */
/* 变量已迁移到 variables.css，此文件仅包含动画类和关键帧 */

/* 基础过渡效果 */
.transition-all {
  transition-property: all;
  transition-timing-function: var(--transition-normal);
  transition-duration: var(--animation-duration-normal);
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: var(--transition-normal);
  transition-duration: var(--animation-duration-normal);
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: var(--transition-normal);
  transition-duration: var(--animation-duration-normal);
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: var(--transition-normal);
  transition-duration: var(--animation-duration-normal);
}

/* 闪烁光标动画 */
@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.animate-blink {
  animation: blink 1s step-end infinite;
}

/* 慢速弹跳动画 */
@keyframes bounce-slow {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.animate-bounce-slow {
  animation: bounce-slow 2s ease-in-out infinite;
}

/* 慢速脉冲动画 */
@keyframes ping-slow {
  0% { transform: scale(1); opacity: 0.7; }
  50% { transform: scale(1.2); opacity: 0.4; }
  100% { transform: scale(1); opacity: 0.7; }
}

.animate-ping-slow {
  animation: ping-slow 3s cubic-bezier(0, 0, 0.2, 1) infinite;
}

/* 浮动动画变体 */
@keyframes float-1 {
  0%, 100% { transform: translateY(0) translateX(0); }
  25% { transform: translateY(-10px) translateX(5px); }
  50% { transform: translateY(0) translateX(10px); }
  75% { transform: translateY(10px) translateX(5px); }
}

@keyframes float-2 {
  0%, 100% { transform: translateY(0) translateX(0); }
  25% { transform: translateY(10px) translateX(-5px); }
  50% { transform: translateY(0) translateX(-10px); }
  75% { transform: translateY(-10px) translateX(-5px); }
}

@keyframes float-3 {
  0%, 100% { transform: translateY(0) rotate(0); }
  50% { transform: translateY(-15px) rotate(5deg); }
}

.animate-float-1 {
  animation: float-1 15s ease-in-out infinite;
}

.animate-float-2 {
  animation: float-2 18s ease-in-out infinite;
}

.animate-float-3 {
  animation: float-3 12s ease-in-out infinite;
}

/* 打字机效果 */
@keyframes typewriter {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blinkCursor {
  from, to { border-color: transparent; }
  50% { border-color: var(--color-text); }
}

.animate-typewriter {
  overflow: hidden;
  white-space: nowrap;
  display: inline-block;
  position: relative;
  animation: typewriter 2s steps(40) 1s forwards;
  width: 0;
}

.animate-typewriter::after {
  content: '';
  position: absolute;
  right: -4px;
  top: 0;
  bottom: 0;
  border-right: 2px solid var(--color-text);
  animation: blinkCursor 0.8s steps(2) infinite;
}

/* 渐变背景动画 */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background: linear-gradient(-45deg, var(--color-primary), var(--color-secondary), var(--color-accent), var(--color-primary));
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
}

/* 闪光效果 */
@keyframes shine {
  from {
    transform: translateX(-100%) skewX(-15deg);
  }
  to {
    transform: translateX(200%) skewX(-15deg);
  }
}

.animate-shine {
  position: relative;
  overflow: hidden;
}

.animate-shine::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-15deg);
  animation: shine 3s infinite;
}

/* 摇晃动画 */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.animate-shake {
  animation: shake 0.8s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

/* 淡入淡出动画 */
@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fade-out {
  from { opacity: 1; }
  to { opacity: 0; }
}

.animate-fade-in {
  animation: fade-in var(--animation-duration-normal) var(--transition-normal) forwards;
}

.animate-fade-out {
  animation: fade-out var(--animation-duration-normal) var(--transition-normal) forwards;
}

/* 滑入动画 */
@keyframes slide-in-top {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slide-in-bottom {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slide-in-left {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slide-in-right {
  from { transform: translateX(20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

.animate-slide-in-top {
  animation: slide-in-top var(--animation-duration-normal) var(--transition-normal) forwards;
}

.animate-slide-in-bottom {
  animation: slide-in-bottom var(--animation-duration-normal) var(--transition-normal) forwards;
}

.animate-slide-in-left {
  animation: slide-in-left var(--animation-duration-normal) var(--transition-normal) forwards;
}

.animate-slide-in-right {
  animation: slide-in-right var(--animation-duration-normal) var(--transition-normal) forwards;
}

/* 缩放动画 */
@keyframes scale-in {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes scale-out {
  from { transform: scale(1); opacity: 1; }
  to { transform: scale(0.9); opacity: 0; }
}

.animate-scale-in {
  animation: scale-in var(--animation-duration-normal) var(--transition-bounce) forwards;
}

.animate-scale-out {
  animation: scale-out var(--animation-duration-normal) var(--transition-normal) forwards;
}

/* 旋转动画 */
@keyframes rotate-360 {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-rotate {
  animation: rotate-360 2s linear infinite;
}

/* 悬停效果类 */
.hover-lift {
  transition: transform var(--animation-duration-normal) var(--transition-bounce);
}

.hover-lift:hover {
  transform: translateY(-5px);
}

.hover-scale {
  transition: transform var(--animation-duration-normal) var(--transition-bounce);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow var(--animation-duration-normal) var(--transition-normal);
}

.hover-glow:hover {
  box-shadow: 0 0 15px rgba(var(--color-primary-rgb), 0.5);
}

/* 聚焦效果 */
.focus-outline {
  transition: box-shadow var(--animation-duration-fast) var(--transition-normal);
}

.focus-outline:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.4);
}

/* 页面过渡动画 */
.page-enter-active,
.page-leave-active {
  transition: opacity var(--animation-duration-normal) var(--transition-normal);
}

.page-enter-from,
.page-leave-to {
  opacity: 0;
}
