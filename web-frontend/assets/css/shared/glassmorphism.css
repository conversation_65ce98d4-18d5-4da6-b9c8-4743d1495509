/* glassmorphism.css - AR-System 毛玻璃效果样式库 */
/* 变量已迁移到 variables.css，此文件仅包含样式类 */

.glass-effect {
  background: var(--glass-bg-light);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border: 1px solid var(--glass-border-light);
  box-shadow: var(--glass-shadow);
}

.dark .glass-effect {
  background: var(--glass-bg-dark);
  border: 1px solid var(--glass-border-dark);
}

/* 毛玻璃变体 */
.glass-effect--heavy {
  backdrop-filter: blur(var(--glass-blur-heavy));
  -webkit-backdrop-filter: blur(var(--glass-blur-heavy));
  background: var(--glass-bg-light-heavy);
}

.dark .glass-effect--heavy {
  background: var(--glass-bg-dark-heavy);
}

.glass-effect--light {
  backdrop-filter: blur(var(--glass-blur-light));
  -webkit-backdrop-filter: blur(var(--glass-blur-light));
  background: var(--glass-bg-light-light);
}

.dark .glass-effect--light {
  background: var(--glass-bg-dark-light);
}
