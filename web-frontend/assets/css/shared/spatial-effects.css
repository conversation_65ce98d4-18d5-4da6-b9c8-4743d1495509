/* spatial-effects.css - AR-System 空间深度样式库 */
/* 变量已迁移到 variables.css，此文件仅包含样式类 */

.spatial-depth {
  box-shadow: var(--depth-shadow-md);
  transition: var(--depth-transition);
  transform-style: preserve-3d;
  perspective: 1000px;
}

.spatial-depth:hover {
  box-shadow: var(--depth-shadow-lg);
  transform: var(--depth-translate-hover);
}

/* 深度变体 */
.spatial-depth--sm { box-shadow: var(--depth-shadow-sm); }
.spatial-depth--md { box-shadow: var(--depth-shadow-md); }
.spatial-depth--lg { box-shadow: var(--depth-shadow-lg); }
.spatial-depth--xl { box-shadow: var(--depth-shadow-xl); }
.spatial-depth--2xl { box-shadow: var(--depth-shadow-2xl); }

/* 浮动效果 */
.spatial-float {
  animation: spatialFloat 6s ease-in-out infinite;
}

@keyframes spatialFloat {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}
