/* quantum-effects.css - AR-System 量子效果样式库 */
/* 使用统一变量系统，所有变量已定义在 variables.css 中 */

.quantum-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
  pointer-events: none;
}

.quantum-particle {
  position: absolute;
  border-radius: 50%;
  background-color: var(--quantum-particle-color);
  box-shadow: 0 0 8px var(--quantum-particle-glow);
  opacity: 0.5;
  transform-origin: center center;
  animation: quantumParticle var(--quantum-animation-duration) infinite;
}

@keyframes quantumParticle {
  0% {
    transform: translate(0, 0) scale(1) rotate(0deg);
    opacity: 0;
  }
  25% {
    opacity: var(--quantum-particle-opacity, 0.5);
  }
  75% {
    opacity: var(--quantum-particle-opacity, 0.5);
  }
  100% {
    transform: translate(
      calc(var(--random-x, 1) * 100px), 
      calc(var(--random-y, 1) * 100px)
    ) scale(0) rotate(360deg);
    opacity: 0;
  }
}

.quantum-pulsing {
  animation: quantumPulse 2s infinite;
}

@keyframes quantumPulse {
  0% {
    box-shadow: 0 0 0 0 var(--color-particle-quantum-trail);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(var(--color-quantum-default-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--color-quantum-default-rgb), 0);
  }
}

/* QuantumInfoCard 专用动画 */
@keyframes quantumInfoCardFloat {
  0%, 100% {
    transform: translate(-50%, -50%) translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  25% {
    transform: translate(-50%, -50%) translateY(-10px) rotate(90deg);
    opacity: 0.6;
  }
  50% {
    transform: translate(-50%, -50%) translateY(-5px) rotate(180deg);
    opacity: 0.8;
  }
  75% {
    transform: translate(-50%, -50%) translateY(-15px) rotate(270deg);
    opacity: 0.4;
  }
}

/* 量子发光效果 */
@keyframes quantumGlow {
  0%, 100% {
    text-shadow: 0 0 5px var(--color-quantum-default), 0 0 10px var(--color-quantum-default), 0 0 15px var(--color-quantum-default);
  }
  50% {
    text-shadow: 0 0 10px var(--color-quantum-light), 0 0 20px var(--color-quantum-light), 0 0 30px var(--color-quantum-light);
  }
}
