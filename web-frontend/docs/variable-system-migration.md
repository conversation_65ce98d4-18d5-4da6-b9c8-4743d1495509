# AR-System 变量系统迁移指南

## 📋 概述

为了解决全局变量混乱和主题切换复杂性问题，我们已将所有CSS变量整合到统一的变量系统中。

## 🔄 变更内容

### **合并的文件**
- ✅ `variables.css` - 主要变量文件（已更新）
- ⚠️ `theme-mapping.css` - 已废弃，变量已迁移
- ✅ `quantum-effects.css` - 已更新使用统一变量

### **新的变量结构**

#### **1. 核心颜色系统**
```css
/* 背景色系统 */
--color-bg-primary: #F9FBFD;
--color-bg-surface: rgba(255, 255, 255, 0.8);
--color-bg-elevated: #FFFFFF;
--color-bg-muted: #F1F5F9;
--color-bg-subtle: #F8FAFC;
--color-bg-hover: rgba(0, 0, 0, 0.05);

/* 前景色系统 */
--color-fg-primary: #1f2937;
--color-fg-secondary: #4b5563;
--color-fg-muted: #64748b;
--color-fg-subtle: #94a3b8;
--color-fg-accent: #3A8DFF;
--color-fg-highlight: #7A5FFF;

/* 量子主题色系统 */
--color-quantum-default: #3A8DFF;
--color-quantum-light: #A993FF;
--color-quantum-dark: #00FFD1;
```

#### **2. 统一别名系统**
```css
/* 主色调别名 */
--color-primary: var(--color-quantum-default);
--color-accent: var(--color-quantum-light);
--color-secondary: var(--color-quantum-dark);

/* 背景色别名 */
--bg-primary: var(--color-bg-primary);
--bg-surface: var(--color-bg-surface);
--bg-elevated: var(--color-bg-elevated);

/* 前景色别名 */
--fg-primary: var(--color-fg-primary);
--fg-secondary: var(--color-fg-secondary);
--fg-muted: var(--color-fg-muted);
```

#### **3. 量子组件系统**
```css
/* Quantum组件通用变量 */
--quantum-primary: var(--color-quantum-default);
--quantum-primary-hover: var(--color-quantum-light);
--quantum-primary-active: var(--color-quantum-dark);
--quantum-text: var(--color-fg-primary);
--quantum-border: var(--color-border-default);
```

## 🚀 迁移步骤

### **步骤 1: 检查组件使用**
搜索项目中使用旧变量的地方：
```bash
# 搜索可能需要更新的变量引用
grep -r "quantum-particle-color-light" components/
grep -r "quantum-particle-color-dark" components/
```

### **步骤 2: 更新组件引用**
将旧的变量名替换为新的统一变量：

**旧的写法：**
```css
.my-component {
  background: var(--quantum-particle-color-light);
  color: var(--quantum-text-dark);
}
```

**新的写法：**
```css
.my-component {
  background: var(--quantum-particle-color);
  color: var(--quantum-text);
}
```

### **步骤 3: 验证主题切换**
确保组件在深浅主题间正确切换：
```css
/* 推荐：使用统一变量，自动适配主题 */
.quantum-button {
  background: var(--quantum-primary);
  color: var(--quantum-text);
  border: 1px solid var(--quantum-border);
}

/* 避免：硬编码颜色值 */
.quantum-button {
  background: #3A8DFF; /* ❌ 不会响应主题切换 */
}
```

## 📝 变量映射表

| 旧变量名 | 新变量名 | 说明 |
|---------|---------|------|
| `--quantum-particle-color-light` | `--quantum-particle-color` | 粒子颜色（自动适配主题） |
| `--quantum-particle-color-dark` | `--quantum-particle-color` | 粒子颜色（自动适配主题） |
| `--quantum-particle-glow-light` | `--quantum-particle-glow` | 粒子发光效果 |
| `--quantum-particle-glow-dark` | `--quantum-particle-glow` | 粒子发光效果 |
| `--quantum-primary-dark` | `--quantum-primary` | 主色调（深色主题自动覆盖） |
| `--quantum-text-dark` | `--quantum-text` | 文字颜色（深色主题自动覆盖） |

## ⚠️ 注意事项

### **1. 主题切换机制**
- 所有变量都支持深浅主题自动切换
- 深色主题通过 `[data-theme="dark"]` 选择器覆盖变量值
- 不需要手动处理 `-dark` 后缀的变量

### **2. 向后兼容性**
- `theme-mapping.css` 文件已标记为废弃但仍保留
- 建议逐步迁移到新的变量系统
- 旧变量在短期内仍可使用，但建议尽快更新

### **3. 性能优化**
- 统一变量系统减少了CSS文件大小
- 主题切换性能得到提升
- 减少了变量查找的复杂度

## 🔧 开发建议

### **1. 使用统一变量**
```css
/* ✅ 推荐：使用统一的核心变量 */
.component {
  background: var(--color-bg-surface);
  color: var(--color-fg-primary);
  border: 1px solid var(--color-border-default);
}

/* ✅ 推荐：使用简化别名 */
.component {
  background: var(--bg-surface);
  color: var(--fg-primary);
  border: 1px solid var(--border-default);
}
```

### **2. 量子组件开发**
```css
/* ✅ 推荐：使用量子组件变量 */
.quantum-card {
  background: var(--quantum-infocard-bg);
  color: var(--quantum-infocard-text);
  padding: var(--quantum-infocard-padding-md);
}
```

### **3. 主题适配测试**
在开发过程中，确保测试两种主题：
```javascript
// 测试主题切换
const themeStore = useThemeStore()
themeStore.setTheme('dark')  // 测试深色主题
themeStore.setTheme('light') // 测试浅色主题
```

## 📚 相关文档

- [主题系统完整文档](./theme-system-complete.md)
- [组件开发指南](./component-development-guide.md)
- [变量系统源码](../assets/css/variables.css)

## 🆘 问题排查

如果遇到主题切换问题：

1. **检查变量引用**：确保使用的是统一变量系统中的变量
2. **验证CSS加载顺序**：确保 `variables.css` 在其他样式文件之前加载
3. **检查选择器优先级**：避免使用过高优先级的选择器覆盖主题变量
4. **浏览器开发者工具**：检查计算后的样式值是否正确

---

**更新日期**: 2025-06-16  
**版本**: 1.0.0  
**维护者**: AR-System 开发团队
