<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, defineExpose } from 'vue'
import { useThemeStore } from '../../stores/theme'
import { useAuthStore } from '../../stores/authStore'

const authStore = useAuthStore()
const isLoggedIn = computed(() => authStore.isLoggedIn)
const currentUser = computed(() => authStore.currentUser)
const userProfileLinkTarget = computed(() => isLoggedIn.value ? '/user/profile' : '/auth/login')
const isAdmin = computed(() => {
  const user = authStore.currentUser
  const result = user && (
    user.role?.name === 'admin' ||
    user.role?.name === 'developer' ||
    user.is_superuser
  )

  // 🔍 调试日志
  console.log('🔍 NavBar isAdmin 检查:', {
    user: user ? {
      username: user.username,
      is_superuser: user.is_superuser,
      role: user.role
    } : null,
    isAdmin: result
  })

  return result
})

// 定义导航项类型
interface NavItem {
  title: string;
  path: string;
}

// 导航项 - 根据交互框架设计构建（logo作为主页链接，不需要单独的首页链接）
const navItems = ref<NavItem[]>([
  { title: '技术架构', path: '/architecture' },
  { title: '硬件原型', path: '/hardware' },
  { title: '开发文档', path: '/docs' },
  { title: '开源项目', path: '/opensource' },
  { title: '社区生态', path: '/community' },
  { title: '量子商城', path: '/ecommerce' }
])

// 主题设置
const themeStore = useThemeStore();
const isDark = computed(() => themeStore.theme === 'dark');
const themeVariant = computed(() => themeStore.themeVariant);
const isPC = ref(true)

// 状态
const mobileMenuOpen = ref(false)
const userMenuOpen = ref(false)
const isTransparent = ref(false)

// 语言切换
const currentLanguage = ref('zh')

const toggleLanguage = () => {
  currentLanguage.value = currentLanguage.value === 'zh' ? 'en' : 'zh'
  // 这里可以添加实际的语言切换逻辑
  console.log('Language switched to:', currentLanguage.value)
}

// 初始化主题
onMounted(() => {
  if (typeof window !== 'undefined') {
    themeStore.initTheme()
  }
})

const navContainer = ref<HTMLElement | null>(null)
const shouldCollapseNav = ref(false)

function checkCollapseCondition(): void {
  if (typeof window === 'undefined' || !navContainer.value) return
  
  // 在小屏幕上始终折叠导航
  if (window.innerWidth < 768) {
    shouldCollapseNav.value = true
    return
  }
  
  const availableWidth = window.innerWidth
  const navLinksEl = navContainer.value.querySelector('.nav-links') as HTMLElement | null
  if (!navLinksEl) {
    shouldCollapseNav.value = false
    return
  }
  const navLinksWidth = navLinksEl.scrollWidth
  const containerPadding = 32 // px, approximate padding (px-4 * 2)
  const logoWidth = (navContainer.value.querySelector('.logo-container') as HTMLElement)?.scrollWidth || 0
  const reservedSpace = 120 // px, for buttons and margins
  shouldCollapseNav.value = navLinksWidth + logoWidth + containerPadding + reservedSpace > availableWidth
}

// 路由
const route = useRoute()

// 响应式断点检测
function updateIsPC(): void {
  isPC.value = window.innerWidth >= 768
  if (isPC.value) {
    closeMobileMenu()
  }
}

// 切换移动菜单
function toggleMobileMenu(): void {
  mobileMenuOpen.value = !mobileMenuOpen.value
  if (typeof window !== 'undefined') {
    document.body.style.overflow = mobileMenuOpen.value ? 'hidden' : ''
  }
}

function closeMobileMenu(): void {
  mobileMenuOpen.value = false
  if (typeof window !== 'undefined') {
    document.body.style.overflow = ''
  }
}

// 切换用户菜单
function toggleUserMenu(): void {
  userMenuOpen.value = !userMenuOpen.value
}

function closeUserMenu(): void {
  userMenuOpen.value = false
}

function handleScroll(): void {
  if (typeof window === 'undefined') return
  isTransparent.value = window.scrollY < 50
}

function handleClickOutside(event: MouseEvent): void {
  if (mobileMenuOpen.value && !(event.target as Element).closest('.navbar')) {
    closeMobileMenu()
  }
  if (userMenuOpen.value && !(event.target as Element).closest('.user-menu-trigger') && !(event.target as Element).closest('.user-dropdown')) {
    closeUserMenu()
  }
}

function toggleTheme(): void {
  if (typeof window === 'undefined') return
  themeStore.toggleTheme()
}



onMounted(() => {
  if (typeof window !== 'undefined') {
    window.addEventListener('scroll', handleScroll)
    window.addEventListener('resize', updateIsPC)
    window.addEventListener('click', handleClickOutside)
    updateIsPC()
    handleScroll()
    checkCollapseCondition()
    window.addEventListener('resize', checkCollapseCondition)
  }
})

onBeforeUnmount(() => {
  if (typeof window !== 'undefined') {
    window.removeEventListener('scroll', handleScroll)
    window.removeEventListener('resize', updateIsPC)
    window.removeEventListener('click', handleClickOutside)
    window.removeEventListener('resize', checkCollapseCondition)
    document.body.style.overflow = ''
  }
})

</script>

<template>
  <div
    ref="navContainer"
    class="navbar fixed top-0 left-0 right-0 z-50 transition-all duration-300"
    :class="{
      'bg-[var(--color-bg-surface)] border-b border-[var(--color-border-subtle)] shadow-sm': !isTransparent,
      'bg-transparent': isTransparent
    }"
  >
    <div class="container mx-auto px-4 py-4 flex justify-between items-center">
      <NuxtLink to="/" class="logo-container flex items-center space-x-2 hover:opacity-80 transition-opacity">
        <div class="logo-icon quantum-gradient">
          <i class="i-carbon-3d-mpr-toggle text-white text-lg"></i>
        </div>
        <span class="text-[var(--color-fg-primary)] text-xl font-bold">MIKEPLAY</span>
      </NuxtLink>

            <div class="nav-links hidden md:flex space-x-6" :class="{ 'hidden': shouldCollapseNav }">
                <NuxtLink
          v-for="item in navItems"
          :key="item.path"
          :to="item.path"
          class="text-[var(--color-fg-primary)] hover:text-[var(--color-fg-accent)] transition-colors duration-300"
          :class="{ 'text-[var(--color-fg-accent)]': route.path === item.path }"
        >
          {{ item.title }}
        </NuxtLink>
      </div>

      <div class="flex items-center space-x-2">

        <!-- Hamburger Menu Button (Mobile) -->
        <button
          @click="toggleMobileMenu"
          class="md:hidden p-2 rounded-lg hover:bg-[var(--color-bg-surface)] transition-all duration-300 group"
          aria-label="Toggle menu"
          title="Toggle menu"
        >
          <i v-if="isPC && shouldCollapseNav" class="i-carbon-menu w-7 h-7 text-[var(--color-fg-primary)] group-hover:text-[var(--color-fg-accent)]"></i>
        </button>

        <!-- 语言切换按钮 -->
        <button
          @click="toggleLanguage"
          class="language-toggle-btn p-2 rounded-lg hover:bg-[var(--color-bg-surface)] transition-all duration-300 group"
          :aria-label="currentLanguage === 'zh' ? 'Switch to English' : '切换到中文'"
          :title="currentLanguage === 'zh' ? 'Switch to English' : '切换到中文'"
        >
          <div class="relative w-5 h-5 flex items-center justify-center">
            <i
              class="i-carbon-earth-americas-filled w-7 h-7 text-[var(--color-fg-primary)] group-hover:text-[var(--color-fg-accent)] transition-[var(--transition-fast)] block"
            ></i>
          </div>
        </button>

        <!-- 主题切换按钮 -->
        <button
          @click="themeStore.toggleTheme"
          class="theme-toggle-btn p-2 rounded-lg hover:bg-[var(--color-bg-surface)] transition-all duration-300 group"
          aria-label="切换主题"
          title="切换主题"
        >
          <div class="relative w-5 h-5 flex items-center justify-center">
            <!-- 浅色主题图标 -->
            <i
              class="i-carbon-brightness-contrast absolute transition-all duration-300 w-7 h-7 text-[var(--color-fg-primary)]"
              :class="[
                !themeStore.isDark
                  ? 'opacity-100 rotate-0 scale-100 group-hover:text-[var(--color-fg-accent)]'
                  : 'opacity-0 rotate-180 scale-75'
              ]"
            ></i>
            <!-- 深色主题图标 -->
            <i
              class="i-carbon-moon-new absolute transition-all duration-300 w-7 h-7 text-[var(--color-fg-primary)]"
              :class="[
                themeStore.isDark
                  ? 'opacity-100 rotate-0 scale-100 group-hover:text-[var(--color-fg-accent)]'
                  : 'opacity-0 -rotate-180 scale-75'
              ]"
            ></i>
          </div>
        </button>

        <!-- Dashboard入口图标 (仅管理员显示) -->
        <a
          v-if="isLoggedIn && isAdmin"
          href="http://localhost:3003"
          target="_blank"
          class="dashboard-link p-2 rounded-lg hover:bg-[var(--color-bg-surface)] transition-all duration-300 group"
          aria-label="管理后台"
          title="管理后台"
        >
          <div class="relative w-5 h-5 flex items-center justify-center">
            <i
              class="i-carbon-dashboard w-7 h-7 text-[var(--color-fg-primary)] group-hover:text-[var(--color-fg-accent)] transition-colors duration-300"
            ></i>
          </div>
        </a>

        <!-- 用户下拉菜单 -->
        <div v-if="isLoggedIn" class="relative">
          <button
            @click="toggleUserMenu"
            class="user-menu-trigger flex items-center space-x-2 p-2 rounded-lg hover:bg-[var(--color-bg-surface)] transition-all duration-300 group"
            :class="{ 'bg-[var(--color-bg-surface)]': userMenuOpen }"
            aria-label="用户菜单"
            title="用户菜单"
          >
            <div class="user-avatar w-8 h-8 rounded-full bg-gradient-to-r from-[var(--color-quantum-default)] to-[var(--color-quantum-light)] flex items-center justify-center">
              <i class="i-carbon-user-avatar w-5 h-5 text-white"></i>
            </div>
            <i class="i-carbon-chevron-down w-4 h-4 text-[var(--color-fg-muted)] transition-transform duration-200" :class="{ 'rotate-180': userMenuOpen }"></i>
          </button>

          <!-- 用户下拉菜单 -->
          <Transition name="dropdown">
            <div v-if="userMenuOpen" class="user-dropdown absolute top-full right-0 mt-2 w-56 bg-[var(--color-bg-surface)] border border-[var(--color-border-default)] rounded-[var(--radius-lg)] shadow-[var(--shadow-lg)] backdrop-filter backdrop-blur-lg z-50">
              <div class="py-2">
                <!-- 用户信息 -->
                <div class="px-4 py-3 border-b border-[var(--color-border-subtle)]">
                  <div class="flex items-center space-x-3">
                    <div class="user-avatar w-10 h-10 rounded-full bg-gradient-to-r from-[var(--color-quantum-default)] to-[var(--color-quantum-light)] flex items-center justify-center">
                      <i class="i-carbon-user-avatar w-6 h-6 text-white"></i>
                    </div>
                    <div>
                      <NuxtLink
                        to="/user/profile"
                        @click="closeUserMenu"
                        class="font-[var(--font-weight-medium)] text-[var(--color-fg-primary)] hover:text-[var(--color-fg-accent)] transition-[var(--transition-fast)] cursor-pointer"
                      >
                        {{ currentUser?.displayName || currentUser?.username }}
                      </NuxtLink>
                      <div class="text-[var(--font-size-sm)] text-[var(--color-fg-muted)]">{{ currentUser?.email }}</div>
                    </div>
                  </div>
                </div>

                <!-- 用户功能 -->

                <NuxtLink
                  to="/user"
                  @click="closeUserMenu"
                  class="user-menu-item flex items-center px-4 py-3 text-[var(--color-fg-primary)] hover:bg-[var(--color-bg-elevated)] hover:text-[var(--color-fg-accent)] transition-[var(--transition-fast)]"
                >
                  <i class="i-carbon-dashboard-reference w-5 h-5 mr-3"></i>
                  <span class="font-[var(--font-weight-medium)]">用户中心</span>
                </NuxtLink>

                <!-- Dashboard 链接 - 仅管理员可见 -->
                <a
                  v-if="isAdmin"
                  href="http://localhost:3003"
                  target="_blank"
                  @click="closeUserMenu"
                  class="user-menu-item flex items-center px-4 py-3 text-[var(--color-fg-primary)] hover:bg-[var(--color-bg-elevated)] hover:text-[var(--color-fg-accent)] transition-[var(--transition-fast)]"
                >
                  <i class="i-carbon-dashboard w-5 h-5 mr-3"></i>
                  <span class="font-[var(--font-weight-medium)]">Dashboard</span>
                  <i class="i-carbon-arrow-up-right w-4 h-4 ml-auto text-[var(--color-fg-muted)]"></i>
                </a>

                <!-- 管理功能 (仅管理员显示) -->
                <div v-if="isAdmin">
                  <hr class="border-[var(--color-border-subtle)] my-2">

                  <NuxtLink
                    to="/admin"
                    @click="closeUserMenu"
                    class="user-menu-item flex items-center px-4 py-3 text-[var(--color-fg-primary)] hover:bg-[var(--color-bg-elevated)] hover:text-[var(--color-fg-accent)] transition-[var(--transition-fast)]"
                  >
                    <i class="i-carbon-analytics w-5 h-5 mr-3"></i>
                    <span class="font-[var(--font-weight-medium)]">管理总览</span>
                  </NuxtLink>

                  <NuxtLink
                    to="/admin/analytics"
                    @click="closeUserMenu"
                    class="user-menu-item flex items-center px-4 py-3 text-[var(--color-fg-primary)] hover:bg-[var(--color-bg-elevated)] hover:text-[var(--color-fg-accent)] transition-[var(--transition-fast)]"
                  >
                    <i class="i-carbon-chart-line w-5 h-5 mr-3"></i>
                    <span class="font-[var(--font-weight-medium)]">数据分析</span>
                  </NuxtLink>

                  <NuxtLink
                    to="/admin/users"
                    @click="closeUserMenu"
                    class="user-menu-item flex items-center px-4 py-3 text-[var(--color-fg-primary)] hover:bg-[var(--color-bg-elevated)] hover:text-[var(--color-fg-accent)] transition-[var(--transition-fast)]"
                  >
                    <i class="i-carbon-user-multiple w-5 h-5 mr-3"></i>
                    <span class="font-[var(--font-weight-medium)]">用户管理</span>
                  </NuxtLink>

                  <NuxtLink
                    to="/admin/devices"
                    @click="closeUserMenu"
                    class="user-menu-item flex items-center px-4 py-3 text-[var(--color-fg-primary)] hover:bg-[var(--color-bg-elevated)] hover:text-[var(--color-fg-accent)] transition-[var(--transition-fast)]"
                  >
                    <i class="i-carbon-devices w-5 h-5 mr-3"></i>
                    <span class="font-[var(--font-weight-medium)]">设备管理</span>
                  </NuxtLink>
                </div>

                <!-- 系统功能 -->
                <hr class="border-[var(--color-border-subtle)] my-2">

                <button
                  @click="() => { authStore.logout(); closeUserMenu(); navigateTo('/'); }"
                  class="user-menu-item flex items-center w-full px-4 py-3 text-[var(--color-state-error)] hover:bg-[var(--color-bg-elevated)] transition-[var(--transition-fast)]"
                >
                  <i class="i-carbon-logout w-5 h-5 mr-3"></i>
                  <span class="font-[var(--font-weight-medium)]">退出登录</span>
                </button>
              </div>
            </div>
          </Transition>
        </div>

        <!-- 未登录时的登录按钮 -->
        <NuxtLink
          v-else
          to="/auth/login"
          class="login-link flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-[var(--color-bg-surface)] transition-all duration-300 group"
          aria-label="登录/注册"
          title="登录/注册"
        >
          <i class="i-carbon-login w-5 h-5 text-[var(--color-fg-primary)] group-hover:text-[var(--color-fg-accent)]"></i>
          <span class="text-[var(--color-fg-primary)] group-hover:text-[var(--color-fg-accent)] font-[var(--font-weight-medium)]">登录</span>
        </NuxtLink>



        <!-- @vue-ignore -->
        <button
          @click="toggleMobileMenu"
          class="mobile-menu-btn md:hidden p-2 rounded-full hover:bg-[var(--color-bg-surface)] transition-[var(--transition-fast)] order-last"
          aria-label="切换菜单"
        >
          <i
            :class="mobileMenuOpen ? 'i-carbon-close w-7 h-7' : 'i-carbon-menu w-7 h-7'"
            class="text-[var(--color-fg-primary)] group-hover:text-[var(--color-fg-accent)] transition-[var(--transition-fast)]"
          ></i>
        </button>
      </div>
    </div>

    <!-- @vue-ignore -->
    <Transition name="slide">
      <!-- @vue-ignore -->
      <div v-if="mobileMenuOpen && shouldCollapseNav" class="mobile-menu">
        <!-- @vue-ignore -->
        <div class="mobile-menu-backdrop" @click="closeMobileMenu"></div>
        <div class="mobile-menu-container">
          <!-- @vue-ignore -->
          <NuxtLink
            v-for="item in navItems"
            :key="item.path"
            :to="item.path"
            class="mobile-nav-link relative group"
            @click="closeMobileMenu"
          >
            <span class="relative z-10">{{ item.title }}</span>
            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-[var(--color-primary)] to-[var(--color-accent)] transition-all duration-300 group-hover:w-full"></span>
          </NuxtLink>
        </div>
      </div>
    </Transition>
    <!-- Mobile Menu -->
    <div 
      v-if="mobileMenuOpen"
      class="md:hidden absolute top-full left-0 right-0 bg-[var(--color-bg-surface)] shadow-lg py-4 z-40 border-t border-[var(--color-border-subtle)]"
    >
      <div class="container mx-auto px-4 flex flex-col space-y-3">
        <NuxtLink 
          to="/"
          @click="closeMobileMenu"
          class="block px-3 py-2 rounded-[var(--radius-md)] text-[var(--font-size-base)] font-[var(--font-weight-medium)] text-[var(--color-fg-primary)] hover:bg-[var(--color-bg-subtle)] hover:text-[var(--color-fg-accent)] transition-[var(--transition-fast)]"
          :class="{ 'text-[var(--color-fg-accent)] bg-[var(--color-bg-subtle)]': route.path === '/' }"
        >
          <i class="i-carbon-home w-6 h-6 mr-2 text-[var(--color-fg-primary)]"></i>首页
        </NuxtLink>
        <NuxtLink 
          v-for="item in navItems" 
          :key="item.path + '-mobile'" 
          :to="item.path" 
          @click="closeMobileMenu"
          class="block px-3 py-2 rounded-[var(--radius-md)] text-[var(--font-size-base)] font-[var(--font-weight-medium)] text-[var(--color-fg-primary)] hover:bg-[var(--color-bg-subtle)] hover:text-[var(--color-fg-accent)] transition-[var(--transition-fast)]"
          :class="{ 'text-[var(--color-fg-accent)] bg-[var(--color-bg-subtle)]': route.path === item.path }"
        >
          {{ item.title }}
        </NuxtLink>
        <hr class="border-[var(--color-border-subtle)] my-2">
        <a
          v-if="isLoggedIn && isAdmin"
          href="http://localhost:3003"
          target="_blank"
          @click="closeMobileMenu"
          class="block px-3 py-2 rounded-[var(--radius-md)] text-[var(--font-size-base)] font-[var(--font-weight-medium)] text-[var(--color-fg-primary)] hover:bg-[var(--color-bg-subtle)] hover:text-[var(--color-fg-accent)] transition-[var(--transition-fast)]"
        >
          <i class="i-carbon-dashboard w-6 h-6 mr-2 text-[var(--color-fg-primary)]"></i>
          管理后台
        </a>
        <NuxtLink
          :to="userProfileLinkTarget"
          @click="closeMobileMenu"
          class="block px-3 py-2 rounded-[var(--radius-md)] text-[var(--font-size-base)] font-[var(--font-weight-medium)] text-[var(--color-fg-primary)] hover:bg-[var(--color-bg-subtle)] hover:text-[var(--color-fg-accent)] transition-[var(--transition-fast)]"
        >
          <i :class="isLoggedIn ? 'i-carbon-user-avatar mr-2' : 'i-carbon-login mr-2'" class="w-6 h-6 text-[var(--color-fg-primary)]"></i>
          {{ isLoggedIn ? '用户中心' : '登录/注册' }}
        </NuxtLink>
        <button
          @click="() => { themeStore.toggleTheme(); closeMobileMenu(); }"
          class="flex items-center w-full px-3 py-2 rounded-[var(--radius-md)] text-[var(--font-size-base)] font-[var(--font-weight-medium)] text-[var(--color-fg-primary)] hover:bg-[var(--color-bg-subtle)] hover:text-[var(--color-fg-accent)] transition-[var(--transition-fast)]"
        >
          <i :class="themeStore.isDark ? 'i-carbon-moon' : 'i-carbon-sun'" class="w-6 h-6 mr-2 text-[var(--color-fg-primary)]"></i>
          {{ themeStore.isDark ? '浅色模式' : '深色模式' }}
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 70px;
  z-index: 9999;
  transition: all 0.3s ease;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.logo-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(135deg, var(--color-quantum-default), var(--color-quantum-light));
  box-shadow: var(--shadow-md);
}

.mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.mobile-menu-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: -1;
}

.mobile-menu-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 90%;
  max-width: 400px;
  padding: var(--space-xl);
  margin: var(--space-md);
  border-radius: var(--radius-lg);
  background-color: var(--color-bg-surface);
  border: 1px solid var(--color-border-subtle);
  box-shadow: var(--shadow-lg);
  z-index: 1;
}

.mobile-nav-link {
  width: 100%;
  padding: var(--space-md);
  margin: var(--space-sm) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-fg-primary);
  text-align: center;
  transition: var(--transition-fast);
  border-radius: var(--radius-md);
}

.mobile-nav-link:hover {
  background-color: var(--color-bg-elevated);
  color: var(--color-fg-accent);
}

.quantum-gradient {
  background: linear-gradient(135deg, var(--color-quantum-default), var(--color-quantum-light));
}

/* 控制台链接样式 */
.dashboard-link {
  transition: var(--transition-fast);
}

.dashboard-link:hover {
  background: var(--color-bg-elevated);
  transform: var(--animation-hover-scale);
}

.dashboard-link:active {
  transform: var(--animation-active-scale);
}

/* 语言切换按钮样式 */
.language-toggle-btn {
  transition: var(--transition-fast);
}

.language-toggle-btn:hover {
  background: var(--color-bg-elevated);
  transform: var(--animation-hover-scale);
}

.language-toggle-btn:active {
  transform: var(--animation-active-scale);
}

/* 主题切换按钮样式 */
.theme-toggle-btn {
  transition: var(--transition-fast);
}

.theme-toggle-btn:hover {
  background: var(--color-bg-elevated);
  transform: var(--animation-hover-scale);
}

.theme-toggle-btn:active {
  transform: var(--animation-active-scale);
}

/* 图标颜色 */
.theme-toggle-btn i {
  color: var(--color-fg-secondary);
  transition: var(--transition-fast);
}

.theme-toggle-btn:hover i {
  color: var(--color-fg-primary);
}

/* 过渡动画 */
.slide-enter-active,
.slide-leave-active {
  transition: var(--transition-normal);
}

.slide-enter-from,
.slide-leave-to {
  opacity: 0;
  transform: var(--animation-active-scale);
}

/* 用户菜单下拉样式 */
.user-dropdown {
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.user-menu-item {
  transition: var(--transition-fast);
}

.user-menu-item:hover {
  background: var(--color-bg-elevated);
  color: var(--color-fg-accent);
}

.user-avatar {
  box-shadow: 0 2px 8px rgba(58, 141, 255, 0.2);
}

/* 下拉菜单动画 */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: var(--transition-fast);
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px) var(--animation-active-scale);
}

.dropdown-enter-to,
.dropdown-leave-from {
  opacity: 1;
  transform: translateY(0) scale(1);
}
</style>
