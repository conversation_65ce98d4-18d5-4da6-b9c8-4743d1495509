<!-- 视觉规范：已更新 2025-05-31 -->
<script setup lang="ts">
// @ts-nocheck
// 使用 Pinia 主题状态管理
import { useThemeStore } from '~/stores/theme';
// 显式导入需要的 Vue Composition API 函数
import { onMounted, defineExpose, computed } from 'vue'
import type { ThemeType, ThemeMode, ThemeVariant } from '~/stores/theme'

// 获取主题状态
const theme = useThemeStore();
const isDark = computed(() => theme.theme === 'dark');
const themeVariant = computed(() => theme.themeVariant);

// 在客户端初始化主题
onMounted(() => {
  theme.initTheme()
})

// 暴露变量给模板使用
defineExpose({
  theme,
  isDark,
  themeVariant
});
</script>

<template>
  <!-- @vue-ignore -->
  <!-- 使用 data-theme 属性而非类名来应用主题 -->
  <div class="theme-provider">
    <slot />
  </div>
</template>

<style>
/* 主题过渡效果 - 所有变量已统一到 variables.css */

/* 应用主题过渡效果到特定元素 - 避免全局过渡导致内存问题 */
.theme-provider,
.theme-provider button,
.theme-provider .card,
.theme-provider .navbar {
  transition: background-color var(--animation-transition-duration) var(--animation-transition-timing),
              border-color var(--animation-transition-duration) var(--animation-transition-timing),
              color var(--animation-transition-duration) var(--animation-transition-timing);
}

/* 主题切换动画 */
.theme-transitioning * {
  transition: none !important;
}

/* 减少性能影响 - 移除全局will-change */
.theme-provider {
  contain: layout style;
}

/* 减少重绘 */
.theme-provider {
  contain: content;
}
</style>
