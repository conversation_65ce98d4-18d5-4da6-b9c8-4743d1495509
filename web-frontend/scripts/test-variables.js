#!/usr/bin/env node

/**
 * AR-System 变量系统测试脚本
 * 验证CSS变量是否正确定义和使用
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 颜色输出函数
const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`,
};

console.log(colors.cyan('🧪 AR-System 变量系统测试'));
console.log('='.repeat(50));

// 读取variables.css文件
const variablesPath = path.join(__dirname, '../assets/css/variables.css');
let variablesContent = '';

try {
  variablesContent = fs.readFileSync(variablesPath, 'utf8');
  console.log(colors.green('✅ variables.css 文件读取成功'));
} catch (error) {
  console.log(colors.red('❌ 无法读取 variables.css 文件'));
  process.exit(1);
}

// 测试核心变量是否存在
const coreVariables = [
  // 背景色系统
  '--color-bg-primary',
  '--color-bg-surface',
  '--color-bg-elevated',
  '--color-bg-muted',
  '--color-bg-subtle',
  '--color-bg-hover',
  
  // 前景色系统
  '--color-fg-primary',
  '--color-fg-secondary',
  '--color-fg-muted',
  '--color-fg-subtle',
  '--color-fg-accent',
  '--color-fg-highlight',
  
  // 量子主题色系统
  '--color-quantum-default',
  '--color-quantum-light',
  '--color-quantum-dark',
  
  // 统一别名系统
  '--color-primary',
  '--color-accent',
  '--color-secondary',
  '--bg-primary',
  '--fg-primary',
  '--quantum',
  '--quantum-light',
  '--quantum-dark',
];

console.log(colors.blue('\n📋 检查核心变量定义...'));
let missingVariables = [];

coreVariables.forEach(variable => {
  if (variablesContent.includes(variable)) {
    console.log(colors.green(`  ✅ ${variable}`));
  } else {
    console.log(colors.red(`  ❌ ${variable}`));
    missingVariables.push(variable);
  }
});

// 检查深色主题覆盖
console.log(colors.blue('\n🌙 检查深色主题覆盖...'));
const darkThemeCheck = variablesContent.includes('[data-theme="dark"]');
if (darkThemeCheck) {
  console.log(colors.green('  ✅ 深色主题覆盖已定义'));
} else {
  console.log(colors.red('  ❌ 深色主题覆盖缺失'));
}

// 检查主题变体
console.log(colors.blue('\n🎨 检查主题变体系统...'));
const variantCheck = variablesContent.includes('[data-theme-variant="quantum"]');
if (variantCheck) {
  console.log(colors.green('  ✅ 量子主题变体已定义'));
} else {
  console.log(colors.red('  ❌ 量子主题变体缺失'));
}

// 检查过渡动画
console.log(colors.blue('\n🎬 检查主题过渡动画...'));
const transitionCheck = variablesContent.includes('.theme-transitioning');
if (transitionCheck) {
  console.log(colors.green('  ✅ 主题过渡动画已定义'));
} else {
  console.log(colors.red('  ❌ 主题过渡动画缺失'));
}

// 检查组件中的变量使用
console.log(colors.blue('\n🔍 检查组件中的变量使用...'));
const componentsDir = path.join(__dirname, '../components');

function checkComponentVariables(dir) {
  const files = fs.readdirSync(dir);
  let issues = [];
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      issues = issues.concat(checkComponentVariables(filePath));
    } else if (file.endsWith('.vue')) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 检查是否使用了废弃的变量
      const deprecatedVariables = [
        '--quantum-particle-color-light',
        '--quantum-particle-color-dark',
        '--quantum-particle-glow-light',
        '--quantum-particle-glow-dark',
        '--quantum-text-dark',
        '--quantum-primary-dark',
      ];
      
      deprecatedVariables.forEach(variable => {
        if (content.includes(variable)) {
          issues.push({
            file: filePath.replace(componentsDir, ''),
            variable: variable,
            type: 'deprecated'
          });
        }
      });
      
      // 检查硬编码颜色值
      const hardcodedColors = content.match(/#[0-9A-Fa-f]{6}|#[0-9A-Fa-f]{3}/g);
      if (hardcodedColors) {
        hardcodedColors.forEach(color => {
          issues.push({
            file: filePath.replace(componentsDir, ''),
            variable: color,
            type: 'hardcoded'
          });
        });
      }
    }
  });
  
  return issues;
}

try {
  const componentIssues = checkComponentVariables(componentsDir);
  
  if (componentIssues.length === 0) {
    console.log(colors.green('  ✅ 组件变量使用正常'));
  } else {
    console.log(colors.yellow(`  ⚠️  发现 ${componentIssues.length} 个问题:`));
    componentIssues.forEach(issue => {
      if (issue.type === 'deprecated') {
        console.log(colors.yellow(`    📁 ${issue.file}: 使用废弃变量 ${issue.variable}`));
      } else if (issue.type === 'hardcoded') {
        console.log(colors.yellow(`    📁 ${issue.file}: 硬编码颜色 ${issue.variable}`));
      }
    });
  }
} catch (error) {
  console.log(colors.yellow('  ⚠️  无法检查组件目录'));
}

// 总结
console.log(colors.cyan('\n📊 测试总结'));
console.log('='.repeat(50));

if (missingVariables.length === 0) {
  console.log(colors.green('✅ 所有核心变量都已正确定义'));
} else {
  console.log(colors.red(`❌ 缺失 ${missingVariables.length} 个核心变量`));
}

if (darkThemeCheck && variantCheck && transitionCheck) {
  console.log(colors.green('✅ 主题系统完整'));
} else {
  console.log(colors.red('❌ 主题系统不完整'));
}

console.log(colors.blue('\n💡 建议:'));
console.log('  1. 运行 npm run dev 测试主题切换功能');
console.log('  2. 检查浏览器开发者工具中的CSS变量值');
console.log('  3. 参考迁移指南更新组件中的变量使用');
console.log('  4. 测试深浅主题切换的视觉效果');

console.log(colors.cyan('\n🎉 变量系统测试完成!'));
