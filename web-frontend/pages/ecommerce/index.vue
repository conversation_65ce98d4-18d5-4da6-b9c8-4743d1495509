<template>
  <div class="quantum-ecommerce-page">
    <!-- 🌟 震撼英雄区块 -->
    <section class="hero-quantum relative min-h-screen flex items-center justify-center overflow-hidden">
      <!-- 动态量子背景 -->
      <div class="quantum-bg absolute inset-0">
        <div class="quantum-particles"></div>
        <div class="quantum-waves"></div>
        <div class="quantum-grid"></div>
        <div class="quantum-orbs"></div>
      </div>

      <!-- 渐变遮罩 -->
      <div class="hero-overlay absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/10 to-cyan-900/20"></div>

      <!-- 主要内容 -->
      <div class="hero-content relative z-20 container mx-auto px-6 text-center">
        <!-- 顶部标签 -->
        <div class="hero-badge mb-8 inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-md rounded-full border border-white/20">
          <div class="w-2 h-2 bg-cyan-400 rounded-full mr-3 animate-pulse"></div>
          <span class="text-white font-medium">AR-System 量子商城</span>
          <div class="ml-3 px-2 py-1 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full text-xs font-bold text-black">NEW</div>
        </div>

        <!-- 主标题 -->
        <h1 class="hero-title text-6xl md:text-8xl font-black mb-6 leading-tight">
          <span class="quantum-text-gradient block">未来科技</span>
          <span class="text-white block">触手可及</span>
        </h1>

        <!-- 副标题 -->
        <p class="hero-subtitle text-xl md:text-2xl text-[var(--color-fg-secondary)] mb-12 max-w-4xl mx-auto leading-relaxed">
          探索下一代智能硬件与创新产品，体验量子美学与前沿科技的完美融合
          <br>
          <span class="quantum-text-gradient font-semibold">每一件产品，都是通往未来的钥匙</span>
        </p>

        <!-- 统计数据 -->
        <div class="hero-stats grid grid-cols-1 md:grid-cols-3 gap-8 mb-12 max-w-4xl mx-auto">
          <div class="stat-card bg-[var(--color-bg-surface)] backdrop-blur-md rounded-2xl p-6 border border-[var(--color-border-default)]">
            <div class="stat-number text-4xl font-black quantum-text-gradient mb-2">500+</div>
            <div class="stat-label text-[var(--color-fg-secondary)]">精选产品</div>
          </div>
          <div class="stat-card bg-[var(--color-bg-surface)] backdrop-blur-md rounded-2xl p-6 border border-[var(--color-border-default)]">
            <div class="stat-number text-4xl font-black quantum-text-gradient mb-2">50K+</div>
            <div class="stat-label text-[var(--color-fg-secondary)]">满意用户</div>
          </div>
          <div class="stat-card bg-[var(--color-bg-surface)] backdrop-blur-md rounded-2xl p-6 border border-[var(--color-border-default)]">
            <div class="stat-number text-4xl font-black quantum-text-gradient mb-2">99.9%</div>
            <div class="stat-label text-[var(--color-fg-secondary)]">好评率</div>
          </div>
        </div>

        <!-- 行动按钮 -->
        <div class="hero-actions flex flex-col sm:flex-row gap-6 justify-center items-center">
          <button @click="scrollToProducts" class="quantum-btn-primary group">
            <span class="relative z-10 flex items-center">
              <i class="i-carbon-rocket mr-3 text-xl"></i>
              开始探索量子世界
            </span>
            <div class="quantum-btn-glow"></div>
          </button>
          <button @click="openAuthModal" class="quantum-btn-secondary group">
            <span class="relative z-10 flex items-center">
              <i class="i-carbon-user mr-3 text-xl"></i>
              加入量子社区
            </span>
          </button>
        </div>
      </div>

      <!-- 3D产品展示 -->
      <div class="hero-product absolute right-10 top-1/2 transform -translate-y-1/2 hidden lg:block">
        <div class="product-showcase relative">
          <div class="main-product-glow absolute inset-0 bg-gradient-to-r from-[var(--color-quantum-default)]/30 to-[var(--color-quantum-light)]/30 rounded-full blur-3xl"></div>
          <div class="main-product relative z-10 w-80 h-80 bg-[var(--color-bg-surface)] rounded-3xl flex items-center justify-center border border-[var(--color-border-default)]">
            <div class="product-icon text-6xl text-[var(--color-quantum-default)]">
              <i class="i-carbon-3d-cursor"></i>
            </div>
          </div>
          <!-- 浮动小产品 -->
          <div class="floating-mini-products">
            <div class="mini-product mini-1">
              <i class="i-carbon-watch text-2xl text-[var(--color-quantum-light)]"></i>
            </div>
            <div class="mini-product mini-2">
              <i class="i-carbon-headphones text-2xl text-[var(--color-quantum-default)]"></i>
            </div>
            <div class="mini-product mini-3">
              <i class="i-carbon-mobile text-2xl text-[var(--color-quantum-dark)]"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 滚动提示 -->
      <div class="scroll-indicator absolute bottom-8 left-1/2 transform -translate-x-1/2 text-[var(--color-fg-muted)]">
        <div class="scroll-mouse mb-2"></div>
        <span class="text-sm">向下探索更多</span>
      </div>
    </section>

    <!-- 🛍️ 产品展示区块 -->
    <section id="products" class="products-section py-20 bg-[var(--color-bg-primary)]">
      <div class="container mx-auto px-6">
        <!-- 区块标题 -->
        <div class="section-header text-center mb-16">
          <div class="section-badge inline-flex items-center px-4 py-2 bg-gradient-to-r from-[var(--color-quantum-default)]/20 to-[var(--color-quantum-light)]/20 rounded-full border border-[var(--color-quantum-default)]/30 mb-6">
            <i class="i-carbon-star-filled text-[var(--color-quantum-default)] mr-2"></i>
            <span class="text-[var(--color-quantum-default)] font-medium">精选推荐</span>
          </div>
          <h2 class="text-5xl font-black mb-6">
            <span class="quantum-text-gradient">热门产品</span>
          </h2>
          <p class="text-xl text-[var(--color-fg-secondary)] max-w-3xl mx-auto">
            精心挑选的热门产品，融合前沿科技与量子美学，为您带来非凡体验
          </p>
        </div>

        <!-- 产品筛选 -->
        <div class="product-filters flex flex-wrap justify-center gap-4 mb-12">
          <button
            v-for="filter in productFilters"
            :key="filter.id"
            @click="setActiveFilter(filter.id)"
            :class="[
              'filter-btn',
              { 'active': activeFilter === filter.id }
            ]"
          >
            <i :class="filter.icon" class="mr-2"></i>
            {{ filter.name }}
          </button>
        </div>

        <!-- 产品网格 -->
        <div class="products-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          <div
            v-for="product in filteredProducts"
            :key="product.id"
            class="quantum-product-card group"
            @click="viewProduct(product)"
          >
            <!-- 产品图片 -->
            <div class="product-image-container relative overflow-hidden rounded-2xl mb-6">
              <div class="product-image-bg absolute inset-0 bg-[var(--color-bg-surface)]"></div>
              <div class="product-image relative z-10 h-64 flex items-center justify-center">
                <i :class="product.icon" class="text-6xl text-[var(--color-quantum-default)]"></i>
              </div>
              <!-- 悬浮效果 -->
              <div class="product-hover-overlay absolute inset-0 bg-gradient-to-t from-[var(--color-quantum-default)]/20 to-[var(--color-quantum-light)]/20 opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
              <!-- 产品标签 -->
              <div class="product-badges absolute top-4 left-4">
                <span v-if="product.isNew" class="badge badge-new">NEW</span>
                <span v-if="product.isHot" class="badge badge-hot">HOT</span>
              </div>
              <!-- 快速操作 -->
              <div class="quick-actions absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300">
                <button class="action-btn" @click.stop="toggleWishlist(product)">
                  <i class="i-carbon-favorite"></i>
                </button>
              </div>
            </div>

            <!-- 产品信息 -->
            <div class="product-info">
              <h3 class="product-name text-xl font-bold text-[var(--color-fg-primary)] mb-2 group-hover:text-[var(--color-quantum-default)] transition-colors">
                {{ product.name }}
              </h3>
              <p class="product-description text-[var(--color-fg-secondary)] text-sm mb-4 line-clamp-2">
                {{ product.description }}
              </p>

              <!-- 评分 -->
              <div class="product-rating flex items-center gap-2 mb-4">
                <div class="stars flex">
                  <i v-for="star in 5" :key="star" class="i-carbon-star-filled text-[var(--color-state-warning)] text-sm"></i>
                </div>
                <span class="rating-text text-xs text-[var(--color-fg-muted)]">({{ product.reviews }})</span>
              </div>

              <!-- 价格和按钮 -->
              <div class="product-footer flex items-center justify-between">
                <div class="price-info">
                  <div class="current-price text-2xl font-black quantum-text-gradient">
                    ¥{{ product.price.toLocaleString() }}
                  </div>
                  <div v-if="product.originalPrice" class="original-price text-sm text-[var(--color-fg-muted)] line-through">
                    ¥{{ product.originalPrice.toLocaleString() }}
                  </div>
                </div>
                <button @click.stop="addToCart(product)" class="add-to-cart-btn">
                  <i class="i-carbon-shopping-cart"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 页面元数据
useHead({
  title: 'AR-System 量子商城 - 探索未来科技',
  meta: [
    { name: 'description', content: 'AR-System量子商城，汇聚前沿智能硬件与创新产品。体验量子美学设计，开启您的科技之旅。' },
    { name: 'keywords', content: 'AR-System,量子商城,智能硬件,科技产品,量子美学,未来科技' }
  ]
})

// 响应式状态
const showAuthModal = ref(false)
const activeFilter = ref('all')

// 产品筛选选项
const productFilters = ref([
  { id: 'all', name: '全部产品', icon: 'i-carbon-grid' },
  { id: 'new', name: '最新上架', icon: 'i-carbon-new-tab' },
  { id: 'hot', name: '热门推荐', icon: 'i-carbon-fire' },
  { id: 'sale', name: '限时优惠', icon: 'i-carbon-tag' }
])

// 模拟产品数据
const products = ref([
  {
    id: '1',
    name: '量子智能眼镜 Pro',
    description: '下一代AR智能眼镜，支持全息投影和手势控制，开启沉浸式体验新纪元',
    price: 8999,
    originalPrice: 12999,
    icon: 'i-carbon-3d-cursor',
    reviews: 1284,
    isNew: true,
    isHot: true
  },
  {
    id: '2',
    name: '量子智能手环 X1',
    description: '革命性健康监测设备，24小时全方位生命体征监控，AI智能健康分析',
    price: 2999,
    originalPrice: 3999,
    icon: 'i-carbon-watch',
    reviews: 856,
    isNew: false,
    isHot: true
  },
  {
    id: '3',
    name: '量子无线耳机 Elite',
    description: '顶级Hi-Fi音质体验，主动降噪技术，无损音频传输，音乐发烧友首选',
    price: 1899,
    icon: 'i-carbon-headphones',
    reviews: 2341,
    isNew: true,
    isHot: false
  },
  {
    id: '4',
    name: '量子智能音箱 Home',
    description: '全屋智能控制中心，360度环绕音效，语音AI助手，打造智慧家居生活',
    price: 1299,
    originalPrice: 1799,
    icon: 'i-carbon-volume-up',
    reviews: 967,
    isNew: false,
    isHot: true
  },
  {
    id: '5',
    name: '量子充电底座 Wireless',
    description: '无线快充黑科技，支持多设备同时充电，量子美学设计，科技与艺术完美融合',
    price: 599,
    icon: 'i-carbon-battery-charging',
    reviews: 543,
    isNew: true,
    isHot: false
  },
  {
    id: '6',
    name: '量子VR头显 Vision',
    description: '沉浸式虚拟现实体验，8K超清显示，空间定位追踪，开启元宇宙新世界',
    price: 6999,
    originalPrice: 8999,
    icon: 'i-carbon-3d-mpr-toggle',
    reviews: 432,
    isNew: true,
    isHot: true
  }
])

// 计算属性
const filteredProducts = computed(() => {
  if (activeFilter.value === 'all') return products.value
  if (activeFilter.value === 'new') return products.value.filter(p => p.isNew)
  if (activeFilter.value === 'hot') return products.value.filter(p => p.isHot)
  if (activeFilter.value === 'sale') return products.value.filter(p => p.originalPrice)
  return products.value
})

// 方法
const scrollToProducts = () => {
  document.getElementById('products')?.scrollIntoView({ behavior: 'smooth' })
}

const openAuthModal = () => {
  showAuthModal.value = true
}

const closeAuthModal = () => {
  showAuthModal.value = false
}

const handleAuthSuccess = (type: string) => {
  console.log(`${type} successful`)
  closeAuthModal()
}

const setActiveFilter = (filterId: string) => {
  activeFilter.value = filterId
}

const viewProduct = (product: any) => {
  console.log('View product:', product.name)
}

const toggleWishlist = (product: any) => {
  console.log('Toggle wishlist:', product.name)
}

const addToCart = (product: any) => {
  console.log('Add to cart:', product.name)
}
</script>

<style scoped>
/* 🌟 量子电商页面 - 震撼视觉效果 */
.quantum-ecommerce-page {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  background: var(--color-bg-primary);
  min-height: 100vh;
}

/* 🎆 英雄区块 */
.hero-quantum {
  background: var(--color-bg-primary);
  position: relative;
  overflow: hidden;
}

/* 🌌 量子背景效果 - 浅色模式优化 */
.quantum-bg {
  position: absolute;
  inset: 0;
  overflow: hidden;
  /* 在浅色模式下几乎隐藏，深色模式下显示 */
  opacity: 0.1;
}

[data-theme="dark"] .quantum-bg {
  opacity: 1;
}

.quantum-particles {
  position: absolute;
  inset: 0;
  background-image:
    radial-gradient(2px 2px at 20px 30px, var(--color-quantum-default), transparent),
    radial-gradient(2px 2px at 40px 70px, var(--color-quantum-light), transparent),
    radial-gradient(1px 1px at 90px 40px, var(--color-quantum-dark), transparent),
    radial-gradient(1px 1px at 130px 80px, var(--color-quantum-light), transparent),
    radial-gradient(2px 2px at 160px 30px, var(--color-quantum-default), transparent);
  background-repeat: repeat;
  background-size: 200px 200px;
  animation: particles-float 20s ease-in-out infinite;
}

.quantum-waves {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(var(--color-quantum-default-rgb), 0.02) 50%,
    transparent 70%
  );
  animation: waves-move 15s ease-in-out infinite;
}

[data-theme="dark"] .quantum-waves {
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(var(--color-quantum-default-rgb), 0.1) 50%,
    transparent 70%
  );
}

.quantum-grid {
  position: absolute;
  inset: 0;
  background-image:
    linear-gradient(rgba(var(--color-quantum-default-rgb), 0.01) 1px, transparent 1px),
    linear-gradient(90deg, rgba(var(--color-quantum-default-rgb), 0.01) 1px, transparent 1px);
  background-size: 100px 100px;
  animation: grid-move 25s linear infinite;
}

[data-theme="dark"] .quantum-grid {
  background-image:
    linear-gradient(rgba(var(--color-quantum-default-rgb), 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(var(--color-quantum-default-rgb), 0.1) 1px, transparent 1px);
}

.quantum-orbs {
  position: absolute;
  inset: 0;
}

.quantum-orbs::before,
.quantum-orbs::after {
  content: '';
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  animation: orbs-float 20s ease-in-out infinite;
}

.quantum-orbs::before {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(var(--color-quantum-default-rgb), 0.02) 0%, transparent 70%);
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

[data-theme="dark"] .quantum-orbs::before {
  background: radial-gradient(circle, rgba(var(--color-quantum-default-rgb), 0.3) 0%, transparent 70%);
}

.quantum-orbs::after {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(var(--color-quantum-light-rgb), 0.01) 0%, transparent 70%);
  bottom: 20%;
  right: 10%;
  animation-delay: 10s;
}

[data-theme="dark"] .quantum-orbs::after {
  background: radial-gradient(circle, rgba(var(--color-quantum-light-rgb), 0.2) 0%, transparent 70%);
}

/* 🎨 量子文字渐变 */
.quantum-text-gradient {
  background: linear-gradient(135deg, var(--color-quantum-default) 0%, var(--color-quantum-light) 50%, var(--color-quantum-dark) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 3s ease-in-out infinite;
}

/* 🚀 量子按钮 */
.quantum-btn-primary {
  position: relative;
  padding: 16px 32px;
  background: linear-gradient(135deg, var(--color-quantum-default) 0%, var(--color-quantum-light) 100%);
  border: none;
  border-radius: 16px;
  color: var(--color-fg-primary);
  font-weight: 700;
  font-size: 18px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(var(--color-quantum-default-rgb), 0.3);
}

.quantum-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(var(--color-quantum-default-rgb), 0.4);
}

.quantum-btn-glow {
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.quantum-btn-primary:hover .quantum-btn-glow {
  transform: translateX(100%);
}

.quantum-btn-secondary {
  position: relative;
  padding: 16px 32px;
  background: transparent;
  border: 2px solid var(--color-quantum-default);
  border-radius: 16px;
  color: var(--color-quantum-default);
  font-weight: 700;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.quantum-btn-secondary:hover {
  background: rgba(var(--color-quantum-default-rgb), 0.1);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(var(--color-quantum-default-rgb), 0.2);
}

/* 📊 统计卡片 */
.stat-card {
  transition: all 0.3s ease;
  backdrop-filter: blur(20px);
  background: var(--color-bg-surface);
  border: 1px solid var(--color-border-default);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(var(--color-quantum-default-rgb), 0.2);
}

/* 🎯 3D产品展示 */
.product-showcase {
  animation: showcase-float 6s ease-in-out infinite;
}

.main-product {
  transition: all 0.3s ease;
  backdrop-filter: blur(20px);
}

.main-product:hover {
  transform: scale(1.05);
}

.floating-mini-products {
  position: absolute;
  inset: 0;
}

.mini-product {
  position: absolute;
  width: 60px;
  height: 60px;
  background: var(--color-bg-surface);
  backdrop-filter: blur(10px);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--color-border-default);
  animation: mini-float 4s ease-in-out infinite;
}

.mini-1 {
  top: 20%;
  left: -20%;
  animation-delay: 0s;
}

.mini-2 {
  bottom: 30%;
  right: -30%;
  animation-delay: 1.5s;
}

.mini-3 {
  top: 60%;
  left: -30%;
  animation-delay: 3s;
}

/* 🖱️ 滚动指示器 */
.scroll-mouse {
  width: 24px;
  height: 40px;
  border: 2px solid var(--color-fg-muted);
  border-radius: 12px;
  position: relative;
  margin: 0 auto;
}

.scroll-mouse::before {
  content: '';
  position: absolute;
  width: 4px;
  height: 8px;
  background: var(--color-fg-muted);
  border-radius: 2px;
  top: 6px;
  left: 50%;
  transform: translateX(-50%);
  animation: scroll-wheel 2s ease-in-out infinite;
}

/* 🏷️ 产品筛选按钮 */
.filter-btn {
  padding: 8px 16px;
  background: var(--color-bg-surface);
  border: 1px solid var(--color-border-default);
  border-radius: 8px;
  color: var(--color-fg-secondary);
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.filter-btn:hover,
.filter-btn.active {
  background: linear-gradient(135deg, var(--color-quantum-default) 0%, var(--color-quantum-light) 100%);
  color: var(--color-fg-primary);
  border-color: transparent;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--color-quantum-default-rgb), 0.2);
}

/* 🛍️ 产品卡片 */
.quantum-product-card {
  background: var(--color-bg-surface);
  border: 1px solid var(--color-border-default);
  border-radius: 20px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.4s ease;
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.quantum-product-card::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(var(--color-quantum-default-rgb), 0.1) 0%, rgba(var(--color-quantum-light-rgb), 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.quantum-product-card:hover::before {
  opacity: 1;
}

.quantum-product-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 30px 60px rgba(var(--color-quantum-default-rgb), 0.2);
  border-color: rgba(var(--color-quantum-default-rgb), 0.3);
}

.product-image-container {
  transition: all 0.3s ease;
}

.quantum-product-card:hover .product-image-container {
  transform: scale(1.05);
}

/* 🏷️ 产品标签 */
.badge {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  line-height: 1;
}

.badge-new {
  background: linear-gradient(135deg, var(--color-state-success) 0%, #059669 100%);
  color: white;
}

.badge-hot {
  background: linear-gradient(135deg, var(--color-state-warning) 0%, #d97706 100%);
  color: white;
}

/* 🎯 快速操作按钮 */
.action-btn {
  width: 40px;
  height: 40px;
  background: var(--color-bg-surface);
  border: 1px solid var(--color-border-default);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-fg-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.action-btn:hover {
  background: linear-gradient(135deg, var(--color-quantum-default) 0%, var(--color-quantum-light) 100%);
  color: var(--color-fg-primary);
  transform: scale(1.1);
}

/* 🛒 购物车按钮 */
.add-to-cart-btn {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--color-quantum-default) 0%, var(--color-quantum-light) 100%);
  border: none;
  border-radius: 50%;
  color: var(--color-fg-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.add-to-cart-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 10px 20px rgba(var(--color-quantum-default-rgb), 0.4);
}

/* 📱 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 3rem;
  }

  .hero-subtitle {
    font-size: 1.125rem;
  }

  .hero-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .hero-actions {
    flex-direction: column;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .product-filters {
    flex-direction: column;
    align-items: center;
  }
}

/* 🎬 动画定义 */
@keyframes particles-float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(120deg); }
  66% { transform: translateY(10px) rotate(240deg); }
}

@keyframes waves-move {
  0%, 100% { transform: translateX(-100%) skewX(-15deg); }
  50% { transform: translateX(100%) skewX(15deg); }
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(100px, 100px); }
}

@keyframes orbs-float {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-30px) scale(1.1); }
}

@keyframes gradient-shift {
  0%, 100% { filter: hue-rotate(0deg); }
  50% { filter: hue-rotate(90deg); }
}

@keyframes showcase-float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

@keyframes mini-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
}

@keyframes scroll-wheel {
  0% { transform: translateX(-50%) translateY(0); opacity: 1; }
  100% { transform: translateX(-50%) translateY(16px); opacity: 0; }
}

/* 🎨 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>