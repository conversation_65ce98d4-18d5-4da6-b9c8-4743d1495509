<template>
  <div class="quantum-auth-page">
    <!-- 🌌 量子背景 -->
    <div class="quantum-bg absolute inset-0">
      <div class="quantum-particles"></div>
      <div class="quantum-waves"></div>
      <div class="quantum-grid"></div>
      <div class="quantum-orbs"></div>
    </div>

    <!-- 🎯 主要内容 -->
    <div class="auth-container relative z-10 min-h-screen flex items-center justify-center p-6">
      <div class="auth-card w-full max-w-md">
        <!-- 🌟 Logo区域 -->
        <div class="logo-section text-center mb-12">
          <div class="quantum-logo w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-cyan-400 to-purple-500 rounded-3xl flex items-center justify-center relative overflow-hidden">
            <div class="logo-glow absolute inset-0 bg-gradient-to-br from-cyan-400/50 to-purple-500/50 blur-xl"></div>
            <i class="i-carbon-3d-cursor text-4xl text-white relative z-10"></i>
          </div>
          <h1 class="text-4xl font-black mb-3" style="color: var(--color-fg-primary);">
            欢迎回来
          </h1>
          <p class="text-lg" style="color: var(--color-fg-muted);">
            登录您的量子账户，继续探索未来科技
          </p>
        </div>

        <!-- 🔐 登录表单 -->
        <form @submit.prevent="handleLogin" class="space-y-6">
          <!-- 邮箱输入 -->
          <div class="form-group">
            <label class="form-label">邮箱地址</label>
            <div class="input-wrapper">
              <i class="i-carbon-email input-icon"></i>
              <input
                v-model="loginForm.email"
                type="email"
                class="quantum-input"
                placeholder="请输入您的邮箱"
                required
              >
            </div>
          </div>

          <!-- 密码输入 -->
          <div class="form-group">
            <label class="form-label">密码</label>
            <div class="input-wrapper">
              <i class="i-carbon-password input-icon"></i>
              <input
                v-model="loginForm.password"
                :type="showPassword ? 'text' : 'password'"
                class="quantum-input"
                placeholder="请输入您的密码"
                required
              >
              <button
                type="button"
                @click="showPassword = !showPassword"
                class="password-toggle"
              >
                <i :class="showPassword ? 'i-carbon-view-off' : 'i-carbon-view'"></i>
              </button>
            </div>
          </div>

          <!-- 记住我和忘记密码 -->
          <div class="form-options flex items-center justify-between">
            <label class="checkbox-wrapper">
              <input v-model="loginForm.rememberMe" type="checkbox" class="quantum-checkbox">
              <span class="checkbox-label">记住我</span>
            </label>
            <NuxtLink to="/auth/forgot-password" class="forgot-password-link">
              忘记密码？
            </NuxtLink>
          </div>

          <!-- 登录按钮 -->
          <button type="submit" :disabled="isLoading" class="quantum-btn-auth">
            <div v-if="isLoading" class="loading-spinner"></div>
            <i v-else class="i-carbon-login mr-3"></i>
            {{ isLoading ? '登录中...' : '登录' }}
            <div class="btn-glow"></div>
          </button>
        </form>

        <!-- 🔗 分隔线 -->
        <div class="divider my-8">
          <span class="divider-text">或</span>
        </div>

        <!-- 🌐 第三方登录 -->
        <div class="social-auth space-y-4">
          <button @click="handleSocialLogin('google')" class="social-btn social-btn-google">
            <i class="i-carbon-logo-google mr-3"></i>
            使用 Google 继续
          </button>
          <button @click="handleSocialLogin('github')" class="social-btn social-btn-github">
            <i class="i-carbon-logo-github mr-3"></i>
            使用 GitHub 继续
          </button>
        </div>

        <!-- 🔄 注册链接 -->
        <div class="register-link text-center mt-8">
          <span style="color: var(--color-fg-muted);">还没有账户？</span>
          <NuxtLink to="/auth/register" class="register-btn">
            立即注册
          </NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 页面元数据
useHead({
  title: 'AR-System 登录 - 探索量子科技',
  meta: [
    { name: 'description', content: '登录AR-System账户，体验量子美学设计，探索前沿科技产品。' }
  ]
})

// 响应式状态
const isLoading = ref(false)
const showPassword = ref(false)

// 表单数据
const loginForm = ref({
  email: '',
  password: '',
  rememberMe: false
})

// 方法
const handleLogin = async () => {
  isLoading.value = true
  try {
    const authStore = useAuthStore()

    // 直接使用authStore的登录方法，它会自动调用API并处理角色信息
    await authStore.login({
      email: loginForm.value.email,
      password: loginForm.value.password
    })

    console.log('Login successful, navigating to home...')
    await navigateTo('/')
  } catch (error) {
    console.error('登录失败:', error)
    alert('登录失败：' + (error.data?.detail || error.message || '请检查您的凭据'))
  } finally {
    isLoading.value = false
  }
}

const handleSocialLogin = (provider: string) => {
  console.log(`使用 ${provider} 登录`)
  // 这里实现第三方登录逻辑
}
</script>

<style scoped>
/* 🌟 量子登录页面 - 震撼视觉效果 */
.quantum-auth-page {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  background: var(--color-bg-primary);
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

/* 🌌 量子背景效果 - 浅色模式优化 */
.quantum-bg {
  position: absolute;
  inset: 0;
  overflow: hidden;
  /* 在浅色模式下几乎隐藏 */
  opacity: 0.05;
}

[data-theme="dark"] .quantum-bg {
  opacity: 1;
}

.quantum-particles {
  position: absolute;
  inset: 0;
  background-image:
    radial-gradient(2px 2px at 20px 30px, var(--color-quantum-default), transparent),
    radial-gradient(2px 2px at 40px 70px, var(--color-quantum-light), transparent),
    radial-gradient(1px 1px at 90px 40px, var(--color-quantum-dark), transparent),
    radial-gradient(1px 1px at 130px 80px, var(--color-quantum-light), transparent),
    radial-gradient(2px 2px at 160px 30px, var(--color-quantum-default), transparent);
  background-repeat: repeat;
  background-size: 200px 200px;
  animation: particles-float 20s ease-in-out infinite;
}

.quantum-waves {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(var(--color-quantum-default-rgb), 0.01) 50%,
    transparent 70%
  );
  animation: waves-move 15s ease-in-out infinite;
}

[data-theme="dark"] .quantum-waves {
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(var(--color-quantum-default-rgb), 0.1) 50%,
    transparent 70%
  );
}

.quantum-grid {
  position: absolute;
  inset: 0;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
  background-size: 100px 100px;
  animation: grid-move 25s linear infinite;
}

.quantum-orbs {
  position: absolute;
  inset: 0;
}

.quantum-orbs::before,
.quantum-orbs::after {
  content: '';
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  animation: orbs-float 20s ease-in-out infinite;
}

.quantum-orbs::before {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(0, 212, 255, 0.3) 0%, transparent 70%);
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.quantum-orbs::after {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(168, 85, 247, 0.2) 0%, transparent 70%);
  bottom: 20%;
  right: 10%;
  animation-delay: 10s;
}

/* 🎨 认证卡片 */
.auth-card {
  background: var(--color-bg-surface);
  backdrop-filter: blur(20px);
  border: 1px solid var(--color-border-default);
  border-radius: 24px;
  padding: 48px;
  box-shadow: 0 25px 50px rgba(var(--color-fg-primary-rgb), 0.1);
}

/* 🌟 Logo设计 */
.quantum-logo {
  animation: logo-glow 2s ease-in-out infinite alternate;
  position: relative;
}

.logo-glow {
  animation: glow-pulse 2s ease-in-out infinite alternate;
}

/* 📝 表单样式 */
.form-group {
  position: relative;
}

.form-label {
  display: block;
  color: var(--color-fg-primary);
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
}

.input-wrapper {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
  font-size: 18px;
  z-index: 10;
}

.quantum-input {
  width: 100%;
  padding: 16px 16px 16px 48px;
  background: var(--color-bg-surface);
  border: 1px solid var(--color-border-default);
  border-radius: 16px;
  color: var(--color-fg-primary);
  font-size: 16px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.quantum-input::placeholder {
  color: var(--color-fg-muted);
}

.quantum-input:focus {
  outline: none;
  border-color: var(--color-quantum-default);
  background: var(--color-bg-elevated);
  box-shadow: 0 0 0 3px rgba(var(--color-quantum-default-rgb), 0.1);
}

.password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
  background: none;
  border: none;
  cursor: pointer;
  transition: color 0.3s ease;
  font-size: 18px;
}

.password-toggle:hover {
  color: #00d4ff;
}

/* ✅ 复选框样式 */
.checkbox-wrapper {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.quantum-checkbox {
  width: 18px;
  height: 18px;
  margin-right: 12px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  appearance: none;
  position: relative;
}

.quantum-checkbox:checked {
  background: linear-gradient(135deg, #00d4ff, #a855f7);
  border-color: #00d4ff;
}

.quantum-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.checkbox-label {
  color: var(--color-fg-primary);
  font-size: 14px;
}

/* 🚀 量子按钮 */
.quantum-btn-auth {
  width: 100%;
  padding: 16px 24px;
  background: linear-gradient(135deg, #00d4ff 0%, #a855f7 100%);
  border: none;
  border-radius: 16px;
  color: white;
  font-weight: 700;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
}

.quantum-btn-auth:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(0, 212, 255, 0.4);
}

.quantum-btn-auth:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-glow {
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.quantum-btn-auth:hover .btn-glow {
  transform: translateX(100%);
}

/* 🔗 社交登录按钮 */
.social-btn {
  width: 100%;
  padding: 14px 20px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--color-border-default);
  backdrop-filter: blur(10px);
}

.social-btn-google {
  background: var(--color-bg-surface);
  color: var(--color-fg-primary);
}

.social-btn-google:hover {
  background: var(--color-bg-elevated);
  border-color: var(--color-border-emphasis);
}

.social-btn-github {
  background: var(--color-bg-surface);
  color: var(--color-fg-primary);
}

.social-btn-github:hover {
  background: var(--color-bg-elevated);
  border-color: var(--color-border-emphasis);
}

/* 📏 分隔线 */
.divider {
  position: relative;
  text-align: center;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
}

.divider-text {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  padding: 0 16px;
  color: #64748b;
  font-size: 14px;
}

/* 🔗 链接样式 */
.forgot-password-link,
.register-btn {
  color: #00d4ff;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.forgot-password-link:hover,
.register-btn:hover {
  color: #a855f7;
}

.register-btn {
  margin-left: 8px;
}

/* 🔄 加载动画 */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 📱 响应式设计 */
@media (max-width: 640px) {
  .auth-card {
    padding: 32px 24px;
    margin: 1rem;
  }
}

/* 🎬 动画定义 */
@keyframes particles-float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(120deg); }
  66% { transform: translateY(10px) rotate(240deg); }
}

@keyframes waves-move {
  0%, 100% { transform: translateX(-100%) skewX(-15deg); }
  50% { transform: translateX(100%) skewX(15deg); }
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(100px, 100px); }
}

@keyframes orbs-float {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-30px) scale(1.1); }
}

@keyframes logo-glow {
  0% { box-shadow: 0 0 20px rgba(0, 212, 255, 0.3); }
  100% { box-shadow: 0 0 40px rgba(168, 85, 247, 0.5); }
}

@keyframes glow-pulse {
  0% { opacity: 0.3; transform: scale(1); }
  100% { opacity: 0.6; transform: scale(1.1); }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>
