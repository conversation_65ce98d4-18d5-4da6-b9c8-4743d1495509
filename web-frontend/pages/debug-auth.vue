<template>
  <div class="debug-auth-page p-8">
    <h1 class="text-2xl font-bold mb-6">🔍 认证状态调试页面</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- 当前用户状态 -->
      <div class="bg-[var(--color-bg-elevated)] p-6 rounded-lg">
        <h2 class="text-lg font-semibold mb-4">👤 当前用户状态</h2>
        <div class="space-y-2 text-sm">
          <div><strong>登录状态:</strong> {{ isLoggedIn ? '✅ 已登录' : '❌ 未登录' }}</div>
          <div><strong>用户ID:</strong> {{ currentUser?.id || 'N/A' }}</div>
          <div><strong>用户名:</strong> {{ currentUser?.username || 'N/A' }}</div>
          <div><strong>邮箱:</strong> {{ currentUser?.email || 'N/A' }}</div>
          <div><strong>显示名:</strong> {{ currentUser?.displayName || 'N/A' }}</div>
          <div><strong>is_superuser:</strong> {{ currentUser?.is_superuser ? '✅ 是' : '❌ 否' }}</div>
          <div><strong>角色名:</strong> {{ currentUser?.role?.name || 'N/A' }}</div>
          <div><strong>角色显示名:</strong> {{ currentUser?.role?.displayName || 'N/A' }}</div>
          <div><strong>角色级别:</strong> {{ currentUser?.role?.level || 'N/A' }}</div>
          <div><strong>是否管理员:</strong> {{ isAdmin ? '✅ 是' : '❌ 否' }}</div>
        </div>
      </div>

      <!-- localStorage 数据 -->
      <div class="bg-[var(--color-bg-elevated)] p-6 rounded-lg">
        <h2 class="text-lg font-semibold mb-4">💾 localStorage 数据</h2>
        <div class="space-y-4">
          <div>
            <h3 class="font-medium">web-frontend 数据:</h3>
            <div class="text-xs bg-gray-100 p-2 rounded mt-1">
              <div><strong>auth-token:</strong> {{ webToken ? '存在' : '不存在' }}</div>
              <div><strong>user-data:</strong> {{ webUserData ? '存在' : '不存在' }}</div>
            </div>
          </div>
          
          <div>
            <h3 class="font-medium">dashboard 数据:</h3>
            <div class="text-xs bg-gray-100 p-2 rounded mt-1">
              <div><strong>auth_token:</strong> {{ dashToken ? '存在' : '不存在' }}</div>
              <div><strong>user_data:</strong> {{ dashUserData ? '存在' : '不存在' }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 详细用户数据 -->
      <div class="bg-[var(--color-bg-elevated)] p-6 rounded-lg md:col-span-2">
        <h2 class="text-lg font-semibold mb-4">📋 详细用户数据</h2>
        <pre class="text-xs bg-gray-100 p-4 rounded overflow-auto">{{ JSON.stringify(currentUser, null, 2) }}</pre>
      </div>

      <!-- Dashboard 用户数据 -->
      <div class="bg-[var(--color-bg-elevated)] p-6 rounded-lg md:col-span-2">
        <h2 class="text-lg font-semibold mb-4">🎛️ Dashboard 用户数据</h2>
        <pre class="text-xs bg-gray-100 p-4 rounded overflow-auto">{{ dashUserDataParsed ? JSON.stringify(dashUserDataParsed, null, 2) : '无数据' }}</pre>
      </div>

      <!-- 操作按钮 -->
      <div class="bg-[var(--color-bg-elevated)] p-6 rounded-lg md:col-span-2">
        <h2 class="text-lg font-semibold mb-4">🔧 调试操作</h2>
        <div class="flex flex-wrap gap-4">
          <button @click="refreshAuth" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
            刷新认证状态
          </button>
          <button @click="syncFromDashboard" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
            从Dashboard同步
          </button>
          <button @click="clearAuth" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
            清除认证数据
          </button>
          <button @click="testAPI" class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
            测试API调用
          </button>
        </div>
        
        <div v-if="apiResult" class="mt-4 p-4 bg-gray-100 rounded">
          <h3 class="font-medium mb-2">API 调用结果:</h3>
          <pre class="text-xs">{{ apiResult }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '~/stores/authStore'

const authStore = useAuthStore()

// 计算属性
const isLoggedIn = computed(() => authStore.isLoggedIn)
const currentUser = computed(() => authStore.currentUser)
const isAdmin = computed(() => {
  const user = authStore.currentUser
  return user && (user.role?.name === 'admin' || user.is_superuser)
})

// localStorage 数据
const webToken = ref('')
const webUserData = ref('')
const dashToken = ref('')
const dashUserData = ref('')
const dashUserDataParsed = ref(null)
const apiResult = ref('')

// 加载 localStorage 数据
const loadLocalStorageData = () => {
  if (typeof window !== 'undefined') {
    webToken.value = localStorage.getItem('auth-token') || ''
    webUserData.value = localStorage.getItem('user-data') || ''
    dashToken.value = localStorage.getItem('auth_token') || ''
    dashUserData.value = localStorage.getItem('user_data') || ''
    
    if (dashUserData.value) {
      try {
        dashUserDataParsed.value = JSON.parse(dashUserData.value)
      } catch (e) {
        dashUserDataParsed.value = null
      }
    }
  }
}

// 刷新认证状态
const refreshAuth = async () => {
  try {
    await authStore.refreshUserInfo()
    loadLocalStorageData()
    console.log('认证状态已刷新')
  } catch (error) {
    console.error('刷新认证状态失败:', error)
  }
}

// 从Dashboard同步
const syncFromDashboard = async () => {
  try {
    const dashUserData = localStorage.getItem('user_data')
    const dashToken = localStorage.getItem('auth_token')
    
    if (dashUserData && dashToken) {
      const dashUser = JSON.parse(dashUserData)
      await authStore.syncFromDashboard(dashUser, dashToken)
      loadLocalStorageData()
      console.log('从Dashboard同步成功')
    } else {
      console.log('没有Dashboard数据可同步')
    }
  } catch (error) {
    console.error('从Dashboard同步失败:', error)
  }
}

// 清除认证数据
const clearAuth = () => {
  authStore.logout()
  loadLocalStorageData()
  console.log('认证数据已清除')
}

// 测试API调用
const testAPI = async () => {
  try {
    const token = localStorage.getItem('auth-token') || localStorage.getItem('auth_token')
    if (!token) {
      apiResult.value = '错误: 没有找到认证token'
      return
    }

    const response = await fetch('/api/v1/auth/me', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const userData = await response.json()
      apiResult.value = `成功: ${JSON.stringify(userData, null, 2)}`
    } else {
      apiResult.value = `失败: ${response.status} ${response.statusText}`
    }
  } catch (error) {
    apiResult.value = `错误: ${error.message}`
  }
}

onMounted(() => {
  loadLocalStorageData()
})
</script>

<style scoped>
.debug-auth-page {
  min-height: 100vh;
  background: var(--color-bg-default);
  color: var(--color-fg-primary);
}
</style>
