// https://nuxt.com/docs/api/configuration/nuxt-config
import { presetUno, presetIcons, presetTypography } from 'unocss'
import { transformerDirectives, transformerVariantGroup } from 'unocss'

export default defineNuxtConfig({
  compatibilityDate: '2024-04-03',
  ssr: false,

  typescript: {
    strict: true,
    tsConfig: {
      compilerOptions: {
        strict: true,
        noImplicitAny: true,
        strictNullChecks: true,
        strictFunctionTypes: true,
        strictBindCallApply: true,
        strictPropertyInitialization: true,
        noImplicitThis: true,
        alwaysStrict: true,
      },
    },
  },

  devtools: { enabled: true },

  css: [
    '@unocss/reset/tailwind.css',
    '~/assets/css/variables.css', // 统一变量系统 - 必须最先加载
    '~/assets/css/fonts.css',
    '~/assets/css/global.css',
    '~/assets/css/animations.css',
    '~/assets/css/shared/quantum-effects.css',
    '~/assets/css/shared/glassmorphism.css',
    '~/assets/css/shared/spatial-effects.css',
    '~/assets/css/icon-enhancement.css',
    // '~/assets/css/theme-mapping.css', // 已废弃 - 变量已合并到 variables.css
  ],

  modules: [
    '@unocss/nuxt',
    '@nuxtjs/color-mode',
    '@pinia/nuxt',
    '@tresjs/nuxt',
    '@vueuse/nuxt',
    '@vueuse/motion/nuxt',
  ],

  unocss: {
    presets: [
      presetUno(),
      presetIcons({
        scale: 1.2,
        warn: true,
        collections: {
          carbon: true,
          ph: true, // Phosphor icons
          // Add more icon collections if needed
        },
        extraProperties: {
          'display': 'inline-block',
          'vertical-align': 'middle',
        },
      }),
      presetTypography(),
    ],
    transformers: [
      transformerDirectives({ enforce: 'pre' }),
      transformerVariantGroup(),
    ],
    theme: {
      // 使用CSS变量，不需要JS对象
    },
    safelist: [
      // Carbon icons - 基础图标
      'i-carbon-menu',
      'i-carbon-close',
      'i-carbon-sun',
      'i-carbon-moon',
      'i-carbon-earth-americas-filled',
      'i-carbon-search',
      'i-carbon-user-avatar',
      'i-carbon-login',
      // Carbon icons - 主页使用的图标
      'i-carbon-open-panel-filled-top',
      'i-carbon-chip',
      'i-carbon-developer',
      'i-carbon-diagram',
      'i-carbon-sensor',
      'i-carbon-screen',
      'i-carbon-volume-up',
      'i-carbon-battery-full',
      'i-carbon-view',
      'i-carbon-cognitive',
      'i-carbon-network-3',
      'i-carbon-3d-cursor',
      'i-carbon-brain',
      'i-carbon-cloud',
      'i-carbon-document',
      'i-carbon-logo-github',
      'i-carbon-user-multiple',
      // Phosphor icons
      'i-ph-brain-bold',
      'i-ph-cube-transparent-bold',
      'i-ph-activity-bold',
      'i-ph-lightning-bold',
      'i-ph-shield-bold',
      'i-ph-rocket-bold'
    ],
  },

  colorMode: {
    preference: 'system',
    fallback: 'light',
    classSuffix: '',
  },

  tres: {
    glsl: true,
    ssr: false,
  },

  app: {
    head: {
      title: 'AR-System',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'description', content: 'AR System - 量子星核' },
      ],
      link: [{ rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }],
    },
  },

  performance: {
    images: {
      lazyLoad: true,
    },
  },

  nitro: {
    preset: 'node-server',
    routeRules: {
      // '/api/**': { proxy: process.env.API_BASE_URL || 'http://localhost:8000' },
    },
    experimental: {
      wasm: true
    }
  },

  build: {
    transpile: ['three', '@vueuse/motion', '@vueuse/core'],
  },

  vite: {
    server: {
      watch: {
        // 排除 node_modules 目录下的深层依赖，减少文件监视
        ignored: [
          "**/node_modules/@unocss/**",
          "**/node_modules/@typescript-eslint/**"
        ]
      }
    }
  },
})
